const cheerio = require("cheerio");
const { getOrFetchHtml } = require("../../../utils/fileCache");

/**
 * Discover important pages on a domain
 * @param {leadUrl} url - getting url object from the
 * @returns {Object} - Object containing discovered URLs
 */
async function scrapePages(originalUrl, domainUrl) {
  const result = {
    originalUrl,
    domainUrl,
    home: [],
    about: [],
    privacy: [],
    contact: [],
    terms: [],
  };

  try {
    console.log("Scraping pages for URL:", originalUrl);
    // Navigate to home page
    const homeUrl = result.domainUrl;
    result.home.push(homeUrl);

    // Use the existing getOrFetchHtml function to get HTML content
    // console.log("Fetching HTML content for:", homeUrl);
    const htmlContent = await getOrFetchHtml(homeUrl);

    if (!htmlContent) {
      console.error("Failed to fetch HTML content for:", homeUrl);
      throw new Error("Empty HTML content received");
    }

    // console.log("HTML Content length:", htmlContent.length);

    // Parse HTML with Cheerio
    const $ = cheerio.load(htmlContent);
    console.log("Cheerio loaded successfully");

    // Extract links using Cheerio
    const links = [];
    const linkElements = $("a");
    // console.log("Found link elements:", linkElements.length);

    linkElements.each((i, element) => {
      const href = $(element).attr("href");
      const text = $(element).text().toLowerCase().trim();

      if (href) {
        // Resolve relative URLs
        // console.log("Processing href:", href);
        let fullUrl = href;
        if (href.startsWith("/")) {
          fullUrl = new URL(href, homeUrl).href;
        } else if (!href.startsWith("http")) {
          try {
            fullUrl = new URL(href, homeUrl).href;
          } catch (error) {
            console.error("Invalid URL:", href, error);
            return;
          }
        }

        links.push({
          href: fullUrl,
          text: text,
        });
      }
    });

    console.log(`Found ${links.length} valid links on home page`);
    // Process links to identify important pages
    for (const link of links) {
      // Skip external links
      if (!link.href.includes(result.domainUrl)) {
        continue;
      }

      // About page detection
      if (
        link.href.includes("/about") ||
        link.text.includes("about") ||
        link.text.includes("about us")
      ) {
        result.about.push(link.href);
      }

      // Privacy page detection
      if (
        link.href.includes("/privacy") ||
        link.href.includes("/privacy-policy") ||
        link.text.includes("privacy")
      ) {
        result.privacy.push(link.href);
      }

      // Contact page detection
      if (
        link.href.includes("/contact") ||
        link.href.includes("/contact-us") ||
        link.text.includes("contact")
      ) {
        result.contact.push(link.href);
      }
      // terms page detection
      if (
        link.href.includes("/terms") ||
        link.href.includes("/terms-of-service") ||
        link.text.includes("terms")
      ) {
        result.terms.push(link.href);
      }
    }

    // Remove duplicates and limit to top 2 candidates per category
    Object.keys(result).forEach((key) => {
      if (Array.isArray(result[key])) {
        // Remove duplicates
        result[key] = [...new Set(result[key])];
        // Limit to top 2
        if (result[key].length > 2) {
          result[key] = result[key].slice(0, 2);
        }
      }
    });

    return result;
  } catch (error) {
    console.error(`Error discovering pages: ${error.message}`);
    console.error("Stack trace:", error.stack);
    // Add the domain as home page if everything fails
    result.home = [result.domainUrl];
    return result;
  }
}

async function main() {
  const { extractDomain } = require("../../../utils/domainHelper");
  const url = "https://sagabeverage.no/";
  const domainValue = extractDomain(url);
  const domainUrl = url.split(domainValue)[0] + domainValue;

  // Call scrapePages with the URL and domain
  const result = await scrapePages(url, domainUrl);

  console.log("Page Discovery Results:", result);
}

if (require.main === module) {
  main();
}
module.exports = { scrapePages };
