const fs = require("fs");
const csvtojson = require("csvtojson");
const csvQueueManager = require("../../utils/csvQueueManager");
const { processCSV: originalProcessCSV } = require("../../middlewares/processCsv");
const prisma = require("../../database/prisma/getPrismaClient");

/**
 * CSV Processor - Handles processing of queued CSV files
 */

/**
 * Process a queued CSV file
 * @param {Object} csvData - CSV data record from queue
 * @returns {Object} Processing result
 */
async function processQueuedCSV(csvData) {
  try {
    console.log(`Starting to process CSV: ${csvData.file_name} (Type: ${csvData.process_type})`);

    // Validate file exists
    if (!fs.existsSync(csvData.file_path)) {
      throw new Error(`CSV file not found: ${csvData.file_path}`);
    }

    // Get processing options from csvData
    const options = csvData.options || {};
    const skipErrorRows = options.skipErrorRows !== undefined ? options.skipErrorRows : true;
    const allowBlankRows = options.allowBlankRows || false;

    // Process the entire CSV file at once using the original processCSV function
    console.log(`Processing entire CSV file: ${csvData.file_name}`);
    
    const result = await originalProcessCSV(
      csvData.file_path,
      csvData.process_type,
      "insert", // Default operation
      skipErrorRows,
      allowBlankRows
    );

    // Check if there were any errors from processCSV
    let hasErrors = false;
    let lastError = null;
    
    if (result && result.length > 0) {
      // There were errors from processCSV, store them in the database
      console.log(`CSV processing completed with ${result.length} errors`);
      hasErrors = true;
      
      // Store each error in the database with row information
      await storeProcessCsvErrors(csvData.id, result);
      
      lastError = result[0]?.error_message || result[0] || "Processing completed with errors";
      
      // Mark all rows as processed but with errors
      await csvQueueManager.markAllRowsProcessed(csvData.id, false);
    } else {
      // No errors, mark all rows as processed successfully
      console.log(`CSV processing completed successfully`);
      await csvQueueManager.markAllRowsProcessed(csvData.id, true);
    }

    // Final statistics
    const finalStats = await csvQueueManager.getProcessingStats(csvData.id);
    
    console.log(`Completed processing CSV: ${csvData.file_name}`);
    console.log(`  Total rows: ${finalStats.totalRows}`);
    console.log(`  Processed: ${finalStats.processedRows}`);
    console.log(`  Errors: ${finalStats.errorRows}`);
    console.log(`  Success rate: ${(((finalStats.processedRows - finalStats.errorRows) / finalStats.totalRows) * 100).toFixed(1)}%`);

    return {
      success: true,
      message: "CSV processed successfully",
      stats: finalStats,
      hasErrors,
      lastError,
    };

  } catch (error) {
    console.error(`Error processing queued CSV:`, error);
    return {
      success: false,
      error: error.message,
      message: "Failed to process CSV",
    };
  }
}

/**
 * Process a single row of data
 * @param {Object} rowData - Single row data
 * @param {string} type - Processing type
 * @param {string} operation - Operation type (insert/update)
 * @param {boolean} skipErrorRows - Whether to skip error rows
 * @param {boolean} allowBlankRows - Whether to allow blank rows
 * @returns {Object} Processing result
 */
async function processSingleRow(rowData, type, operation = "insert", skipErrorRows = true, allowBlankRows = false) {
  try {
    // Create a temporary CSV file with just this row for processing
    const tempCsvPath = await createTempCSVFile([rowData]);
    
    try {
      // Use the original processCSV function
      const result = await originalProcessCSV(
        tempCsvPath,
        type,
        operation,
        skipErrorRows,
        allowBlankRows
      );

      // Clean up temp file
      if (fs.existsSync(tempCsvPath)) {
        fs.unlinkSync(tempCsvPath);
      }

      if (result && result.length > 0) {
        // There were errors
        return {
          success: false,
          error: result[0]?.error_message || "Processing error",
          result: result,
        };
      }

      return {
        success: true,
        message: "Row processed successfully",
      };

    } catch (processingError) {
      // Clean up temp file on error
      if (fs.existsSync(tempCsvPath)) {
        fs.unlinkSync(tempCsvPath);
      }
      throw processingError;
    }

  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * Store errors from processCSV.js in the database
 * @param {number} csvDataId - CSV data ID
 * @param {Array} errors - Array of error objects from processCSV
 */
async function storeProcessCsvErrors(csvDataId, errors) {
  try {
    console.log(`Storing ${errors.length} errors from processCSV for CSV ID: ${csvDataId}`);
    
    // Get all rows for this CSV to map errors to row numbers
    const allRows = await prisma.csvDataLog.findMany({
      where: { data_id: csvDataId },
      orderBy: { row_number: 'asc' },
      select: {
        id: true,
        row_number: true,
        key1: true,
        key2: true,
        key3: true,
        key4: true,
        key5: true,
        key6: true,
        key7: true,
        key8: true,
        key9: true,
        key10: true,
      }
    });

    // Process each error and map it to the appropriate row
    for (let i = 0; i < errors.length; i++) {
      const error = errors[i];
      const rowIndex = i; // processCSV returns errors in the same order as rows
      
      if (rowIndex < allRows.length) {
        const row = allRows[rowIndex];
        
        // Update the row with error information
        await prisma.csvDataLog.update({
          where: { id: row.id },
          data: {
            processed: true,
            error_status: true,
            error_message: typeof error === 'string' ? error : JSON.stringify(error),
          }
        });
        
        console.log(`Stored error for row ${row.row_number}: ${typeof error === 'string' ? error : JSON.stringify(error)}`);
      }
    }
    
    console.log(`Successfully stored ${errors.length} errors in database`);
  } catch (error) {
    console.error("Error storing processCSV errors:", error);
    throw error;
  }
}

/**
 * Create a temporary CSV file from row data
 * @param {Array} rowDataArray - Array of row objects
 * @returns {string} Path to temporary CSV file
 */
async function createTempCSVFile(rowDataArray) {
  const tempDir = "uploads/temp";
  
  // Ensure temp directory exists
  if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir, { recursive: true });
  }

  const tempFileName = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.csv`;
  const tempFilePath = `${tempDir}/${tempFileName}`;

  if (rowDataArray.length === 0) {
    throw new Error("No data to create CSV file");
  }

  // Get headers from first row
  const headers = Object.keys(rowDataArray[0]);
  
  // Create CSV content
  let csvContent = headers.join(",") + "\n";
  
  for (const row of rowDataArray) {
    const values = headers.map(header => {
      const value = row[header] || "";
      // Escape quotes and wrap in quotes if contains comma or quote
      if (typeof value === "string" && (value.includes(",") || value.includes('"') || value.includes("\n"))) {
        return '"' + value.replace(/"/g, '""') + '"';
      }
      return value;
    });
    csvContent += values.join(",") + "\n";
  }

  // Write to file
  fs.writeFileSync(tempFilePath, csvContent, "utf8");
  
  return tempFilePath;
}

/**
 * Retry failed rows for a CSV
 * @param {number} csvDataId - CSV data ID
 * @param {number} maxRetries - Maximum retries per row
 * @returns {Object} Retry result
 */
async function retryFailedRows(csvDataId, maxRetries = 3) {
  try {
    const csvData = await prisma.csvData.findUnique({
      where: { id: csvDataId },
    });

    if (!csvData) {
      throw new Error("CSV data not found");
    }

    console.log(`Retrying failed rows for CSV: ${csvData.file_name}`);

    // Get failed rows that haven't exceeded max retries
    const failedRows = await prisma.csvDataLog.findMany({
      where: {
        data_id: csvDataId,
        error_status: true,
        retry_count: {
          lt: maxRetries,
        },
      },
      orderBy: {
        row_number: "asc",
      },
    });

    if (failedRows.length === 0) {
      return {
        success: true,
        message: "No failed rows to retry",
        retriedCount: 0,
      };
    }

    console.log(`Found ${failedRows.length} failed rows to retry`);

    let successCount = 0;
    let failureCount = 0;

    // Process failed rows
    for (const rowLog of failedRows) {
      try {
        // Reset error status for retry
        await prisma.csvDataLog.update({
          where: { id: rowLog.id },
          data: {
            error_status: false,
            error_message: null,
          },
        });

        // Reconstruct and process row
        const originalRowData = csvQueueManager.reconstructRowData(rowLog);
        const result = await processSingleRow(
          originalRowData,
          csvData.process_type,
          "insert",
          true
        );

        if (result.success) {
          await csvQueueManager.markRowProcessed(csvDataId, rowLog.row_number, true);
          successCount++;
        } else {
          await csvQueueManager.markRowProcessed(
            csvDataId,
            rowLog.row_number,
            false,
            `Retry failed: ${result.error}`
          );
          failureCount++;
        }

      } catch (rowError) {
        console.error(`Error retrying row ${rowLog.row_number}:`, rowError);
        await csvQueueManager.markRowProcessed(
          csvDataId,
          rowLog.row_number,
          false,
          `Retry error: ${rowError.message}`
        );
        failureCount++;
      }
    }

    console.log(`Retry completed - Success: ${successCount}, Failed: ${failureCount}`);

    return {
      success: true,
      message: "Retry completed",
      retriedCount: failedRows.length,
      successCount,
      failureCount,
    };

  } catch (error) {
    console.error("Error retrying failed rows:", error);
    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * Get processing summary for a CSV
 * @param {number} csvDataId - CSV data ID
 * @returns {Object} Processing summary
 */
async function getProcessingSummary(csvDataId) {
  try {
    const stats = await csvQueueManager.getProcessingStats(csvDataId);
    
    if (!stats) {
      throw new Error("CSV data not found");
    }

    // Get error details
    const errorRows = await prisma.csvDataLog.findMany({
      where: {
        data_id: csvDataId,
        error_status: true,
      },
      select: {
        row_number: true,
        error_message: true,
        retry_count: true,
      },
      orderBy: {
        row_number: "asc",
      },
    });

    return {
      ...stats,
      errorDetails: errorRows,
    };

  } catch (error) {
    console.error("Error getting processing summary:", error);
    throw error;
  }
}

module.exports = {
  processQueuedCSV,
  processSingleRow,
  retryFailedRows,
  getProcessingSummary,
  createTempCSVFile,
};
