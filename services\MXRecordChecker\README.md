# MX Record Checker Service

A comprehensive service for checking and updating MX records of email domains using Google's DNS-over-HTTPS API. This service automatically processes email records and refreshes MX information every 4 months.

## Features

- **Automatic MX Record Lookup**: Uses Google's DNS-over-HTTPS API for reliable MX record queries
- **Scheduled Processing**: Runs every 24 hours via cron job
- **Batch Processing**: Handles large datasets efficiently with configurable batch sizes
- **Rate Limiting**: Respects API rate limits with generous allowances
- **Error Handling**: Robust error handling and retry logic
- **Email Provider Detection**: Automatically detects common email providers (Gmail, Outlook, Yahoo, etc.)
- **Statistics & Monitoring**: Provides detailed processing statistics

## Configuration

### Environment Variables

The service uses the existing database configuration from your `.env` file:

```env
DATABASE_URL=your_postgresql_connection_string
```

### Service Configuration

- **Batch Size**: 50 records per batch (configurable)
- **Refresh Interval**: 120 days (4 months)
- **Cron Schedule**: Daily at 2:00 AM UTC
- **Rate Limits**: 1000 requests per minute

## Database Schema

The service adds the following fields to the `Email` table:

```sql
-- MX Record tracking fields
emailProvider    STRING?     -- Detected email provider (gmail.com, outlook.com, etc.)
mxRecords        JSON?       -- Array of MX records with priority and exchange
lastProcessAt    DATETIME?   -- Last time MX records were processed
nextProcessAt    DATETIME?   -- Next scheduled processing time
```

## API Endpoints

### GET `/mx/stats`
Get MX record processing statistics

**Response:**
```json
{
  "success": true,
  "data": {
    "emailProviders": [
      {
        "_count": 725704,
        "emailProvider": null
      }
    ],
    "totalEmails": 725704,
    "processedEmails": 0,
    "pendingEmails": 725704,
    "processedPercentage": "0.00"
  },
  "timestamp": "2025-07-03T09:35:01.252Z"
}
```

### GET `/mx/pending`
Get email records that need MX processing

**Query Parameters:**
- `details` (optional): Set to `true` to include detailed record information

**Response:**
```json
{
  "success": true,
  "data": {
    "summary": {
      "totalEmails": 50,
      "uniqueDomains": 45
    },
    "emails": [
      {
        "id": 1,
        "fromEmailID": "<EMAIL>",
        "toEmailID": "<EMAIL>",
        "domain": "sellerplexamzservices.com"
      }
    ]
  },
  "timestamp": "2025-07-03T09:35:01.252Z"
}
```

### POST `/mx/process`
Manually trigger MX record processing

**Response:**
```json
{
  "success": true,
  "message": "MX record processing started",
  "timestamp": "2025-07-03T09:35:01.252Z"
}
```

### GET `/mx/scheduler/status`
Get scheduler status and statistics

**Response:**
```json
{
  "success": true,
  "data": {
    "isRunning": false,
    "lastRunTime": "2025-07-03T02:00:00.000Z",
    "nextRunTime": "2025-07-04T02:00:00.000Z",
    "stats": {
      "totalRuns": 5,
      "successfulRuns": 5,
      "failedRuns": 0,
      "lastError": null
    }
  }
}
```

## Usage

### Starting the Service

The service automatically starts when the main application starts:

```javascript
const mxScheduler = require('./services/MXRecordChecker/scheduler');
mxScheduler.start();
```

### Manual Processing

You can also manually process MX records:

```javascript
const MXRecordChecker = require('./services/MXRecordChecker');

const checker = new MXRecordChecker();
await checker.processAllRecords();
```

### Testing

Run the test suite to verify functionality:

```bash
node services/MXRecordChecker/test.js
```

## How It Works

1. **Email Detection**: The service identifies email records that need MX processing:
   - Records with `emailProvider = null`
   - Records with `nextProcessAt = null`
   - Records past their `nextProcessAt` time

2. **Domain Extraction**: Extracts the domain from email addresses using regex

3. **MX Lookup**: Queries Google's DNS-over-HTTPS API for MX records:
   ```
   https://dns.google/resolve?name=domain.com&type=MX
   ```

4. **Provider Detection**: Analyzes MX records to detect email providers:
   - Gmail: `gmail-smtp-in.l.google.com`
   - Outlook: `outlook.com`
   - Yahoo: `yahoodns.net`
   - And many more...

5. **Database Update**: Updates the email record with:
   - Detected email provider
   - Full MX record details
   - Processing timestamps

## Error Handling

The service includes comprehensive error handling:

- **DNS Query Failures**: Retries failed queries after 24 hours
- **Rate Limiting**: Respects API rate limits with delays between batches
- **Database Errors**: Logs errors and continues processing other records
- **Invalid Data**: Handles malformed email addresses gracefully

## Monitoring

Monitor the service through:

- **API Endpoints**: Check processing statistics and status
- **Logs**: Detailed logging of all operations
- **Database**: Query `Email` table for processing status

## Common Email Providers Detected

- Gmail (gmail.com)
- Outlook (outlook.com, hotmail.com)
- Yahoo (yahoo.com)
- ProtonMail (protonmail.com)
- Zoho (zoho.com)
- Apple (icloud.com)
- And many more...

## Troubleshooting

### Service Not Starting
- Check if cron job is properly configured
- Verify database connection
- Check for port conflicts

### No Records Being Processed
- Verify email records exist in the database
- Check if records have valid `fromEmailID` values
- Ensure database schema includes MX fields

### API Errors
- Check internet connectivity
- Verify Google DNS API is accessible
- Monitor rate limits

## Development

### Adding New Email Providers

To add detection for new email providers, update the `processMXRecords` method:

```javascript
if (exchange.includes('newprovider.com')) {
  provider = 'newprovider.com';
}
```

### Customizing Refresh Intervals

Modify the `refreshIntervalDays` property in the constructor:

```javascript
this.refreshIntervalDays = 90; // 3 months instead of 4
```

### Adjusting Batch Sizes

For better performance with large datasets, adjust the batch size:

```javascript
this.batchSize = 100; // Process 100 records at once
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This service is part of the SellerBot project and follows the same license terms. 