CREATE MATERIALIZED VIEW amazon_seller_priority_expanded_mv AS
WITH company_data AS (
  SELECT
    c.id,
    c.name,
    c.seller_group_id,
    c.primary_category,
    c.primary_sub_category,
    c.business_name,
    c.domain,
    c.website_status,
    c.lookup_sources,
    c.lookup_source,
    c.adr_country AS country,
    c.marketplace,
    c.amazon_seller_id,
    c.estimate_sales AS derived_estimate_sales,
    COUNT(CASE WHEN p.email_status IN ('CATCHALL', 'VERIFIED', 'GREYLISTING') THEN p.prospect_id END) AS jeff_num_prospects,
    COUNT(CASE WHEN p.email_status = 'INCONCLUSIVE' THEN p.prospect_id END) AS jeff_num_inconclusive,
    COUNT(CASE WHEN p.email_status = 'UNAVAILABLE' THEN p.prospect_id END) AS jeff_num_unavailable,
    COUNT(CASE WHEN p.email_status = 'FAILED' THEN p.prospect_id END) AS jeff_num_failed,
    COUNT(CASE WHEN p.email_status = 'UNVERIFIED' THEN p.prospect_id END) AS jeff_num_unverified,
    CASE
      WHEN c.adr_country = 'CN' THEN 'SP9'
      WHEN (c.estimate_sales IS NULL OR c.estimate_sales = 0)
           AND COALESCE(c.adr_country, '') IN ('US','GB','AU','IL','DE','ES','FR','NL','NZ','IE','CH','EE','SE','BE','CA')
        THEN 'SP4'
      WHEN (c.estimate_sales IS NULL OR c.estimate_sales = 0)
           AND COALESCE(c.adr_country, '') NOT IN ('US','GB','AU','IL','DE','ES','FR','NL','NZ','IE','CH','EE','SE','BE','CA')
        THEN 'SP8'
      WHEN c.estimate_sales > 0
           AND c.estimate_sales <= 10000
           AND COALESCE(c.adr_country, '') IN ('US','GB','AU','IL','DE','ES','FR','NL','NZ','IE','CH','EE','SE','BE','CA')
        THEN 'SP3B'
      WHEN c.estimate_sales > 10000
           AND c.estimate_sales < 20000
           AND COALESCE(c.adr_country, '') IN ('US','GB','AU','IL','DE','ES','FR','NL','NZ','IE','CH','EE','SE','BE','CA')
        THEN 'SP3A'
      WHEN c.estimate_sales > 0
           AND c.estimate_sales < 20000
           AND COALESCE(c.adr_country, '') NOT IN ('US','GB','AU','IL','DE','ES','FR','NL','NZ','IE','CH','EE','SE','BE','CA')
        THEN 'SP7'
      WHEN c.estimate_sales > 20000
           AND c.estimate_sales < 50000
           AND COALESCE(c.adr_country, '') IN ('US','GB','AU','IL','DE','ES','FR','NL','NZ','IE','CH','EE','SE','BE','CA')
        THEN 'SP2'
      WHEN c.estimate_sales > 20000
           AND c.estimate_sales < 50000
           AND COALESCE(c.adr_country, '') NOT IN ('US','GB','AU','IL','DE','ES','FR','NL','NZ','IE','CH','EE','SE','BE','CA')
        THEN 'SP6'
      WHEN c.estimate_sales > 50000
           AND COALESCE(c.adr_country, '') IN ('US','GB','AU','IL','DE','ES','FR','NL','NZ','IE','CH','EE','SE','BE','CA')
        THEN 'SP1'
      WHEN c.estimate_sales > 50000
           AND COALESCE(c.adr_country, '') NOT IN ('US','GB','AU','IL','DE','ES','FR','NL','NZ','IE','CH','EE','SE','BE','CA')
        THEN 'SP5'
      ELSE NULL
    END AS jeff_search_priority
  FROM
    "public"."AmazonSeller" c
  LEFT JOIN 
    "public"."AmazonProspect" p ON p.seller_id = c.id
  GROUP BY
    c.id,
    c.name,
    c.amazon_seller_id,
    c.marketplace,
    c.seller_group_id,
    c.primary_category,
    c.primary_sub_category,
    c.business_name,
    c.adr_state,
    c.domain,
    c.website_status,
    c.lookup_sources,
    c.lookup_source,
    c.adr_country,
    c.estimate_sales
)
SELECT
  *,
  CASE
    WHEN jeff_num_prospects = 0 THEN jeff_search_priority || ':0'
    WHEN jeff_search_priority IN ('SP5', 'SP6', 'SP7', 'SP8', 'SP9') THEN jeff_search_priority || ':4'
    WHEN jeff_num_prospects BETWEEN 1 AND 3 THEN jeff_search_priority || ':4'
    WHEN jeff_num_prospects >= 4 THEN jeff_search_priority || ':4+'
    ELSE jeff_search_priority
  END AS jeff_send_priority
FROM
  company_data
LIMIT 3048575;
