const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();
const axios = require("axios");

require("dotenv").config();
const SMART_LEADS_API_KEY = process.env.SMART_LEADS_API_KEY;

const MAX_LIMIT = 100;

const RATE_LIMIT_TOTAL = 200; // total requests allowed per window
const RATE_LIMIT_WINDOW = 60; // seconds

async function adaptiveSleep(headers) {
  const remaining = parseInt(headers["x-ratelimit-remaining"] || "1");
  const resetEpoch = parseInt(headers["x-ratelimit-reset"] || "0"); // epoch seconds

  const nowEpoch = Math.floor(Date.now() / 1000);
  let timeUntilReset = resetEpoch - nowEpoch;

  if (timeUntilReset < 0) {
    // Reset window passed, no need to wait
    timeUntilReset = 0;
  }

  if (remaining <= 1) {
    // We reached the limit, wait until reset + buffer
    const sleepMs = timeUntilReset * 1000 + 100;
    console.log(
      `⏳ Rate limit reached. Sleeping for ${(sleepMs / 1000).toFixed(2)}s`
    );
    await new Promise((res) => setTimeout(res, sleepMs));
    return;
  }

  // Calculate how many requests we've used in this window
  const used = RATE_LIMIT_TOTAL - remaining;

  // Calculate elapsed time since window start
  const elapsed = RATE_LIMIT_WINDOW - timeUntilReset;

  // Calculate average interval per request to spread requests evenly
  const avgInterval = RATE_LIMIT_WINDOW / RATE_LIMIT_TOTAL; // in seconds

  // Calculate how fast we can send next request to not exceed average rate
  const expectedElapsed = used * avgInterval;

  let delay = (expectedElapsed - elapsed) * 1000; // ms

  // If delay is negative, send immediately
  if (delay < 0) delay = 0;

  // Cap max delay to 1 second to avoid slowing too much
  if (delay > 1000) delay = 1000;

  console.log(
    `⏳ Remaining: ${remaining}, Delay: ${(delay / 1000).toFixed(2)}s`
  );
  await new Promise((res) => setTimeout(res, delay));
}

async function fetchLeadsWithRetry(url, params, maxRetries = 5) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await axios.get(url, { params });
      return response;
    } catch (error) {
      if (error.response?.status === 404) {
        console.warn(`⚠️ 404 received. Skipping request.`);
        return null; // or handle as needed
      }
      if (error.response?.status === 502 && attempt < maxRetries) {
        const delay = 1000 * attempt; // exponential backoff
        console.warn(`⚠️ 502 received. Retry #${attempt} after ${delay}ms`);
        await new Promise((res) => setTimeout(res, delay));
      } else {
        console.error(`❌ Request failed: ${error.message}`);

        // Respect rate-limit on error
        if (error.response && error.response.headers) {
          await adaptiveSleep(error.response.headers);
        } else {
          const fallbackDelayMs = 1000; // 1 second fallback
          console.log(
            `⏳ No rate-limit headers in error. Sleeping for ${fallbackDelayMs / 1000}s`
          );
          await new Promise((res) => setTimeout(res, fallbackDelayMs));
        }

        throw error; // rethrow after respecting rate-limit
      }
    }
  }
}


async function getLeadsData(campaignId) {
  try {
    const campaign = await prisma.campaign.findFirst({
      where: { campaignId },
    });

    if (!campaign) {
      throw new Error(`Campaign with ID ${campaignId} not found`);
    }

    let offset = 0;
    let totalLeads = 0;
    let allLeads = [];
    let successfulUpserts = 0;
    let failedUpserts = 0;

    do {
      const leadsUrl = `https://server.smartlead.ai/api/v1/campaigns/${campaign.campaignId}/leads`;
      const response = await fetchLeadsWithRetry(leadsUrl, {
        api_key: SMART_LEADS_API_KEY,
        offset: offset,
        limit: MAX_LIMIT,
      });
      if (!response) {
        console.warn(`❌ No data found at offset ${offset}, skipping...`);
        offset += MAX_LIMIT;
        continue; // skip to next iteration
      }
      const headers = response.headers;
      const { data, total_leads } = response.data;

      // Update total leads on first iteration
      if (offset === 0) {
        totalLeads = total_leads;
        console.log(`Total leads to fetch: ${totalLeads}`);
      }

      console.log(
        `Processing batch of ${data.length} leads (offset: ${offset})`
      );

      const upsertOperations = data.map((lead) => {
        const leadData = {
          campaignLeadMapId: lead.campaign_lead_map_id,
          leadStatus: lead.status,
          leadId: lead.lead.id.toString(),
          email: lead.lead.email,
          website: lead.lead.website,
          campaignId: campaign.id,
          lead_category_id: lead.lead_category_id,
        };
        // console.log({leadData})

        return prisma.smartLead_Lead.upsert({
          where: { campaignLeadMapId: leadData.campaignLeadMapId },
          update: leadData,
          create: leadData,
        });
      });

      try {
        await prisma.$transaction(upsertOperations);
        successfulUpserts += upsertOperations.length;
      } catch (error) {
        console.error("❌ Error during bulk upsert:", error.message);
        failedUpserts += upsertOperations.length;
      }

      allLeads = [...allLeads, ...data];
      offset += MAX_LIMIT;

      console.log(`Processed ${allLeads.length} out of ${totalLeads} leads`);
      console.log(
        `Successful upserts: ${successfulUpserts}, Failed upserts: ${failedUpserts}`
      );
      await adaptiveSleep(headers);
    } while (allLeads.length < totalLeads);
    console.log(
      `✅ Successfully processed all ${totalLeads} leads for campaign ${campaignId}`
    );
    console.log(
      `Final counts - Total leads: ${totalLeads}, Successful upserts: ${successfulUpserts}, Failed upserts: ${failedUpserts}`
    );
    return allLeads;
  } catch (error) {
    console.error(`Error in getLeadsData function: ${error.message}`);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// getLeadsData(1741620);

module.exports = { getLeadsData, adaptiveSleep };
