/*
  Warnings:

  - You are about to drop the column `seller_group_id` on the `AmazonProspect` table. All the data in the column will be lost.
  - You are about to drop the column `seller_group_id` on the `Prospect` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "AmazonProspect" DROP CONSTRAINT "AmazonProspect_seller_group_id_fkey";

-- DropForeignKey
ALTER TABLE "Prospect" DROP CONSTRAINT "Prospect_seller_group_id_fkey";

-- AlterTable
ALTER TABLE "AmazonProspect" DROP COLUMN "seller_group_id",
ADD COLUMN     "seller_id" INTEGER;

-- AlterTable
ALTER TABLE "Prospect" DROP COLUMN "seller_group_id";

-- AddForeignKey
ALTER TABLE "AmazonProspect" ADD CONSTRAINT "AmazonProspect_seller_id_fkey" FOREIGN KEY ("seller_id") REFERENCES "AmazonSeller"("id") ON DELETE SET NULL ON UPDATE CASCADE;
