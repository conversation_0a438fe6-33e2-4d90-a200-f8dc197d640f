const { PrismaClient } = require("@prisma/client");
const { ReviewStatus } = require("@prisma/client");
const fs = require("fs");
const path = require("path");
const csvtojson = require("csvtojson");

const prisma = new PrismaClient();

/**
 * Analyze reviews that were incorrectly marked compared to CSV data
 */
async function analyzeRemovedReviews() {
    try {
        console.log("🔍 Analyzing incorrectly marked reviews compared to CSV...");

        // Path to the CSV file
        const csvPath = path.join(__dirname, '../sample/removed/lex_reviews_output_2025-08-08T13-56-37-245Z.csv');

        if (!fs.existsSync(csvPath)) {
            console.error(`❌ CSV file not found: ${csvPath}`);
            return;
        }

        console.log(`📄 Reading CSV file: ${csvPath}`);

        // Read and parse the CSV file
        const csvData = await csvtojson().fromFile(csvPath);
        console.log(`📊 Found ${csvData.length} records in CSV`);

        // Create a map of reviewID to status from CSV
        const csvStatusMap = new Map();
        csvData.forEach(row => {
            const reviewId = row['Review ID'];
            const status = row['Status'];
            if (reviewId && status) {
                csvStatusMap.set(reviewId, status);
            }
        });

        console.log(`🗺️  Created status map for ${csvStatusMap.size} reviews from CSV`);

        // Get all reviews from the database that were updated yesterday
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        yesterday.setHours(0, 0, 0, 0);

        const today = new Date();
        today.setHours(0, 0, 0, 0);

        console.log(`📅 Looking for reviews updated between ${yesterday.toISOString()} and ${today.toISOString()}`);

        const dbReviews = await prisma.review.findMany({
            where: {
                updatedAt: {
                    gte: yesterday,
                    lt: today
                }
            },
            select: {
                id: true,
                reviewId: true,
                reviewUrl: true,
                status: true,
                removedAt: true,
                updatedAt: true,
                reviewJobId: true
            }
        });

        console.log(`📊 Found ${dbReviews.length} reviews in database from yesterday`);

        let correctCount = 0;
        let incorrectCount = 0;
        let notFoundInCSVCount = 0;
        let incorrectReviews = [];
        let correctReviews = [];
        let notFoundReviews = [];
        let notFoundStatusBreakdown = {}; // Add status breakdown for not found reviews

        for (const dbReview of dbReviews) {
            const csvStatus = csvStatusMap.get(dbReview.reviewId);

            if (!csvStatus) {
                console.log(`❓ Review ${dbReview.reviewId} not found in CSV`);
                notFoundInCSVCount++;
                notFoundReviews.push({
                    reviewId: dbReview.reviewId,
                    reviewUrl: dbReview.reviewUrl,
                    reviewJobId: dbReview.reviewJobId,
                    dbStatus: dbReview.status
                });

                // Track status breakdown for not found reviews
                notFoundStatusBreakdown[dbReview.status] = (notFoundStatusBreakdown[dbReview.status] || 0) + 1;
                continue;
            }

            // Check if the status matches
            if (dbReview.status !== csvStatus) {
                console.log(`❌ MISMATCH: Review ${dbReview.reviewId}: DB=${dbReview.status}, CSV=${csvStatus}`);
                incorrectCount++;
                incorrectReviews.push({
                    reviewId: dbReview.reviewId,
                    reviewUrl: dbReview.reviewUrl,
                    reviewJobId: dbReview.reviewJobId,
                    dbStatus: dbReview.status,
                    csvStatus: csvStatus
                });
            } else {
                console.log(`✅ MATCH: Review ${dbReview.reviewId}: DB=${dbReview.status}, CSV=${csvStatus}`);
                correctCount++;
                correctReviews.push({
                    reviewId: dbReview.reviewId,
                    reviewUrl: dbReview.reviewUrl,
                    reviewJobId: dbReview.reviewJobId,
                    status: dbReview.status
                });
            }
        }

        console.log("\n" + "=".repeat(80));
        console.log("📈 ANALYSIS SUMMARY");
        console.log("=".repeat(80));
        console.log(`✅ Correct matches: ${correctCount}`);
        console.log(`❌ Incorrect matches: ${incorrectCount}`);
        console.log(`❓ Not found in CSV: ${notFoundInCSVCount}`);
        console.log(`📊 Total processed: ${dbReviews.length}`);
        console.log(`📄 Total in CSV: ${csvData.length}`);

        // Show status breakdown for reviews not found in CSV
        if (Object.keys(notFoundStatusBreakdown).length > 0) {
            console.log("\n📊 Status Breakdown for Reviews NOT in CSV:");
            Object.entries(notFoundStatusBreakdown).forEach(([status, count]) => {
                console.log(`  ${status}: ${count} reviews`);
            });
        }

        // Show breakdown by status
        const statusBreakdown = {};
        incorrectReviews.forEach(review => {
            const key = `${review.dbStatus} → ${review.csvStatus}`;
            statusBreakdown[key] = (statusBreakdown[key] || 0) + 1;
        });

        if (Object.keys(statusBreakdown).length > 0) {
            console.log("\n📊 Status Change Breakdown:");
            Object.entries(statusBreakdown).forEach(([change, count]) => {
                console.log(`  ${change}: ${count} reviews`);
            });
        }

        // Create a detailed analysis report
        const analysis = {
            timestamp: new Date().toISOString(),
            csvFilePath: csvPath,
            csvRecordCount: csvData.length,
            csvStatusMapSize: csvStatusMap.size,
            dbReviewCount: dbReviews.length,
            correctCount,
            incorrectCount,
            notFoundInCSVCount,
            statusBreakdown,
            incorrectReviews,
            correctReviews,
            notFoundReviews,
            notFoundStatusBreakdown // Add notFoundStatusBreakdown to analysis
        };

        // Save analysis to file
        const analysisPath = path.join(__dirname, '../sample/removed/analysis_removedReviews.json');
        fs.writeFileSync(analysisPath, JSON.stringify(analysis, null, 2));
        console.log(`\n📄 Analysis saved to: ${analysisPath}`);

        // Show some examples of incorrect reviews
        if (incorrectReviews.length > 0) {
            console.log("\n🔍 Examples of incorrect reviews:");
            incorrectReviews.slice(0, 10).forEach(review => {
                console.log(`  ${review.reviewId}: ${review.dbStatus} → ${review.csvStatus}`);
            });
            if (incorrectReviews.length > 10) {
                console.log(`  ... and ${incorrectReviews.length - 10} more`);
            }
        }

        console.log("\n🎉 Analysis completed!");

    } catch (error) {
        console.error("❌ Error in analyzeRemovedReviews:", error);
        throw error;
    } finally {
        await prisma.$disconnect();
    }
}

// Run the analysis if this file is executed directly
if (require.main === module) {
    analyzeRemovedReviews().catch(console.error);
}

module.exports = { analyzeRemovedReviews };
