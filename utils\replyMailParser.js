const cheerio = require("cheerio");

function extractTextFromHTML(htmlString) {
  const $ = cheerio.load(htmlString);
  return $("body").text();
}

function cleanReply(reply) {
  reply = extractTextFromHTML(reply);
  // Remove automated response markers
  reply = reply.replace(/##- Please type your reply above this line -##/g, "");
  reply = reply.replace(/-- Please reply above this line --/g, "");

  // Remove quoted text and reply threads (e.g., "-----Original Message-----" or "On ... wrote:")
  reply = reply.replace(/-----Original Message-----.*$/gis, "");
  reply = reply.replace(/wrote:.*/gis, "");

  // Remove common email headers and footers
  reply = reply.replace(/From:.*|To:.*|Subject:.*|Date:.*$/gim, "");

  // Remove email signatures, disclaimers, and unrelated links
  reply = reply.replace(
    /(Best regards,|Thanks,|Sincerely,|Kind regards,|Regards,).*$/gis,
    ""
  );
  reply = reply.replace(
    /(To add additional comments, reply to this email).*$/gis,
    ""
  );
  reply = reply.replace(
    /(This email.*is confidential|Do not share|For.*visit.*https?:\/\/).*/gis,
    ""
  );
  // reply = reply.replace(/unsubscribe/gi, "");
  reply = reply.replace(/Thank you for your patience.*$/gis, "");
  reply = reply.replace(
    /We are currently experiencing a higher volume.*$/gis,
    ""
  );
  reply = reply.replace(/You can copy the following URL.*$/gis, "");
  reply = reply.replace(/Click here.*$/gis, "");
  reply = reply.replace(/https?:\/\/\S+/g, ""); // Remove URLs
  reply = reply.replace(/\[.*?\]/g, ""); // Remove bracketed content
  reply = reply.replace(/\*.*?\*/g, ""); // Remove emphasized text blocks
  // reply = reply.replace(/\bunsubscribe\b/gi, ""); // Remove "unsubscribe"
  reply = reply.replace(/Sent from my .*?\n/gi, ""); // Sent from mobile disclaimers
  reply = reply.replace(/(If you have any further questions).*$/gi, ""); // Generic closing statements

  // Remove excessive whitespace and normalize line breaks
  reply = reply.replace(/\n\s*\n/g, "\n").trim();

  // Keep only the main body content (remove overly technical or promotional blocks)
  reply = reply.replace(/We appreciate hearing from you.*$/gis, "");
  reply = reply.replace(/Links:------.*$/gis, "");
  reply = reply.replace(/A Customer Support Representative.*$/gis, "");

  // Remove text enclosed in ** and anything after the first *
  reply = reply.replace(/\*\*.*?\*\*/g, ""); // Remove text within double asterisks
  reply = reply.replace(/.*?\*[\s\S]*$/g, ""); // Remove everything after the first asterisk (*)

  // Remove everything within < > and after an email
  reply = reply.replace(/<[^>]*>/g, ""); // Remove text within <>
  // reply = reply.replace(
  //   /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}.*$/g,
  //   ""
  // ); // Remove everything after an email address
  reply = reply
    .replace(/&gt;/g, ">")
    .replace(/&lt;/g, "<")
    .replace(/&amp;/g, "&");

  return reply.trim();
}

module.exports = { cleanReply };
