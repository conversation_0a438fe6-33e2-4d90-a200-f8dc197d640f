const csvQueueManager = require("../../utils/csvQueueManagement");
const { processQueuedCSV } = require("./csvFileProcessor");
const prisma = require("../../database/prisma/getPrismaClient");

/**
 * CSV Processing Scheduler
 * Handles automatic processing of queued CSV files
 */
class CsvScheduler {
  constructor() {
    this.isRunning = false;
    this.currentJob = null;
    this.processingInterval = null;
    this.intervalSeconds = 60; // Check every 30 seconds
  }

  /**
   * Start the CSV processing scheduler
   */
  start() {
    if (this.isRunning) {
      console.log("CSV Scheduler is already running");
      return;
    }

    console.log("Starting CSV Processing Scheduler...");
    this.isRunning = true;

    // Start immediate processing check
    this.processQueue();

    // Schedule periodic processing checks
    this.processingInterval = setInterval(() => {
      this.processQueue();
    }, this.intervalSeconds * 1000);

    console.log(`CSV Scheduler started with ${this.intervalSeconds}s interval`);
  }

  /**
   * Stop the CSV processing scheduler
   */
  stop() {
    if (!this.isRunning) {
      console.log("CSV Scheduler is not running");
      return;
    }

    console.log("Stopping CSV Processing Scheduler...");
    this.isRunning = false;

    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
    }

    console.log("CSV Scheduler stopped");
  }

  /**
   * Process the CSV queue
   */
  async processQueue() {
    if (this.currentJob) {
      console.log("CSV processing already in progress, skipping...");
      return;
    }

    try {
      const nextCsv = await csvQueueManager.getNextPending();
      
      if (!nextCsv) {
        // No pending CSVs to process
        return;
      }

      console.log(`Found pending CSV: ${nextCsv.file_name} (ID: ${nextCsv.id})`);
      
      this.currentJob = nextCsv;
      await this.processCSV(nextCsv);
      
    } catch (error) {
      console.error("Error in processQueue:", error);
      
      if (this.currentJob) {
        await csvQueueManager.updateStatus(
          this.currentJob.id,
          "FAILED",
          `Scheduler error: ${error.message}`
        );
      }
    } finally {
      this.currentJob = null;
    }
  }

  /**
   * Process a single CSV file
   * @param {Object} csvData - CSV data record
   */
  async processCSV(csvData) {
    try {
      console.log(`Processing CSV: ${csvData.file_name} (Type: ${csvData.process_type})`);
      
      // Update status to processing
      await csvQueueManager.updateStatus(csvData.id, "PROCESSING");

      // Process the CSV using the processor
      const result = await processQueuedCSV(csvData);

      if (result.success) {
        await csvQueueManager.updateStatus(csvData.id, "COMPLETED");
        console.log(`Successfully completed CSV: ${csvData.file_name}`);
      } else {
        await csvQueueManager.updateStatus(
          csvData.id,
          "FAILED",
          result.error || "Processing failed"
        );
        console.error(`Failed to process CSV: ${csvData.file_name} - ${result.error}`);
      }

    } catch (error) {
      console.error(`Error processing CSV ${csvData.file_name}:`, error);
      await csvQueueManager.updateStatus(
        csvData.id,
        "FAILED",
        `Processing error: ${error.message}`
      );
    }
  }

  /**
   * Get current scheduler status
   * @returns {Object} Scheduler status information
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      currentJob: this.currentJob ? {
        id: this.currentJob.id,
        fileName: this.currentJob.file_name,
        processType: this.currentJob.process_type,
      } : null,
      intervalSeconds: this.intervalSeconds,
    };
  }

  /**
   * Set processing interval
   * @param {number} seconds - Interval in seconds
   */
  setInterval(seconds) {
    this.intervalSeconds = seconds;
    
    if (this.isRunning) {
      // Restart with new interval
      this.stop();
      this.start();
    }
    
    console.log(`CSV Scheduler interval set to ${seconds} seconds`);
  }

  /**
   * Process a specific CSV by ID (manual trigger)
   * @param {number} csvDataId - CSV data ID
   * @returns {Object} Processing result
   */
  async processSpecificCSV(csvDataId) {
    try {
      const csvData = await prisma.csvData.findUnique({
        where: { id: csvDataId },
      });

      if (!csvData) {
        throw new Error("CSV data not found");
      }

      if (csvData.status === "PROCESSING") {
        throw new Error("CSV is already being processed");
      }

      console.log(`Manual processing triggered for CSV: ${csvData.file_name}`);
      
      // Temporarily store current job if any
      const previousJob = this.currentJob;
      this.currentJob = csvData;

      try {
        await this.processCSV(csvData);
        return { success: true, message: "CSV processed successfully" };
      } finally {
        // Restore previous job
        this.currentJob = previousJob;
      }

    } catch (error) {
      console.error("Error in manual CSV processing:", error);
      return { 
        success: false, 
        error: error.message,
        message: "Failed to process CSV manually"
      };
    }
  }

  /**
   * Get queue statistics
   * @returns {Object} Queue statistics
   */
  async getQueueStats() {
    try {
      const stats = await csvQueueManager.getQueueList({
        page: 1,
        limit: 1000, // Get all for stats
      });

      const statusCounts = stats.data.reduce((acc, csv) => {
        acc[csv.status] = (acc[csv.status] || 0) + 1;
        return acc;
      }, {});

      const processTypeCounts = stats.data.reduce((acc, csv) => {
        acc[csv.process_type] = (acc[csv.process_type] || 0) + 1;
        return acc;
      }, {});

      return {
        total: stats.pagination.total,
        statusCounts,
        processTypeCounts,
        currentJob: this.currentJob ? {
          id: this.currentJob.id,
          fileName: this.currentJob.file_name,
          processType: this.currentJob.process_type,
        } : null,
        isRunning: this.isRunning,
      };
    } catch (error) {
      console.error("Error getting queue stats:", error);
      throw error;
    }
  }
}

// Create singleton instance
const csvScheduler = new CsvScheduler();

// Auto-start scheduler if enabled via environment variable
if (process.env.AUTO_START_CSV_SCHEDULER === "true") {
  csvScheduler.start();
  console.log("CSV Scheduler auto-started via environment variable");
}

module.exports = csvScheduler;
