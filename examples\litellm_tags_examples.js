#!/usr/bin/env node

/**
 * LiteLLM Tags Examples
 * 
 * This file shows exactly how to make requests with proper LiteLLM metadata/tags format
 * using environment variables for authentication, just like your curl example.
 */

require('dotenv').config();
const axios = require('axios');
const { litellmService } = require('../services/ai/litellmService');
const { aiMetadataHelper } = require('../utils/aiMetadataHelper');

// Use environment variables for configuration
const LITELLM_PROXY_URL = process.env.LITELLM_PROXY_URL || 'http://74.225.248.239:4000';
const API_KEY = process.env.LITELLM_API_KEY || process.env.OPENAI_API_KEY || 'sk-1234';

/**
 * Example 1: Direct HTTP request (exactly like your curl example)
 */
async function directHttpExample() {
    console.log('🔹 Example 1: Direct HTTP Request (like your curl)');
    
    try {
        const requestData = {
            model: 'gpt-4o',
            messages: [
                {
                    role: 'user',
                    content: 'what llm are you'
                }
            ],
            user: 'sellerbot_user', // OPTIONAL: pass user to track spend by user
            metadata: {
                tags: [
                    'jobID:214590dsff09fds', 
                    'taskName:run_page_classification',
                    'service:SellerBot',
                    'environment:' + (process.env.NODE_ENV || 'development'),
                    'function:directHttpExample'
                ] // ENTERPRISE: pass tags to track spend by tags
            }
        };
        
        console.log('Request:', JSON.stringify(requestData, null, 2));
        
        const response = await axios.post(`${LITELLM_PROXY_URL}/v1/chat/completions`, requestData, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${API_KEY}`
            }
        });
        
        console.log('Response:', response.data.choices[0].message.content);
        console.log('Usage:', response.data.usage);
        
    } catch (error) {
        console.error('Error:', error.response?.data || error.message);
    }
}

/**
 * Example 2: Using LiteLLM Service (automatic tags generation)
 */
async function litellmServiceExample() {
    console.log('\n🔹 Example 2: LiteLLM Service with Auto Tags');
    
    try {
        const result = await litellmService.createChatCompletion([
            {
                role: 'system',
                content: 'You are a helpful assistant for SellerBot e-commerce platform.'
            },
            {
                role: 'user',
                content: 'How can AI help with Amazon seller automation?'
            }
        ], {
            // Business context
            useCase: 'customer_support',
            feature: 'ai_explanation',
            userId: 'support_agent_123',
            userType: 'employee',
            
            // Model settings
            model: 'gpt-4o',
            temperature: 0.7,
            max_tokens: 200,
            
            // Custom tags for tracking
            customTags: {
                jobID: 'support_job_12345',
                taskName: 'ai_benefits_explanation',
                department: 'customer_success',
                priority: 'normal'
            }
        });
        
        if (result.success) {
            console.log('Response:', result.message);
            console.log('Generated Tags:', result.metadata.tags);
            console.log('User:', result.metadata.user || 'system');
        } else {
            console.error('Error:', result.error);
        }
        
    } catch (error) {
        console.error('Error:', error.message);
    }
}

/**
 * Example 3: ScrapeGPT with business-specific tags
 */
async function scrapeGPTExample() {
    console.log('\n🔹 Example 3: ScrapeGPT with Business Tags');
    
    try {
        const result = await litellmService.getChatGPTResponse(
            'You are an AI that extracts business information from website content.',
            'Extract company name and business type from: "TechCorp Inc. is a leading software development company specializing in AI solutions for e-commerce."',
            {
                // ScrapeGPT specific context
                useCase: 'scrape_analysis',
                feature: 'business_extraction',
                userId: 'scraper_bot',
                userType: 'system',
                
                // Scraping context
                scrapeType: 'business_info',
                domain: 'techcorp.com',
                
                // Custom tracking tags
                customTags: {
                    jobID: 'scrape_job_67890',
                    taskName: 'business_info_extraction',
                    batchID: 'batch_001',
                    sourceType: 'website_content',
                    extractionType: 'company_details'
                }
            }
        );
        
        if (result.success) {
            console.log('Extracted Info:', result.message);
            console.log('Scraping Tags:', result.metadata.tags.filter(tag => 
                tag.includes('scrape') || tag.includes('domain') || tag.includes('batch')
            ));
        } else {
            console.error('Error:', result.error);
        }
        
    } catch (error) {
        console.error('Error:', error.message);
    }
}

/**
 * Example 4: Lead generation with tracking tags
 */
async function leadGenerationExample() {
    console.log('\n🔹 Example 4: Lead Generation with Tracking');
    
    try {
        const leadData = {
            companyName: 'Example Corp',
            website: 'example-corp.com',
            description: 'A software company that builds CRM tools for small businesses'
        };
        
        const result = await litellmService.createChatCompletion([
            {
                role: 'system',
                content: 'You are an AI that analyzes companies for lead qualification.'
            },
            {
                role: 'user',
                content: `Analyze this company for lead qualification: ${JSON.stringify(leadData)}`
            }
        ], {
            // Lead generation context
            useCase: 'lead_generation',
            feature: 'lead_qualification',
            userId: 'lead_gen_system',
            userType: 'system',
            
            // Lead specific tags
            customTags: {
                jobID: 'lead_job_11111',
                taskName: 'company_qualification',
                leadSource: 'manual_input',
                batchID: 'lead_batch_002',
                companyDomain: 'example-corp.com',
                qualificationType: 'initial_screening'
            }
        });
        
        if (result.success) {
            console.log('Lead Analysis:', result.message);
            console.log('Lead Tags:', result.metadata.tags.filter(tag => 
                tag.includes('lead') || tag.includes('company') || tag.includes('qualification')
            ));
        } else {
            console.error('Error:', result.error);
        }
        
    } catch (error) {
        console.error('Error:', error.message);
    }
}

/**
 * Example 5: Email analysis with campaign tracking
 */
async function emailAnalysisExample() {
    console.log('\n🔹 Example 5: Email Analysis with Campaign Tracking');
    
    try {
        const emailContent = 'Subject: Special offer for your business\n\nHi there! We have a special discount on our premium CRM software...';
        
        const result = await litellmService.createChatCompletion([
            {
                role: 'system',
                content: 'You are an AI that classifies email content and sentiment.'
            },
            {
                role: 'user',
                content: `Classify this email content: ${emailContent}`
            }
        ], {
            // Email analysis context
            useCase: 'email_analysis',
            feature: 'email_classification',
            userId: 'email_processor',
            userType: 'system',
            
            // Email specific tags
            emailType: 'promotional',
            campaignId: 'campaign_winter_2024',
            
            customTags: {
                jobID: 'email_job_22222',
                taskName: 'email_sentiment_classification',
                emailSource: 'inbound',
                processingType: 'automated',
                campaignType: 'promotional'
            }
        });
        
        if (result.success) {
            console.log('Email Classification:', result.message);
            console.log('Email Tags:', result.metadata.tags.filter(tag => 
                tag.includes('email') || tag.includes('campaign') || tag.includes('classification')
            ));
        } else {
            console.error('Error:', result.error);
        }
        
    } catch (error) {
        console.error('Error:', error.message);
    }
}

/**
 * Example 6: Streaming with real-time tags
 */
async function streamingExample() {
    console.log('\n🔹 Example 6: Streaming with Real-time Tags');
    
    try {
        const stream = await litellmService.createStreamingCompletion([
            {
                role: 'user',
                content: 'Write a product description for wireless bluetooth headphones, streaming the response.'
            }
        ], {
            // Streaming context
            useCase: 'content_generation',
            feature: 'product_description',
            userId: 'content_writer_bot',
            userType: 'system',
            
            customTags: {
                jobID: 'content_job_33333',
                taskName: 'product_description_generation',
                outputFormat: 'streaming',
                productCategory: 'electronics',
                contentType: 'product_description'
            }
        });
        
        console.log('Streaming response:');
        let fullResponse = '';
        for await (const chunk of stream) {
            const content = chunk.choices[0]?.delta?.content || '';
            if (content) {
                fullResponse += content;
                process.stdout.write(content);
            }
        }
        console.log('\n\nStreaming completed!');
        
    } catch (error) {
        console.error('Error:', error.message);
    }
}

/**
 * Example 7: Show raw tags format
 */
function showTagsFormat() {
    console.log('\n🔹 Example 7: Tags Format Reference');
    
    const metadata = aiMetadataHelper.generateOpenAIMetadata({
        useCase: 'example',
        feature: 'tags_demo',
        userId: 'demo_user',
        operationType: 'demo',
        customTags: {
            jobID: 'demo_job_12345',
            taskName: 'show_tags_format',
            priority: 'high'
        }
    });
    
    console.log('LiteLLM Request Format:');
    console.log(JSON.stringify({
        model: 'gpt-4o',
        messages: [{ role: 'user', content: 'Hello' }],
        user: metadata.user,
        metadata: metadata.metadata
    }, null, 2));
    
    console.log('\nTags Array Format:');
    metadata.metadata.tags.forEach(tag => console.log(`  "${tag}"`));
}

/**
 * Main function to run all examples
 */
async function main() {
    console.log('🚀 LiteLLM Tags Examples');
    console.log(`📍 Proxy URL: ${LITELLM_PROXY_URL}`);
    console.log(`🔑 API Key: ${API_KEY.substring(0, 10)}...\n`);
    
    try {
        await directHttpExample();
        await litellmServiceExample();
        await scrapeGPTExample();
        await leadGenerationExample();
        await emailAnalysisExample();
        await streamingExample();
        showTagsFormat();
        
        console.log('\n✅ All examples completed!');
        console.log('\n💡 Key Points:');
        console.log('1. Tags format: ["key:value", "key:value"] - exactly like your curl example');
        console.log('2. User field enables spend tracking by user in LiteLLM');
        console.log('3. jobID and taskName help track specific operations');
        console.log('4. All requests include automatic function/file context');
        console.log('5. Custom tags can be added for business-specific tracking');
        
    } catch (error) {
        console.error('❌ Examples failed:', error);
    }
}

// Export for use in other files
module.exports = {
    directHttpExample,
    litellmServiceExample,
    scrapeGPTExample,
    leadGenerationExample,
    emailAnalysisExample,
    streamingExample,
    showTagsFormat
};

// Run examples if this file is executed directly
if (require.main === module) {
    main();
}
