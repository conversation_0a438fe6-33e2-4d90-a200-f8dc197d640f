const { centralizedAI } = require("../ai/centralizedAIService");
const { litellmService } = require("../ai/litellmService");
const { aiMetadataHelper, metadataGenerators } = require("../../utils/aiMetadataHelper");
const fs = require("fs");
require("dotenv").config();

async function getChatGPTResponse(system_prompt, user_prompt, usePortkey = true, options = {}) {
  try {
    // Generate metadata for this scrapeGPT request
    const metadata = metadataGenerators.scrapeGPT({
      useCase: options.useCase || 'scrape_analysis',
      feature: options.feature || 'content_analysis',
      userId: options.userId,
      userType: options.userType,
      customTags: {
        system_prompt_length: system_prompt.length,
        user_prompt_length: user_prompt.length,
        use_portkey: usePortkey,
        scrape_type: options.scrapeType || 'general',
        domain: options.domain,
        ...options.customTags
      }
    });

    const messages = [
      {
        role: "system",
        content: system_prompt,
      },
      {
        role: "user",
        content: user_prompt,
      },
    ];

    // Log the request metadata
    aiMetadataHelper.logMetadata({
      ...metadata,
      operation: 'getChatGPTResponse',
      messages_count: messages.length
    }, 'info');

    // Choose service based on configuration
    const useLiteLLM = process.env.USE_LITELLM_PROXY === 'true';

    if (useLiteLLM) {
      // Use LiteLLM service with metadata
      const result = await litellmService.getChatGPTResponse(system_prompt, user_prompt, {
        ...options,
        metadata: metadata,
        temperature: 1,
        presence_penalty: 0,
        top_p: 1,
        max_tokens: 256,
      });

      return result;
    } else {
      // Use existing centralized AI service
      const useCase = (process.env.AZURE_OPENAI_ENDPOINT && process.env.AZURE_OPENAI_DEPLOYMENT) ? 'azure' : 'chat';

      const result = await centralizedAI.createChatCompletion(messages, {
        temperature: 1,
        presence_penalty: 0,
        top_p: 1,
        max_tokens: 256,
        metadata: metadata, // Pass metadata to centralized service
        ...options
      }, useCase, usePortkey);

      return result;
    }
  } catch (error) {
    // Log error with metadata
    aiMetadataHelper.logMetadata({
      operation: 'getChatGPTResponse',
      status: 'error',
      error_message: error.message,
      error_stack: error.stack,
      system_prompt_length: system_prompt?.length || 0,
      user_prompt_length: user_prompt?.length || 0
    }, 'error');

    console.error("Error Stack:", error.stack);
    throw new Error("Error fetching response from ChatGPT: " + error.message);
  }
}

async function main() {
  try {
    const systemPrompt = "You are a helpful assistant.";
    const userPrompt = "What is the capital of France?";

    const response = await getChatGPTResponse(systemPrompt, userPrompt);
    console.log("ChatGPT Response:", response.message);
    console.log("Token Usage:", {
      promptTokens: response.prompt_tokens,
      completionTokens: response.completion_tokens,
      totalTokens: response.total_tokens,
    });
  } catch (error) {
    console.error("Error:", error.message);
  }
}
module.exports = {
  getChatGPTResponse,
};
