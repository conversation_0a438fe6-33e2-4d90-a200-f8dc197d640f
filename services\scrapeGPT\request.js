const { centralizedAI } = require("../ai/centralizedAIService");
const fs = require("fs");
require("dotenv").config();

async function getChatGPTResponse(system_prompt, user_prompt, usePortkey = true) {
  try {
    const messages = [
      {
        role: "system",
        content: system_prompt,
      },
      {
        role: "user",
        content: user_prompt,
      },
    ];

    // Determine use case based on environment variables
    const useCase = (process.env.AZURE_OPENAI_ENDPOINT && process.env.AZURE_OPENAI_DEPLOYMENT) ? 'azure' : 'chat';

    const result = await centralizedAI.createChatCompletion(messages, {
      temperature: 1,
      presence_penalty: 0,
      top_p: 1,
      max_tokens: 256,
    }, useCase, usePortkey);

    return result;
  } catch (error) {
    console.error("Error Stack:", error.stack);
    throw new Error("Error fetching response from ChatGPT: " + error.message);
  }
}

async function main() {
  try {
    const systemPrompt = "You are a helpful assistant.";
    const userPrompt = "What is the capital of France?";

    const response = await getChatGPTResponse(systemPrompt, userPrompt);
    console.log("ChatGPT Response:", response.message);
    console.log("Token Usage:", {
      promptTokens: response.prompt_tokens,
      completionTokens: response.completion_tokens,
      totalTokens: response.total_tokens,
    });
  } catch (error) {
    console.error("Error:", error.message);
  }
}
module.exports = {
  getChatGPTResponse,
};
