#!/usr/bin/env node

/**
 * LiteLLM Proxy Test Script
 * 
 * This script tests the LiteLLM proxy running at http://**************:4000/
 * using the OpenAI library and direct HTTP requests.
 */

require('dotenv').config();
const OpenAI = require('openai');
const axios = require('axios');

// Configuration
const LITELLM_PROXY_URL = 'http://**************:4000';
// LiteLLM proxy expects virtual keys starting with 'sk-'
const TEST_API_KEY = process.env.LITELLM_API_KEY || process.env.OPENAI_API_KEY || 'sk-test-key-for-litellm-proxy';

function printTestHeader(title) {
    console.log('\n' + '='.repeat(60));
    console.log(`🧪 ${title}`);
    console.log('='.repeat(60));
}

function printTestResult(testName, success, details = '') {
    const icon = success ? '✅' : '❌';
    console.log(`${icon} ${testName}: ${success ? 'PASSED' : 'FAILED'}`);
    if (details) {
        console.log(`   ${details}`);
    }
}

async function testProxyHealth() {
    printTestHeader('LiteLLM Proxy Health Check');

    try {
        console.log(`🔄 Checking proxy health at ${LITELLM_PROXY_URL}/health...`);
        // Try health endpoint without auth first
        let response;
        try {
            response = await axios.get(`${LITELLM_PROXY_URL}/health`, {
                timeout: 10000
            });
        } catch (healthError) {
            // If health endpoint requires auth, try with API key
            console.log('Health endpoint requires auth, trying with API key...');
            response = await axios.get(`${LITELLM_PROXY_URL}/health`, {
                headers: {
                    'Authorization': `Bearer ${TEST_API_KEY}`
                },
                timeout: 10000
            });
        }

        printTestResult('Health Check', response.status === 200, `Status: ${response.status}`);
        console.log('Health Response:', response.data);
        return true;
    } catch (error) {
        printTestResult('Health Check', false, error.message);
        // Try a simple ping to see if the server is responding
        try {
            console.log('Trying basic connectivity test...');
            const pingResponse = await axios.get(`${LITELLM_PROXY_URL}`, { timeout: 5000 });
            console.log('Server is responding, but health endpoint may require different auth');
        } catch (pingError) {
            console.log('Server appears to be unreachable');
        }
        return false;
    }
}

async function testProxyModels() {
    printTestHeader('Available Models Check');
    
    try {
        console.log(`🔄 Fetching available models from ${LITELLM_PROXY_URL}/v1/models...`);
        const response = await axios.get(`${LITELLM_PROXY_URL}/v1/models`, {
            headers: {
                'Authorization': `Bearer ${TEST_API_KEY}`,
                'Content-Type': 'application/json'
            },
            timeout: 10000
        });
        
        const models = response.data;
        printTestResult('Models Endpoint', response.status === 200, `Found ${models.data?.length || 0} models`);
        
        if (models.data && models.data.length > 0) {
            console.log('Available Models:');
            models.data.slice(0, 5).forEach((model, index) => {
                console.log(`  ${index + 1}. ${model.id}`);
            });
            if (models.data.length > 5) {
                console.log(`  ... and ${models.data.length - 5} more models`);
            }
        }
        
        return models.data || [];
    } catch (error) {
        printTestResult('Models Check', false, error.message);
        return [];
    }
}

async function testOpenAILibrary() {
    printTestHeader('OpenAI Library Test');
    
    try {
        // Initialize OpenAI client with LiteLLM proxy
        const openai = new OpenAI({
            apiKey: TEST_API_KEY,
            baseURL: `${LITELLM_PROXY_URL}/v1`
        });
        
        console.log('🔄 Testing chat completion with OpenAI library...');
        
        const completion = await openai.chat.completions.create({
            model: 'gpt-3.5-turbo', // Common model that should be available
            messages: [
                {
                    role: 'system',
                    content: 'You are a helpful assistant. Respond concisely.'
                },
                {
                    role: 'user',
                    content: 'Say "LiteLLM proxy test successful!" and nothing else.'
                }
            ],
            max_tokens: 50,
            temperature: 0
        });
        
        const message = completion.choices[0]?.message?.content;
        const success = !!message;
        
        printTestResult('OpenAI Library Chat', success, `Response: "${message}"`);
        printTestResult('Response Format', !!completion.id, `ID: ${completion.id}`);
        printTestResult('Usage Tracking', !!completion.usage, `Tokens: ${completion.usage?.total_tokens || 'N/A'}`);
        
        return success;
    } catch (error) {
        printTestResult('OpenAI Library Test', false, error.message);
        return false;
    }
}

async function testDirectHTTPRequest() {
    printTestHeader('Direct HTTP Request Test');
    
    try {
        console.log('🔄 Testing direct HTTP request...');
        
        const requestData = {
            model: 'gpt-3.5-turbo',
            messages: [
                {
                    role: 'system',
                    content: 'You are a helpful assistant.'
                },
                {
                    role: 'user',
                    content: 'Respond with "Direct HTTP test successful!" and nothing else.'
                }
            ],
            max_tokens: 50,
            temperature: 0
        };
        
        const response = await axios.post(`${LITELLM_PROXY_URL}/v1/chat/completions`, requestData, {
            headers: {
                'Authorization': `Bearer ${TEST_API_KEY}`,
                'Content-Type': 'application/json'
            },
            timeout: 30000
        });
        
        const result = response.data;
        const message = result.choices?.[0]?.message?.content;
        const success = response.status === 200 && !!message;
        
        printTestResult('Direct HTTP Request', success, `Status: ${response.status}`);
        printTestResult('Response Content', !!message, `Response: "${message}"`);
        
        console.log('Full Response Structure:');
        console.log(JSON.stringify(result, null, 2));
        
        return success;
    } catch (error) {
        printTestResult('Direct HTTP Request', false, error.message);
        return false;
    }
}

async function testStreamingResponse() {
    printTestHeader('Streaming Response Test');
    
    try {
        console.log('🔄 Testing streaming response...');
        
        const openai = new OpenAI({
            apiKey: TEST_API_KEY,
            baseURL: `${LITELLM_PROXY_URL}/v1`
        });
        
        const stream = await openai.chat.completions.create({
            model: 'gpt-3.5-turbo',
            messages: [
                {
                    role: 'user',
                    content: 'Count from 1 to 5, each number on a new line.'
                }
            ],
            max_tokens: 50,
            temperature: 0,
            stream: true
        });
        
        let fullResponse = '';
        let chunkCount = 0;
        
        for await (const chunk of stream) {
            const content = chunk.choices[0]?.delta?.content || '';
            fullResponse += content;
            chunkCount++;
            if (content) {
                process.stdout.write(content);
            }
        }
        
        console.log(); // New line after streaming
        
        const success = chunkCount > 0 && fullResponse.length > 0;
        printTestResult('Streaming Response', success, `Received ${chunkCount} chunks`);
        printTestResult('Stream Content', fullResponse.length > 0, `Content length: ${fullResponse.length}`);
        
        return success;
    } catch (error) {
        printTestResult('Streaming Response', false, error.message);
        return false;
    }
}

async function testApiKeyFormats() {
    printTestHeader('API Key Format Testing');

    const apiKeyFormats = [
        { name: 'Original Key', key: process.env.OPENAI_API_KEY },
        { name: 'sk-test format', key: 'sk-test-key-for-litellm' },
        { name: 'sk-anything format', key: 'sk-anything' },
        { name: 'No auth', key: null }
    ];

    for (const format of apiKeyFormats) {
        if (!format.key && format.name !== 'No auth') continue;

        try {
            console.log(`🔄 Testing ${format.name}...`);

            const headers = {
                'Content-Type': 'application/json'
            };

            if (format.key) {
                headers['Authorization'] = `Bearer ${format.key}`;
            }

            const response = await axios.post(`${LITELLM_PROXY_URL}/v1/chat/completions`, {
                model: 'gpt-3.5-turbo',
                messages: [{ role: 'user', content: 'Hello' }],
                max_tokens: 10
            }, {
                headers,
                timeout: 10000
            });

            printTestResult(format.name, response.status === 200, 'Authentication successful');
            if (response.status === 200) {
                console.log(`✅ Working API key format: ${format.key ? format.key.substring(0, 10) + '...' : 'No auth required'}`);
                return format.key;
            }

        } catch (error) {
            const isAuthError = error.response?.status === 401;
            printTestResult(format.name, false, isAuthError ? 'Authentication failed' : error.message);
        }
    }

    return null;
}

async function testMultipleModels() {
    printTestHeader('Multiple Models Test');

    const modelsToTest = ['gpt-3.5-turbo', 'gpt-4', 'claude-3-sonnet-20240229'];
    let successCount = 0;

    const openai = new OpenAI({
        apiKey: TEST_API_KEY,
        baseURL: `${LITELLM_PROXY_URL}/v1`
    });

    for (const model of modelsToTest) {
        try {
            console.log(`🔄 Testing model: ${model}...`);

            const completion = await openai.chat.completions.create({
                model: model,
                messages: [
                    {
                        role: 'user',
                        content: `Say "Hello from ${model}!" and nothing else.`
                    }
                ],
                max_tokens: 20,
                temperature: 0
            });

            const message = completion.choices[0]?.message?.content;
            const success = !!message;

            printTestResult(`Model ${model}`, success, success ? `Response: "${message}"` : 'No response');

            if (success) successCount++;

        } catch (error) {
            printTestResult(`Model ${model}`, false, error.message);
        }
    }

    printTestResult('Multiple Models', successCount > 0, `${successCount}/${modelsToTest.length} models working`);
    return successCount > 0;
}

async function main() {
    console.log('🚀 Starting LiteLLM Proxy Tests');
    console.log(`📍 Proxy URL: ${LITELLM_PROXY_URL}`);
    console.log(`🔑 API Key: ${TEST_API_KEY ? TEST_API_KEY.substring(0, 10) + '...' : 'Not set'}\n`);

    // First, test different API key formats to find the working one
    const workingApiKey = await testApiKeyFormats();

    // Update the global API key if we found a working one
    if (workingApiKey && workingApiKey !== TEST_API_KEY) {
        console.log(`\n🔄 Updating API key for remaining tests...`);
        // Update the global variable for remaining tests
        global.TEST_API_KEY = workingApiKey;
    }

    const results = {
        health: await testProxyHealth(),
        apiKeyFormat: !!workingApiKey,
        models: (await testProxyModels()).length > 0,
        openaiLibrary: await testOpenAILibrary(),
        directHTTP: await testDirectHTTPRequest(),
        streaming: await testStreamingResponse(),
        multipleModels: await testMultipleModels(),
    };
    
    // Final summary
    printTestHeader('Test Summary');
    
    const totalTests = Object.keys(results).length;
    const passedTests = Object.values(results).filter(Boolean).length;
    const failedTests = totalTests - passedTests;
    
    console.log(`📊 Test Results: ${passedTests}/${totalTests} passed`);
    console.log(`✅ Passed: ${passedTests}`);
    console.log(`❌ Failed: ${failedTests}`);
    
    for (const [testName, passed] of Object.entries(results)) {
        printTestResult(testName.charAt(0).toUpperCase() + testName.slice(1), passed);
    }
    
    if (passedTests === totalTests) {
        console.log('\n🎉 All tests passed! Your LiteLLM proxy is working perfectly.');
        console.log('\n💡 Usage Examples:');
        console.log('   - OpenAI Library: new OpenAI({ baseURL: "http://**************:4000/v1" })');
        console.log('   - Direct HTTP: POST http://**************:4000/v1/chat/completions');
    } else {
        console.log('\n⚠️  Some tests failed. Please check the proxy configuration.');
    }
    
    process.exit(failedTests > 0 ? 1 : 0);
}

if (require.main === module) {
    main().catch(error => {
        console.error('❌ Test suite failed:', error);
        process.exit(1);
    });
}

module.exports = {
    testProxyHealth,
    testProxyModels,
    testOpenAILibrary,
    testDirectHTTPRequest,
    testStreamingResponse,
    testMultipleModels
};
