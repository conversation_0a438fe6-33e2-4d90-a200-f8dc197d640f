/**
 * List of most popular domain extensions
 */
const POPULAR_DOMAIN_EXTENSIONS = [
  '.com', '.org', '.net', '.co', '.io', '.ai', '.app',
  '.store', '.shop', '.company', '.business', '.us', '.uk', 
  '.ca', '.au', '.de', '.fr', '.jp', '.cn', '.in'
];

/**
 * Popular websites to prioritize in domain matching
 */
const POPULAR_DOMAINS = [
  'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'aol.com',
  'icloud.com', 'protonmail.com', 'mail.com', 'zoho.com',
  'gmx.com', 'yandex.com', 'tutanota.com'
];

/**
 * Filter URLs to prioritize popular domains
 * @param {string[]} urls - List of URLs to filter
 * @returns {string[]} - Filtered URLs prioritizing popular domains
 */
function filterPopularDomains(urls) {
  if (!urls || !Array.isArray(urls) || urls.length === 0) {
    return [];
  }
  
  return urls.sort((a, b) => {
    // Extract domains from URLs
    const domainA = extractDomainFromUrl(a);
    const domainB = extractDomainFromUrl(b);
    
    // Check for popular domain extensions
    const aHasPopularExt = POPULAR_DOMAIN_EXTENSIONS.some(ext => domainA.endsWith(ext));
    const bHasPopularExt = POPULAR_DOMAIN_EXTENSIONS.some(ext => domainB.endsWith(ext));
    
    if (aHasPopularExt && !bHasPopularExt) return -1;
    if (!aHasPopularExt && bHasPopularExt) return 1;
    
    // Check for known popular domains
    const aIsPopular = POPULAR_DOMAINS.some(domain => domainA.includes(domain));
    const bIsPopular = POPULAR_DOMAINS.some(domain => domainB.includes(domain));
    
    if (aIsPopular && !bIsPopular) return -1;
    if (!aIsPopular && bIsPopular) return 1;
    
    // Default to shorter domain names (often more established)
    return domainA.length - domainB.length;
  });
}

/**
 * Extract domain from a URL
 * @param {string} url - URL to extract domain from
 * @returns {string} - Extracted domain
 */
function extractDomainFromUrl(url) {
  try {
    // Handle URLs without protocol
    if (url && !url.startsWith('http') && !url.startsWith('https')) {
      url = 'https://' + url;
    }
    
    const urlObj = new URL(url);
    return urlObj.hostname.toLowerCase();
  } catch (error) {
    // Return the original string if it's not a valid URL
    console.warn(`Error extracting domain from ${url}: ${error.message}`);
    return url.toLowerCase();
  }
}

/**
 * Filter out broken or removed domains
 * @param {string[]} domains - List of domains to filter
 * @returns {Promise<string[]>} - Filtered domains
 */
async function filterRemovedDomains(domains) {
  const axios = require('axios');
  const validDomains = [];
  
  if (!domains || !Array.isArray(domains)) {
    return [];
  }
  
  for (const domain of domains) {
    try {
      const url = domain.startsWith('http') ? domain : `https://${domain}`;
      // Make a HEAD request with a short timeout to check domain availability
      await axios.head(url, { timeout: 5000 });
      validDomains.push(domain);
    } catch (error) {
      console.log(`Domain ${domain} appears to be unavailable: ${error.message}`);
    }
  }
  
  return validDomains;
}

module.exports = {
  POPULAR_DOMAIN_EXTENSIONS,
  POPULAR_DOMAINS,
  filterPopularDomains,
  extractDomainFromUrl,
  filterRemovedDomains
}; 