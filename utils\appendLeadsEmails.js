const prisma = require("../database/prisma/getPrismaClient");
async function appendLeadsEmails(action, data, clientName) {
  const response = {
    replySentiment: data?.category || null,
    campaignName: data?.campaign_name || null,
    email: data?.from_email || data?.lead_data?.email || null,
    clientName: data?.to_name || data?.client_id || null,
    createdAt: data?.last_reply?.time || data?.event_timestamp || null,
    emailsSent: [],
    replies: [],
  };

  if (!response?.email) {
    throw { status: 400, message: `Email is required` };
  }

  const prospects = await prisma.clientsProspectsMatching.findMany({
    where: {
      client: clientName,
      prospect: {
        email: response?.email || null,
      },
    },
    include: {
      prospect: true,
    },
  });

  if (!prospects || !prospects.length) {
    throw `Prospect not found for email ${response?.email}`;
  }

  if (action == "lead-category-updated") {
    for (let prospect of prospects) {
      await prisma.clientsProspectsMatching.update({
        where: {
          client: clientName,
          match_id: prospect.match_id,
          prospect_id: prospect.prospect.id,
        },
        data: {
          reply_sentiment: response.replySentiment,
          campaign_name: response.campaignName,
        },
      });
    }

    // await prisma.clientsProspectsMatching.update({
    //   where: {
    //     client: clientName,
    //     prospect_id: prospect.prospect[0].id,
    //   },
    //   data: {
    //     reply_sentiment: response.replySentiment,
    //     campaign_name: response.campaignName,
    //   }
    // });
  }

  if (action === "email-sent" && data?.sent_message) {
    for (let prospect of prospects) {
      await prisma.clientsProspectsMatching.update({
        where: {
          client: clientName,
          match_id: prospect.match_id,
          prospect_id: prospect.prospect.id,
        },
        data: {
          emails_sent: [
            ...(prospect.emails_sent || []),
            {
              text: data.sent_message.text || null,
              dateTime: data.sent_message.time || null,
            },
          ],
        },
      });
    }
  } else if (action === "email-replied" && data?.reply_message) {
    for (let prospect of prospects) {
      await prisma.clientsProspectsMatching.update({
        where: {
          client: clientName,
          prospect_id: prospect.prospect.id,
          match_id: prospect.match_id,
        },
        data: {
          replies: [
            ...(prospect.replies || []),
            {
              text: data.reply_message.text || null,
              dateTime: data.reply_message.time || null,
            },
          ],
        },
      });
    }
  }

  return response;
}

module.exports = { appendLeadsEmails };
