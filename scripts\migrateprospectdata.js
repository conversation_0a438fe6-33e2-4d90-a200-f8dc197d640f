const prisma = require("../database/prisma/getPrismaClient");

// Configuration
const BATCH_SIZE = 1000;
const MAX_TRANSACTION_SIZE = 500;

// Helper functions for data conversion
function convertToFloat(value) {
  if (value === null || value === undefined || value === "") return null;
  const num = parseFloat(value);
  return isNaN(num) ? null : num;
}

function convertToInt(value) {
  if (value === null || value === undefined || value === "") return null;
  const num = parseInt(value, 10);
  return isNaN(num) ? null : num;
}

function convertToDate(value) {
  if (value === null || value === undefined || value === "") return null;
  const date = new Date(value);
  return isNaN(date.getTime()) ? null : date;
}

/**
 * Compare two objects and return true if they have differences
 */
function hasChanges(existing, newData) {
  for (const key in newData) {
    if (key === 'id' || key === 'createdAt' || key === 'updatedAt') continue;
    
    // Handle null/undefined comparisons
    const existingValue = existing[key];
    const newValue = newData[key];
    
    if (existingValue !== newValue) {
      // Special handling for dates
      if (existingValue instanceof Date && newValue instanceof Date) {
        if (existingValue.getTime() !== newValue.getTime()) return true;
      } else if (existingValue !== newValue) {
        return true;
      }
    }
  }
  return false;
}

// Helper function to map company data to seller data
function mapCompanyToSellerData(company, marketplace, sellerGroupId) {
  return {
    name: company.name || null,
    amazon_seller_id: company.amazon_seller_id || null,
    marketplace: marketplace || null,
    // seller_group_id: sellerGroupId,
    primary_category: company.primary_category || null,
    primary_sub_category: company.primary_sub_category || null,
    estimate_sales: convertToFloat(company.estimate_sales),
    avg_price: convertToFloat(company.avg_price),
    percent_fba: convertToFloat(company.percent_fba),
    number_reviews_lifetime: convertToInt(company.number_reviews_lifetime),
    number_reviews_30days: convertToInt(company.number_reviews_30days),
    number_winning_brands: convertToInt(company.number_winning_brands),
    number_asins: convertToInt(company.number_asins),
    number_top_asins: convertToInt(company.number_top_asins),
    business_name: company.business_name || null,
    number_brands_1000: convertToInt(company.number_brands_1000),
    mom_growth: convertToFloat(company.mom_growth),
    mom_growth_count: convertToInt(company.mom_growth_count),
    started_selling_date: convertToDate(company.started_selling_date),
    // website: company.website || null,
    domain: company.domain || null,
    website_status: company.website_status || null,
    lookup_source: company.lookup_source || null,
    lookup_sources: company.lookup_sources || {},
    street: company.company_address || null,
    city: company.company_location || null,
    adr_state: company.state || null,
    adr_country: company.country || null,
    adr_zip_code: company.company_pincode || null,
  };
}

/**
 * Update existing sellers with latest company data
 */
async function updateSellersWithLatestData() {
  console.log("Starting update of sellers with latest company data...");

  // Preload ALL seller country matchings into memory
  const allSellerCountryMatchings = await prisma.sellerCountryMatching.findMany();
  const matchingsBySellerID = allSellerCountryMatchings.reduce((acc, match) => {
    if (!acc[match.amazon_seller_id]) acc[match.amazon_seller_id] = [];
    acc[match.amazon_seller_id].push(match);
    return acc;
  }, {});

  let skip = 0;
  let hasMore = true;
  let totalProcessed = 0;
  let totalUpdated = 0;
  let totalSkipped = 0;

  while (hasMore) {
    // Get a batch of companies ordered by updatedAt DESC to get the newest data first
    const companies = await prisma.company.findMany({
      skip,
      take: BATCH_SIZE,
      orderBy: { updatedAt: "desc" },
    });

    if (companies.length === 0) {
      hasMore = false;
      continue;
    }

    // Get all seller IDs for this batch
    const sellerIds = companies.map((c) => c.amazon_seller_id).filter(Boolean);

    // Get existing sellers
    const existingSellers = await prisma.amazonSeller.findMany({
      where: { amazon_seller_id: { in: sellerIds } },
    });

    // Create a lookup map for existing sellers
    const existingSellerMap = new Map();
    existingSellers.forEach((seller) => {
      const key = `${seller.amazon_seller_id}_${seller.marketplace}`;
      existingSellerMap.set(key, seller);
    });

    // Find sellers that need updates
    const updateSellerData = [];

    for (const company of companies) {
      const countryMatches = matchingsBySellerID[company.amazon_seller_id] || [];
      
      for (const match of countryMatches) {
        const key = `${company.amazon_seller_id}_${match.smartscout_country}`;
        const existingSeller = existingSellerMap.get(key);
        
        if (existingSeller) {
          // Check if existing seller needs update with latest company data
          const newSellerData = mapCompanyToSellerData(
            company,
            match.smartscout_country,
            existingSeller.seller_group_id
          );
          
          if (hasChanges(existingSeller, newSellerData)) {
            updateSellerData.push({
              id: existingSeller.id,
              data: newSellerData,
            });
          }
        }
      }
    }

    // Update existing sellers with latest data
    if (updateSellerData.length > 0) {
      for (let i = 0; i < updateSellerData.length; i += MAX_TRANSACTION_SIZE) {
        const batch = updateSellerData.slice(i, i + MAX_TRANSACTION_SIZE);
        
        await prisma.$transaction(
          batch.map(item => 
            prisma.amazonSeller.update({
              where: { id: item.id },
              data: item.data,
            })
          )
        );
      }
      totalUpdated += updateSellerData.length;
    }

    totalProcessed += companies.length;
    totalSkipped += companies.length - updateSellerData.length;

    console.log(
      `Processed ${totalProcessed} companies, Updated ${totalUpdated} sellers, Skipped ${totalSkipped} unchanged sellers`
    );
    skip += BATCH_SIZE;
  }

  console.log(`Seller update completed! Total processed: ${totalProcessed}, Total updated: ${totalUpdated}, Total skipped: ${totalSkipped}`);
}

/**
 * Update existing amazon prospects with latest prospect data
 */
async function updateProspectsWithLatestData() {
  console.log("Starting update of amazon prospects with latest prospect data...");

  let skip = 0;
  let hasMore = true;
  let totalProcessed = 0;
  let totalUpdated = 0;
  let totalSkipped = 0;

  while (hasMore) {
    // Fetch a batch of prospects ordered by updatedAt DESC to get the newest data first
    const prospects = await prisma.prospect.findMany({
      skip,
      take: BATCH_SIZE,
      include: {
        clientsProspects: true,
        sellerGroup: true,
      },
      orderBy: {
        updatedAt: "desc",
      },
    });

    if (prospects.length === 0) {
      hasMore = false;
      continue;
    }

    // Get unique identifiers for checking existence
    const uniqueEmails = [...new Set(prospects.map((p) => p.email).filter(Boolean))];
    const uniqueLinkedIns = [...new Set(prospects.map((p) => p.person_linkedin).filter(Boolean))];

    // Get existing amazon prospects
    const existingAmazonProspects = await prisma.amazonProspect.findMany({
      where: {
        OR: [
          { email: { in: uniqueEmails } },
          { person_linkedin: { in: uniqueLinkedIns } },
        ],
      },
    });

    // Create lookup maps
    const existingByEmail = new Map();
    const existingByLinkedIn = new Map();
    
    existingAmazonProspects.forEach(prospect => {
      if (prospect.email) existingByEmail.set(prospect.email, prospect);
      if (prospect.person_linkedin) existingByLinkedIn.set(prospect.person_linkedin, prospect);
    });

    // Find prospects that need updates
    const updateProspects = [];

    prospects.forEach(prospect => {
      let existingProspect = null;
      
      // Find existing prospect by email or LinkedIn
      if (prospect.email && existingByEmail.has(prospect.email)) {
        existingProspect = existingByEmail.get(prospect.email);
      } else if (prospect.person_linkedin && existingByLinkedIn.has(prospect.person_linkedin)) {
        existingProspect = existingByLinkedIn.get(prospect.person_linkedin);
      }

      if (existingProspect) {
        // Check if existing prospect needs update with latest data
        const newProspectData = {
          person_name: prospect.person_name,
          person_linkedin: prospect.person_linkedin,
          email: prospect.email,
          job_title: prospect.job_title,
          source: prospect.source,
          sources: prospect.sources,
          email_status: prospect.email_status,
          seller_group_id: prospect.seller_group_id,
        };

        if (hasChanges(existingProspect, newProspectData)) {
          updateProspects.push({
            id: existingProspect.id,
            data: newProspectData,
          });
        }
      }
    });

    // Update existing amazon prospects with latest data
    if (updateProspects.length > 0) {
      for (let i = 0; i < updateProspects.length; i += MAX_TRANSACTION_SIZE) {
        const batch = updateProspects.slice(i, i + MAX_TRANSACTION_SIZE);
        
        await prisma.$transaction(
          batch.map(item => 
            prisma.amazonProspect.update({
              where: { id: item.id },
              data: item.data,
            })
          )
        );
      }
      totalUpdated += updateProspects.length;
    }

    totalProcessed += prospects.length;
    totalSkipped += prospects.length - updateProspects.length;

    console.log(
      `Processed ${totalProcessed} prospects, Updated ${totalUpdated} amazon prospects, Skipped ${totalSkipped} unchanged prospects`
    );
    skip += BATCH_SIZE;
  }

  console.log(`Prospect update completed! Total processed: ${totalProcessed}, Total updated: ${totalUpdated}, Total skipped: ${totalSkipped}`);
}

/**
 * Update both sellers and prospects with latest data
 */
async function updateAllWithLatestData() {
  console.log("Starting update of all records with latest data...");
  
  await updateSellersWithLatestData();
  await updateProspectsWithLatestData();
  
  console.log("All updates completed!");
}

// Main function to run the updates
async function main() {
  try {
    const args = process.argv.slice(2);
    const command = args[0];

    switch (command) {
      case "update-sellers":
        await updateSellersWithLatestData();
        break;
      case "update-prospects":
        await updateProspectsWithLatestData();
        break;
      case "update-all":
        await updateAllWithLatestData();
        break;
      default:
        console.log("Usage: node updateLatestData.js [command]");
        console.log("Commands:");
        console.log("  update-sellers     - Update sellers with latest company data");
        console.log("  update-prospects   - Update amazon prospects with latest prospect data");
        console.log("  update-all         - Update both sellers and prospects with latest data");
        process.exit(1);
    }
  } catch (error) {
    console.error("Update failed:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the update if this file is executed directly
if (require.main === module) {
  main();
}

// Export functions for testing or use in other modules
module.exports = {
  updateSellersWithLatestData,
  updateProspectsWithLatestData,
  updateAllWithLatestData,
};