const express = require("express");
const router = express.Router();
// const { runJobUpdateWorker } = require("../services/generateLeads/workers/jobUpdateWorker");

// Import user routes
const userRoutes = require("./user");
router.use(userRoutes);

// Import job routes
const jobRoutes = require("./job");
router.use(jobRoutes);

// Import validation routes
const validationRoutes = require("./lead");
router.use(validationRoutes);

// Import lead queue routes
const leadQueueRoutes = require("./leadQueue");
router.use(leadQueueRoutes);

//Adhoc routes
const adhocRoutes = require("./adHoc");
router.use(adhocRoutes);

// Import FuzzySearch routes
const fuzzySearch = require("./fuzzysearch");
router.use(fuzzySearch);

// Import DNS Records routes
const dnsRecords = require("./DnsRecords");
router.use(dnsRecords);

// Import MX Records routes
const mxRecords = require("./mxRecords");
router.use("/mx", mxRecords);

const smartleads = require("./smartleads");
router.use(smartleads);

// Import Metabase CSV routes
const metabaseCSV = require("./metabaseCSV");
router.use(metabaseCSV);

// Import Health Check routes
const healthRoutes = require("./health");
router.use(healthRoutes);

// Import Portkey Demo routes
const portkeyDemo = require("./portkeyDemo");
router.use(portkeyDemo);

module.exports = router;
