require("dotenv").config({ path: "../.env" });
const prisma = require("../database/prisma/getPrismaClient");
const axios = require("axios");
const fs = require("fs");
const path = require("path");

// Command-line argument processing
const args = process.argv.slice(2);
const VALID_MODES = ['inboxes', 'meetings'];
const mode = args[0];

// Check if mode is provided
if (!mode) {
  console.error(`❌ Error: Mode must be specified`);
  console.error(`Usage: node syncEmailTags.js [mode]`);
  console.error(`Available modes: ${VALID_MODES.join(', ')}`);
  process.exit(1);
}

// Check if mode is valid
if (!VALID_MODES.includes(mode)) {
  console.error(`❌ Error: Invalid mode '${mode}'`);
  console.error(`Available modes: ${VALID_MODES.join(', ')}`);
  process.exit(1);
}

/**
 * Fetches inbox tags data from the CRM API
 * @returns {Promise<Array>} Array of inbox objects with associated tags
 */
async function fetchInboxTags() {
  try {
    console.log("📥 Fetching inbox tags from API...");
    const response = await axios.get("https://jeffcrm.equalcollective.com/api/public/inbox-tags");
    console.log(`✅ Successfully fetched data for ${response.data.length} inboxes`);
    return response.data;
  } catch (error) {
    console.error("❌ Error fetching inbox tags:", error.message);
    throw error;
  }
}

/**
 * Ensures that the required database tables exist
 */
async function ensureTablesExist() {
  try {
    console.log("🔧 Checking and creating required database tables...");
    
    // Check if InboxesFromReplit table exists, create if it doesn't
    await prisma.$executeRawUnsafe(`
      CREATE TABLE IF NOT EXISTS "public2"."InboxesFromReplit" (
        "id" SERIAL PRIMARY KEY,
        "inboxEmail" TEXT UNIQUE NOT NULL,
        "provider_name" TEXT
      )
    `);
    
    // Check if InboxTags table exists, create if it doesn't
    await prisma.$executeRawUnsafe(`
      CREATE TABLE IF NOT EXISTS "public2"."InboxTagsFromReplit" (
        "id" SERIAL PRIMARY KEY,
        "inboxId" INTEGER NOT NULL,
        "tag" TEXT NOT NULL,
        "tagId" INTEGER,
        UNIQUE("inboxId", "tag"),
        FOREIGN KEY ("inboxId") REFERENCES "public2"."InboxesFromReplit"("id") ON DELETE CASCADE
      )
    `);
    
    console.log("✅ Tables verified and created if needed");
  } catch (error) {
    console.error("❌ Error ensuring tables exist:", error.message);
    throw error;
  }
}

/**
 * Truncates both database tables
 */
async function truncateTables() {
  try {
    console.log("🗑️ Truncating existing tables...");
    
    // Truncate InboxTags first due to foreign key constraints
    await prisma.$executeRawUnsafe(`TRUNCATE TABLE "public2"."InboxTagsFromReplit" CASCADE`);
    await prisma.$executeRawUnsafe(`TRUNCATE TABLE "public2"."InboxesFromReplit" CASCADE`);
    
    console.log("✅ Tables successfully truncated");
  } catch (error) {
    console.error("❌ Error truncating tables:", error.message);
    throw error;
  }
}

/**
 * Inserts inbox data into the database
 * @param {Array} inboxes Array of inbox objects from the API
 */
async function insertInboxData(inboxes) {
  try {
    console.log(`📝 Inserting ${inboxes.length} inboxes into database...`);
    
    // For mapping emails to their database IDs
    const inboxIdMap = {};
    // For collecting all tags that need to be inserted
    const allTags = [];
    
    // STEP 1: Insert all inboxes in batches
    const INBOX_BATCH_SIZE = 500;
    console.log(`📩 Preparing to insert inboxes in batches of ${INBOX_BATCH_SIZE}...`);
    
    // Process inboxes in batches
    for (let i = 0; i < inboxes.length; i += INBOX_BATCH_SIZE) {
      const batchInboxes = inboxes.slice(i, i + INBOX_BATCH_SIZE);
      console.log(`🔄 Inserting inbox batch ${Math.floor(i / INBOX_BATCH_SIZE) + 1} of ${Math.ceil(inboxes.length / INBOX_BATCH_SIZE)}...`);
      
      // Create arrays to hold batch data
      const inboxesToInsert = [];
      const emailAddresses = [];
      
      // Gather all emails and provider name for this batch
      batchInboxes.forEach(inbox => {
        // Extract only provider name from domain.currentProvider if available
        const provider_name = inbox.domain?.currentProvider?.name || null;
        
        inboxesToInsert.push({
          email: inbox.email,
          provider_name
        });
        emailAddresses.push(inbox.email);
      });
      
      try {
        // Create placeholders for values insertion
        const placeholders = [];
        const values = [];
        let paramIndex = 1;
        
        inboxesToInsert.forEach(inbox => {
          placeholders.push(`($${paramIndex}, $${paramIndex+1})`);
          values.push(inbox.email, inbox.provider_name);
          paramIndex += 2;
        });
        
        // Insert all emails with provider name in this batch and get their IDs
        const query = `
          WITH inserted AS (
            INSERT INTO "public2"."InboxesFromReplit" ("inboxEmail", "provider_name")
            VALUES ${placeholders.join(', ')}
            ON CONFLICT ("inboxEmail") DO UPDATE SET 
              "provider_name" = EXCLUDED."provider_name"
            RETURNING "id", "inboxEmail"
          )
          SELECT "id", "inboxEmail" FROM inserted
        `;
        
        const results = await prisma.$queryRawUnsafe(query, ...values);
        
        // Map the returned IDs to their emails
        for (const result of results) {
          inboxIdMap[result.inboxEmail] = result.id;
        }
        
        // Now collect tags for each inbox in this batch
        for (const inbox of batchInboxes) {
          if (inbox.tags && inbox.tags.length > 0) {
            const inboxId = inboxIdMap[inbox.email];
            if (inboxId) {
              for (const tag of inbox.tags) {
                allTags.push({
                  inboxId,
                  tagName: tag.name,
                  tagId: tag.tagId
                });
              }
            }
          }
        }
      } catch (error) {
        console.error(`⚠️ Error inserting inbox batch:`, error.message);
        // Continue with next batch
      }
    }
    
    console.log(`✅ Inserted ${Object.keys(inboxIdMap).length} inboxes in batches`);
    
    // STEP 2: Insert all tags in batches
    console.log(`🏷️ Preparing to insert ${allTags.length} tags in batches...`);
    
    if (allTags.length > 0) {
      const TAG_BATCH_SIZE = 500;
      
      for (let i = 0; i < allTags.length; i += TAG_BATCH_SIZE) {
        const batch = allTags.slice(i, i + TAG_BATCH_SIZE);
        console.log(`🔄 Inserting tag batch ${Math.floor(i / TAG_BATCH_SIZE) + 1} of ${Math.ceil(allTags.length / TAG_BATCH_SIZE)}...`);
        
        // Build batch insert query
        const placeholders = [];
        const values = [];
        let paramIndex = 1;
        
        batch.forEach(tagData => {
          placeholders.push(`($${paramIndex}, $${paramIndex + 1}, $${paramIndex + 2})`);
          values.push(tagData.inboxId, tagData.tagName, tagData.tagId);
          paramIndex += 3;
        });
        
        const query = `
          INSERT INTO "public2"."InboxTagsFromReplit" ("inboxId", "tag", "tagId") 
          VALUES ${placeholders.join(', ')} 
          ON CONFLICT ("inboxId", "tag") DO NOTHING
        `;
        
        try {
          await prisma.$executeRawUnsafe(query, ...values);
        } catch (error) {
          console.error(`⚠️ Error inserting tag batch:`, error.message);
          // Continue with next batch
        }
      }
      
      console.log(`✅ Successfully inserted ${allTags.length} tags in batches`);
    } else {
      console.log(`ℹ️ No tags to insert`);
    }
    
    console.log("✅ Successfully inserted all inbox and tag data");
    return inboxIdMap;
  } catch (error) {
    console.error("❌ Error inserting inbox data:", error.message);
    throw error;
  }
}

/**
 * Main function to synchronize inbox tags
 */
async function syncInboxTags() {
  try {
    console.log("🔄 Starting inbox tags sync...");
    
    // Fetch inbox tags from API
    const inboxes = await fetchInboxTags();
    
    // Ensure required tables exist
    await ensureTablesExist();
    
    // Truncate existing tables
    await truncateTables();
    
    // Insert inbox data into database
    await insertInboxData(inboxes);
    
    console.log("✅ inbox tags sync completed successfully!");
    return inboxes; // Return for potential further processing
  } catch (error) {
    console.error("❌ Inbox tags synchronization failed:", error.message);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * Extracts the fromEmailId from email history by finding the earliest SENT email
 * @param {Array} emailHistory Array of email history objects
 * @returns {String} The from email address of the sender or 'unknown' if not found
 */
function extractFromEmailId(emailHistory) {
  try {
    if (!emailHistory || !Array.isArray(emailHistory) || emailHistory.length === 0) {
      return 'unknown';
    }
    
    // Filter emails of type SENT
    const sentEmails = emailHistory.filter(email => email.type === 'SENT');
    
    if (sentEmails.length === 0) {
      return 'unknown';
    }
    
    // Find the earliest email based on the time field
    // Sort by time (null values should be last)
    const sortedEmails = sentEmails.sort((a, b) => {
      if (!a.time) return 1;
      if (!b.time) return -1;
      return new Date(a.time) - new Date(b.time);
    });
    
    // Use the 'from' field from the earliest email
    const fromEmail = sortedEmails[0]?.from || 'unknown';
    
    return fromEmail;
  } catch (error) {
    console.error(`Error extracting fromEmailId: ${error.message}`);
    return 'unknown';
  }
}

/**
 * Fetches meetings data from the CRM API
 * @returns {Promise<Array>} Array of meeting objects
 */
async function fetchMeetings() {
  try {
    console.log("📥 Fetching meetings from API...");
    const response = await axios.get("https://jeffcrm.equalcollective.com/api/public/meetings");
    console.log(`✅ Successfully fetched ${response.data.length} meetings`);
    return response.data;
  } catch (error) {
    console.error("❌ Error fetching meetings:", error.message);
    throw error;
  }
}

/**
 * Ensures that the meetings table exists
 */
async function ensureMeetingsTableExists() {
  try {
    console.log("🔧 Checking and creating meetings table...");
    
    // Check if MeetingsFromReplit table exists, create if it doesn't
    await prisma.$executeRawUnsafe(`
      CREATE TABLE IF NOT EXISTS "public2"."MeetingsFromReplit" (
        "id" SERIAL PRIMARY KEY,
        "date" DATE NOT NULL,
        "client" INTEGER NOT NULL,
        "funnel" TEXT NOT NULL,
        "fromEmailId" TEXT NOT NULL,
        "slots_sent_after_1" INTEGER DEFAULT 0,
        "slots_sent_after_2" INTEGER DEFAULT 0,
        "slots_sent_after_3" INTEGER DEFAULT 0,
        "slots_sent_unknown" INTEGER DEFAULT 0,
        "booked" INTEGER DEFAULT 0
      )
    `);
    
    console.log("✅ Meetings table verified and created if needed");
  } catch (error) {
    console.error("❌ Error ensuring meetings table exists:", error.message);
    throw error;
  }
}

/**
 * Truncates the meetings table
 */
async function truncateMeetingsTable() {
  try {
    console.log("🗑️ Truncating meetings table...");
    await prisma.$executeRawUnsafe(`TRUNCATE TABLE "public2"."MeetingsFromReplit" CASCADE`);
    console.log("✅ Meetings table successfully truncated");
  } catch (error) {
    console.error("❌ Error truncating meetings table:", error.message);
    throw error;
  }
}

/**
 * Processes meeting data and organizes it by date, client, and funnel
 * @param {Array} meetings Array of meeting objects from the API
 * @returns {Object} Aggregated meeting data by date, client, and funnel
 */
async function processMeetingsData(meetings) {
  try {
    console.log(`📊 Processing ${meetings.length} meetings...`);
    
    // Object to hold aggregated data
    const aggregatedData = {};
    
    // Process each meeting
    for (const meeting of meetings) {
      console.log(`Processing meeting ID: ${meeting.id || 'UNK'}`);
      
      // Extract stage history
      const stageHistory = meeting.stageHistory || [];
      // Extract email history
      const emailHistory = meeting.emailHistory || [];
      
      // Extract fromEmailId
      const fromEmailId = extractFromEmailId(emailHistory);
      
      // Get client ID
      const clientId = meeting.client?.smartleadId || "UNK";
      
      // Get funnel
      const funnel = meeting.campaignCategory?.code || "UNK";

      
      // Log email history for diagnosis
      if (emailHistory.length > 0) {
        console.log(`Meeting ${meeting.id} has ${emailHistory.length} email history records. Funnel - ${funnel}`);
        console.log('Email sequence numbers:', emailHistory.map(e => e.email_seq_number || 'undefined').join(', '));
      }

      // Determine max email sequence number - must convert to number as it could be a string
      let maxEmailSeq = 0;
      for (const emailEvent of emailHistory) {
        if (emailEvent.email_seq_number) {
          // Parse the sequence number as it might be a string
          const seqNum = parseInt(emailEvent.email_seq_number, 10);
          if (!isNaN(seqNum) && seqNum > maxEmailSeq) {
            maxEmailSeq = seqNum;
          }
        }
      }
      
      console.log(`Meeting ${meeting.id}: Max email sequence = ${maxEmailSeq}`);
      
      // Process stage history
      for (const stageChange of stageHistory) {
        const stage = stageChange.stage;
        const changedAt = stageChange.changedAt;
        
        if (!changedAt) continue;
        
        // Extract date (YYYY-MM-DD)
        const dateStr = changedAt.split('T')[0];
        
        // Create key for this date, client, funnel, and fromEmailId combination
        const key = `${dateStr}|${clientId}|${funnel}|${fromEmailId}`;
        
        // Create entry if not exists, or update counts
        if (!aggregatedData[key]) {
          aggregatedData[key] = {
            date: dateStr,
            client: parseInt(clientId, 10) || 0, // Convert client to integer
            funnel: funnel,
            fromEmailId: fromEmailId,
            slots_sent_after_1: 0,
            slots_sent_after_2: 0,
            slots_sent_after_3: 0,
            slots_sent_unknown: 0,
            booked: 0
          };
        }
        
        // Count different stages
        if (stage === 'slots_sent') {
          if (maxEmailSeq === 1) {
            aggregatedData[key].slots_sent_after_1++;
          } else if (maxEmailSeq === 2) {
            aggregatedData[key].slots_sent_after_2++;
          } else if (maxEmailSeq === 3) {
            aggregatedData[key].slots_sent_after_3++;
          } else {
            aggregatedData[key].slots_sent_unknown++;
          }
        } else if (stage === 'booked') {
          aggregatedData[key].booked++;
        }
      }
    }
    
    // Convert to array for easier database insertion
    const result = Object.values(aggregatedData);
    console.log(`✅ Processed ${result.length} unique date-client-funnel-fromEmailId combinations`);
    return result;
  } catch (error) {
    console.error("❌ Error processing meetings data:", error.message);
    throw error;
  }
}

/**
 * Inserts processed meeting data into the database
 * @param {Array} meetingData Array of processed meeting data objects
 */
async function insertMeetingsData(meetingData) {
  try {
    console.log(`📝 Inserting ${meetingData.length} meeting records into database...`);
    
    // Process in batches for better performance
    const BATCH_SIZE = 500;
    
    for (let i = 0; i < meetingData.length; i += BATCH_SIZE) {
      const batch = meetingData.slice(i, i + BATCH_SIZE);
      console.log(`🔄 Inserting batch ${Math.floor(i / BATCH_SIZE) + 1} of ${Math.ceil(meetingData.length / BATCH_SIZE)}...`);
      
      // Build batch insert query
      const placeholders = [];
      const values = [];
      let paramIndex = 1;
      
      batch.forEach(record => {
        // Use CAST to convert string date to PostgreSQL DATE type
        placeholders.push(`(CAST($${paramIndex} AS DATE), $${paramIndex + 1}, $${paramIndex + 2}, $${paramIndex + 3}, $${paramIndex + 4}, $${paramIndex + 5}, $${paramIndex + 6}, $${paramIndex + 7}, $${paramIndex + 8})`);
        values.push(
          record.date, // Will be cast to DATE in the query
          record.client,
          record.funnel,
          record.fromEmailId,
          record.slots_sent_after_1,
          record.slots_sent_after_2,
          record.slots_sent_after_3,
          record.slots_sent_unknown,
          record.booked
        );
        paramIndex += 9;
      });
      
      const query = `
        INSERT INTO "public2"."MeetingsFromReplit" 
        ("date", "client", "funnel", "fromEmailId", "slots_sent_after_1", "slots_sent_after_2", "slots_sent_after_3", "slots_sent_unknown", "booked") 
        VALUES ${placeholders.join(', ')}
      `;
      
      await prisma.$executeRawUnsafe(query, ...values);
    }
    
    console.log("✅ Successfully inserted all meeting data");
  } catch (error) {
    console.error("❌ Error inserting meeting data:", error.message);
    throw error;
  }
}

/**
 * Updates meeting analytics data by aggregating meeting information
 * and inserts it into the database
 */
async function updateMeetingAnalytics() {
  try {
    console.log("🔄 Starting meeting analytics update...");
    
    // Fetch meetings from API
    const meetingsData = await fetchMeetings();
    console.log("💾 Successfully fetched meeting data from API.");
    
    // Process meeting data
    const processedData = await processMeetingsData(meetingsData);
    
    // Output the processed data sample
    console.log("📃 Processed meeting data sample (first 5 rows):");
    console.log(JSON.stringify(processedData.slice(0, 5), null, 2));
    console.log(`Total rows to be inserted: ${processedData.length}`);
    
    // Write processed data to JSON file for backup
    const outputFilePath = path.join(__dirname, "meetingsData.json");
    fs.writeFileSync(outputFilePath, JSON.stringify(processedData, null, 2));
    console.log(`💾 Meeting data backed up to: ${outputFilePath}`);
    
    // Ensure necessary tables exist
    await ensureMeetingsTableExists();
    
    // Truncate existing data
    await truncateMeetingsTable();
    
    // Insert processed data into the database
    await insertMeetingsData(processedData);
    
    console.log("✅ Meeting analytics update completed successfully!");
    return processedData;
  } catch (error) {
    console.error("❌ Meeting analytics update failed:", error.message);
    throw error;
  }
}

/**
 * Main function that determines which mode to run
 */
async function main() {
  console.log(`🚀 Running in '${mode}' mode...`);
  
  try {
    if (mode === 'inboxes') {
      await syncInboxTags();
    } else if (mode === 'meetings') {
      await updateMeetingAnalytics();
    } else {
      throw new Error(`Unsupported mode: ${mode}`);
    }
    console.log(`✅ ${mode} sync completed successfully!`);
  } catch (error) {
    console.error(`❌ ${mode} sync failed:`, error.message);
    throw error;
  }
}

// Execute the main function

(async () => {
  try {
    await main();
  } catch (error) {
    console.error("❌ Fatal error:", error);
    process.exit(1);
  }
})();