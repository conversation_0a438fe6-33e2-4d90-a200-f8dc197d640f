// dnsProcessor.js
const dns = require("dns").promises;
const https = require("https");
const http = require("http");
const { parse } = require("csv-parse/sync");
const { stringify } = require("csv-stringify/sync");
const fs = require("fs");
const path = require("path");
const { parse: parseDomain } = require("tldts");

async function processCsvData(csvData, jobId, updateJobFn) {
  try {
    // Create exports directory if it doesn't exist
    const exportsDir = path.join(__dirname, "..", "..", "exports");
    if (!fs.existsSync(exportsDir)) {
      fs.mkdirSync(exportsDir, { recursive: true });
    }

    // Parse input CSV
    const inputRecords = parse(csvData, {
      columns: true,
      skip_empty_lines: true,
    });

    if (inputRecords.length === 0) {
      await updateJobFn(jobId, {
        status: "failed",
        error: "No records found in CSV data",
      });
      return null;
    }

    // Update job with total domains
    await updateJobFn(jobId, {
      totalDomains: inputRecords.length,
      status: "in_progress",
    });

    const originalColumns = Object.keys(inputRecords[0]);
    const domainNameColumn = originalColumns[0]; // Assuming first column contains domains

    const newColumns = [
      "Final Redirect Location",
      "MX",
      "SPF",
      "DKIM",
      "DMARC",
      "PTR",
      "Last Check At",
    ];
    const allColumns = [...originalColumns, ...newColumns];

    // Create output file
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const outputFileName = `domainProcessed_${jobId}_${timestamp}.csv`;
    const outputPath = path.join(exportsDir, outputFileName);

    // Write header row
    const headerRow = stringify([{}], { header: true, columns: allColumns });
    fs.writeFileSync(outputPath, headerRow);

    // Update job with output path
    await updateJobFn(jobId, { outputPath });

    // Process each domain one at a time
    let processedCount = 0;
    for (let i = 0; i < inputRecords.length; i++) {
      const record = inputRecords[i];
      const domain = record[domainNameColumn];

      console.log(
        `Processing domain ${i + 1}/${inputRecords.length}: ${domain}`
      );

      // Process the single domain
      const processedRecord = await processSingleDomain(
        record,
        domain,
        domainNameColumn
      );

      // Append the processed record to the output file
      const recordCsv = stringify([processedRecord], {
        header: false,
        columns: allColumns,
      });

      fs.appendFileSync(outputPath, recordCsv);

      // Update progress
      processedCount++;
      const progress = Math.round((processedCount / inputRecords.length) * 100);
      await updateJobFn(jobId, {
        progress,
        processedDomains: processedCount,
      });
    }

    // Mark job as completed
    await updateJobFn(jobId, {
      status: "completed",
      progress: 100,
    });

    console.log(
      `Processing complete. ${processedCount} domains processed. Results saved to ${outputPath}`
    );
    return outputPath;
  } catch (error) {
    console.error("Error processing CSV:", error);
    await updateJobFn(jobId, { status: "failed", error: error.message });
    return null;
  }
}

async function processSingleDomain(record, domain, domainNameColumn) {
  // Initialize with default values
  const outputRecord = {
    ...record,
    "Final Redirect Location": "N/A",
    MX: "N/A",
    SPF: "N/A",
    DKIM: "N/A",
    DMARC: "N/A",
    PTR: "N/A",
    "Last Check At": new Date().toLocaleString(),
  };

  if (!domain || typeof domain !== "string") {
    return outputRecord;
  }

  try {
    // Clean domain name (remove protocols if present)
    const cleanDomain = domain?.trim()
      ? domain.replace(/^https?:\/\//, "").replace(/\/.*$/, "")
      : "";

    // Add validation for the cleaned domain
    if (
      !cleanDomain ||
      !/^[a-zA-Z0-9][a-zA-Z0-9-_.]+\.[a-zA-Z]{2,}$/.test(cleanDomain)
    ) {
      console.log(`Invalid domain format: ${domain}`);
      return outputRecord;
    }

    // Get MX records
    try {
      const mxRecords = await dns.resolveMx(cleanDomain);
      // console.log(`MX records for domain: ${domain} is : ${JSON.stringify(mxRecords)}`);
      outputRecord["MX"] = mxRecords
        .map((r) => `${r.exchange} (${r.priority})`)
        .join(", ");
    } catch (err) {
      console.log(`No MX records for ${cleanDomain}`);
    }

    // Get TXT records for SPF, DKIM, DMARC
    try {
      const txtRecords = await dns.resolveTxt(cleanDomain);
      const flatTxtRecords = txtRecords.map((r) => r.join(""));
      // console.log(`TXT records for domain: ${domain} is : ${JSON.stringify(flatTxtRecords)}`);

      const spfRecord = flatTxtRecords.find((txt) => txt.startsWith("v=spf1"));
      if (spfRecord) outputRecord["SPF"] = spfRecord;

      // DKIM checking with multiple selectors
      const dkimSelectors = [
        "default",
        "google",
        "selector1",
        "selector2",
        "krs",
        "mail",
        "k1",
        "dkim",
        "domainkey",
        "email",
        "mandrill",
        "mailing",
        "key1",
        "emailkey",
        "amazonses",
        "sendgrid",
        "mailjet",
        "cm",
        "dk",
        "postmark",
        "smtpapi",
        "mailgun",
        "mx1",
        "em",
        "smtp",
        "dkim1",
        "dkim2",
        "zoho",
      ];

      let dkimFound = false;

      // Function to check DKIM for a specific selector
      async function checkDKIM(selector) {
        try {
          const selectorDomain = `${selector}._domainkey.${cleanDomain}`;
          const dkimRecords = await dns.resolveTxt(selectorDomain);
          const flatDkimRecords = dkimRecords.map((r) => r.join(""));
          // console.log(`DKIM records for ${selectorDomain}: ${JSON.stringify(flatDkimRecords)}`);

          const dkimRecord = flatDkimRecords.find((txt) =>
            txt.includes("v=DKIM1")
          );
          if (dkimRecord) {
            outputRecord["DKIM"] = `${selector}: ${dkimRecord}`;
            return true;
          }
          return false;
        } catch (err) {
          return false;
        }
      }

      // Try each selector until we find a DKIM record
      for (const selector of dkimSelectors) {
        if (await checkDKIM(selector)) {
          dkimFound = true;
          break;
        }
      }

      // DMARC checking
      try {
        // Parse the domain to check both current domain and org root
        const parsedDomain = parseDomain(cleanDomain);
        const rootDomain =
          parsedDomain.domain + "." + parsedDomain.publicSuffix;
        const targets = parsedDomain.subdomain
          ? [`_dmarc.${cleanDomain}`, `_dmarc.${rootDomain}`]
          : [`_dmarc.${cleanDomain}`];

        let dmarcFound = false;

        // Try each possible DMARC location
        for (const target of targets) {
          try {
            const dmarcRecords = await dns.resolveTxt(target);
            const flatDmarcRecords = dmarcRecords.map((r) => r.join(""));
            // console.log(`DMARC records for ${target}: ${JSON.stringify(flatDmarcRecords)}`);

            // Check for both uppercase and lowercase DMARC records
            const dmarcRecord = flatDmarcRecords.find(
              (txt) =>
                txt.startsWith("v=DMARC1") ||
                txt.toLowerCase().startsWith("v=dmarc1")
            );

            if (dmarcRecord) {
              outputRecord["DMARC"] = `${target}: ${dmarcRecord}`;
              dmarcFound = true;
              break;
            }
          } catch (err) {
            // Continue to the next target
          }
        }
      } catch (err) {
        console.log(`Error checking DMARC for ${cleanDomain}: ${err.message}`);
      }
    } catch (err) {
      console.log(`No TXT records for ${cleanDomain}`);
    }

    // Get A record for PTR lookup
    try {
      const aRecords = await dns.resolve4(cleanDomain);
      if (aRecords && aRecords.length > 0) {
        const ptrResults = [];
        for (const ip of aRecords) {
          try {
            const ptrRecords = await dns.reverse(ip);
            ptrResults.push(`${ip}: ${ptrRecords.join(", ")}`);
          } catch (err) {
            ptrResults.push(`${ip}: No PTR record`);
          }
        }
        outputRecord["PTR"] = ptrResults.join(" | ");
      }
    } catch (err) {
      console.log(`No A records for ${cleanDomain}`);
    }

    // Follow HTTP redirects to get final URL
    try {
      outputRecord["Final Redirect Location"] =
        await getRedirectLocation(cleanDomain);
    } catch (err) {
      console.log(
        `Error checking redirects for ${cleanDomain}: ${err.message}`
      );
    }
  } catch (error) {
    console.error(`Error processing domain ${domain}: ${error.message}`);
  }

  return outputRecord;
}

function getRedirectLocation(domain) {
  return new Promise((resolve, reject) => {
    const cleanUrl = domain.includes("://") ? domain : `https://${domain}`;
    const protocol = cleanUrl.startsWith("https") ? https : http;
    const options = {
      method: "HEAD",
      timeout: 5000,
    };

    function followRedirect(url, depth = 0) {
      if (depth > 10) {
        return resolve(url); // Prevent infinite redirect loops
      }

      try {
        // Validate URL before creating request
        const parsedUrl = new URL(url);
        const currentProtocol = parsedUrl.protocol === "https:" ? https : http;

        const req = currentProtocol.request(url, options, (res) => {
          if (
            res.statusCode >= 300 &&
            res.statusCode < 400 &&
            res.headers.location
          ) {
            // Handle relative URLs
            let nextUrl = res.headers.location;
            if (nextUrl.startsWith("/")) {
              nextUrl = `${parsedUrl.protocol}//${parsedUrl.host}${nextUrl}`;
            } else if (!nextUrl.includes("://")) {
              if (nextUrl.includes(".") && !nextUrl.includes("/")) {
                nextUrl = `${parsedUrl.protocol}//${nextUrl}`;
              } else {
                const basePathname = parsedUrl.pathname.endsWith("/")
                  ? parsedUrl.pathname
                  : parsedUrl.pathname.substring(
                      0,
                      parsedUrl.pathname.lastIndexOf("/") + 1
                    );
                nextUrl = `${parsedUrl.protocol}//${parsedUrl.host}${basePathname}${nextUrl}`;
              }
            }

            try {
              new URL(nextUrl);
              followRedirect(nextUrl, depth + 1);
            } catch (urlErr) {
              console.error(`Invalid redirect URL: ${nextUrl}`);
              resolve(url + ` (invalid redirect to ${nextUrl})`);
            }
          } else {
            resolve(url);
          }
        });

        req.on("error", (err) => {
          if (depth === 0 && url.startsWith("https")) {
            followRedirect(url.replace("https://", "http://"), depth + 1);
          } else {
            resolve(url + " (connection error)");
          }
        });

        req.on("timeout", () => {
          req.destroy();
          resolve(url + " (timeout)");
        });

        req.end();
      } catch (urlError) {
        console.error(`Invalid URL: ${url}`, urlError.message);
        resolve(`${url} (invalid URL)`);
      }
    }

    followRedirect(cleanUrl);
  });
}

module.exports = {
  processCsvData,
  processSingleDomain,
};
