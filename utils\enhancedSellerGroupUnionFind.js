const prisma = require("../database/prisma/getPrismaClient");
const { extractDomain } = require('./domainHelper');

// Helper: normalize JSON column into an array
function normalizeJsonArray(value) {
  if (!value) return [];
  if (Array.isArray(value)) return value;
  try {
    // Handle JSON string
    if (typeof value === 'string') {
      const parsed = JSON.parse(value);
      return Array.isArray(parsed) ? parsed : [];
    }
    // Handle JSON object
    return Array.isArray(value.data) ? value.data : [];
  } catch (_) {
    return [];
  }
}

/**
 * Find parent group ID for an entity
 */
async function findParentGroup(entityType, entityValue) {
  if (!entityValue) return null;
  
  const entity = await prisma.sellerTree.findUnique({
    where: {
      entity_type_entity_value: {
        entity_type: entityType.toLowerCase(),
        entity_value: entityValue
      }
    },
    select: { parent_id: true }
  });

  return entity?.parent_id || null;
}

/**
 * Create an entity in the seller tree
 */
async function createEntity(entityType, entityValue, parentGroupId) {
  if (!entityValue || !parentGroupId) return null;
  
  await prisma.sellerTree.create({
    data: {
      entity_type: entityType.toLowerCase(),
      entity_value: entityValue,
      parent_id: parentGroupId
    }
  });
}

/**
 * Choose winning group based on rank
 */
async function chooseWinner(group1, group2) {
  if (!group1 || !group2) {
    throw new Error(`Cannot choose winner: One or both groups not found`);
  }

  const rank1 = group1.rank || 0;
  const rank2 = group2.rank || 0;

  let winner, loser, shouldUpdateRank = false;
  
  if (rank1 > rank2) {
    winner = group2.id; // Lower rank becomes winner
    loser = group1.id;
  } else if (rank1 < rank2) {
    winner = group1.id; // Lower rank becomes winner
    loser = group2.id;
  } else {
    // Equal rank - choose based on seller count as tiebreaker
    const count1 = group1.seller_count || normalizeJsonArray(group1.seller_ids).length;
    const count2 = group2.seller_count || normalizeJsonArray(group2.seller_ids).length;

    if (count1 >= count2) {
      winner = group1.id;
      loser = group2.id;
    } else {
      winner = group2.id;
      loser = group1.id;
    }
    
    shouldUpdateRank = true;
  }

  return { winner, loser, shouldUpdateRank };
}

/**
 * Merge two seller groups
 */
async function mergeGroups(group1, group2) {
  console.log(`🔄 Merging group ${group2.id} into group ${group1.id}`);

  if (!group1 || !group2) {
    throw new Error(`Cannot merge: One or both groups not found`);
  }

  // Choose winner based on rank
  const { winner, loser, shouldUpdateRank } = await chooseWinner(group1, group2);
  
  // Get the actual group objects for winner and loser
  const finalWinner = winner === group1.id ? group1 : group2;
  const finalLoser = loser === group2.id ? group2 : group1;

  // Get actual counts from database
  const [winnerSellers, winnerDomains, loserSellers, loserDomains] = await Promise.all([
    prisma.amazonSeller.findMany({
      where: { seller_group_id: finalWinner.id },
      select: { amazon_seller_id: true }
    }),
    prisma.sellerTree.findMany({
      where: { 
        parent_id: finalWinner.id,
        entity_type: 'domain'
      },
      select: { entity_value: true }
    }),
    prisma.amazonSeller.findMany({
      where: { seller_group_id: finalLoser.id },
      select: { amazon_seller_id: true }
    }),
    prisma.sellerTree.findMany({
      where: { 
        parent_id: finalLoser.id,
        entity_type: 'domain'
      },
      select: { entity_value: true }
    })
  ]);

  // Clean and normalize arrays
  const winnerSellerIds = [
    ...normalizeJsonArray(finalWinner.seller_ids),
    ...winnerSellers.map(s => s.amazon_seller_id).filter(Boolean)
  ];
  const loserSellerIds = [
    ...normalizeJsonArray(finalLoser.seller_ids),
    ...loserSellers.map(s => s.amazon_seller_id).filter(Boolean)
  ];
  const winnerDomainList = [
    ...normalizeJsonArray(finalWinner.domains),
    ...winnerDomains.map(d => d.entity_value).filter(Boolean)
  ];
  const loserDomainList = [
    ...normalizeJsonArray(finalLoser.domains),
    ...loserDomains.map(d => d.entity_value).filter(Boolean)
  ];

  // Log details before merge
  console.log('Before merge - Winner:', {
    id: finalWinner.id,
    sellerCount: winnerSellerIds.length,
    domainCount: winnerDomainList.length,
    sellerIds: winnerSellerIds,
    domains: winnerDomainList
  });
  console.log('Before merge - Loser:', {
    id: finalLoser.id,
    sellerCount: loserSellerIds.length,
    domainCount: loserDomainList.length,
    sellerIds: loserSellerIds,
    domains: loserDomainList
  });

  // Merge arrays with deduplication
  const mergedSellerIds = [...new Set([
    ...winnerSellerIds,
    ...loserSellerIds
  ].filter(id => id && id.trim && id.trim() !== ''))];

  const mergedDomains = [...new Set([
    ...winnerDomainList,
    ...loserDomainList
  ].filter(domain => domain && domain.trim && domain.trim() !== ''))];

  await prisma.$transaction(async (tx) => {
    // Move all tree entries to winning group
    await tx.sellerTree.updateMany({
      where: { parent_id: finalLoser.id },
      data: { parent_id: finalWinner.id }
    });

    // Move all sellers to winning group
    await tx.amazonSeller.updateMany({
      where: { seller_group_id: finalLoser.id },
      data: { seller_group_id: finalWinner.id }
    });

    // Calculate new rank
    const newRank = shouldUpdateRank ? 
      Math.max(finalWinner.rank || 0, finalLoser.rank || 0) + 1 : 
      finalWinner.rank || 0;

    // Update winning group with merged data
    const updatedGroup = await tx.sellerGroup.update({
      where: { id: finalWinner.id },
      data: {
        seller_ids: mergedSellerIds,
        domains: mergedDomains,
        name: `MERGED_SELLER_GROUP`,
        rank: newRank,
        domain_count: mergedDomains.length,
        seller_count: mergedSellerIds.length
      }
    });

    // Delete losing group
    await tx.sellerGroup.delete({
      where: { id: loser }
    });

    // Log after update
    console.log('After merge - Updated group:', {
      id: finalWinner.id,
      seller_count: updatedGroup.seller_count,
      domain_count: updatedGroup.domain_count,
      sellerIds: mergedSellerIds,
      domains: mergedDomains
    });
  });

  console.log(`✅ Successfully merged group ${finalLoser.id} into ${finalWinner.id}`);
  return { finalWinnerId: finalWinner.id };
}

/**
 * Process a single seller
 */
async function processSeller(seller, validationOptions = {}) {
  // Validate seller object
  if (!seller || !seller.id) {
    console.error('Invalid seller object provided to processSeller');
    return { action: 'error', error: 'Invalid seller object' };
  }

  const sellerId = seller.amazon_seller_id;
  
  // Process domain: extract, normalize, and validate
  let domain = null;
  if (seller.domain) {
    domain = extractDomain(seller.domain);
    domain = domain?.toLowerCase().trim();
  }
  
  let currentGroupId = seller.seller_group_id;
  
  // Domain validation criteria
  const { validWebsiteStatuses = ['Final Correct'] } = validationOptions;
  const isDomainValid = domain && 
                       domain !== '' && 
                       seller.website_status && 
                       validWebsiteStatuses.includes(seller.website_status);
  
  // Find parent groups for seller_id and domain (only if domain is valid)
  const sellerParent = sellerId ? await findParentGroup('seller_id', sellerId) : null;
  const domainParent = isDomainValid ? await findParentGroup('domain', domain) : null;


  let result = {
    action: 'none',
    merged: false,
    created: [],
    groupId: currentGroupId
  };

  if (sellerParent && domainParent) {
    // Both entities exist
    if (sellerParent === domainParent) {
      // Same group - check if matches current
      if (sellerParent === currentGroupId) {
        result.action = 'already_grouped';
      } else {
        result.action = 'group_mismatch';
        result.groupId = sellerParent;
        
        // Update seller to point to correct group
        await prisma.amazonSeller.update({
          where: { id: seller.id },
          data: { seller_group_id: sellerParent }
        });
        // Update Seller Group Data
        await addToGroupArrays(sellerParent, sellerId, isDomainValid ? domain : null);
      }
    } else {
      // Different groups - need to merge
      result.action = 'merge_required';
      
      // Get both groups
      const [group1, group2] = await Promise.all([
        prisma.sellerGroup.findUnique({
          where: { id: sellerParent },
          select: { id: true, rank: true, seller_ids: true, domains: true, name: true, domain_count: true, seller_count: true }
        }),
        prisma.sellerGroup.findUnique({
          where: { id: domainParent },
          select: { id: true, rank: true, seller_ids: true, domains: true, name: true, domain_count: true, seller_count: true }
        })
      ]);
      
      // Check if both groups exist before merging
      if (!group1 || !group2) {
        console.error(`Cannot merge groups - group1: ${group1?.id || 'null'}, group2: ${group2?.id || 'null'}`);
        result.action = 'merge_failed';
        return result;
      }
      
      const { finalWinnerId } = await mergeGroups(group1, group2);
      result.merged = true;
      result.groupId = finalWinnerId;
      
      // Update seller to point to winning group (will be determined by mergeGroups)
      const finalGroupId = await findParentGroup('seller_id', sellerId) || 
                          await findParentGroup('domain', domain);
      if (finalGroupId) {
        await prisma.amazonSeller.update({
          where: { id: seller.id },
          data: { seller_group_id: finalGroupId }
        });
        await addToGroupArrays(finalGroupId, sellerId, isDomainValid ? domain : null);
        result.groupId = finalGroupId;
      }
    }
  } else if (sellerParent || domainParent) {
    // One entity exists
    const existingGroup = sellerParent || domainParent;
    
    if (existingGroup === currentGroupId) {
      // Create missing entity
      if (sellerId && !sellerParent) {
        await createEntity('seller_id', sellerId, existingGroup);
        result.created.push('seller_id');
      }
      if (isDomainValid && !domainParent) {
        await createEntity('domain', domain, existingGroup);
        result.created.push('domain');
      }
      
      // Add entities to group arrays
      await addToGroupArrays(existingGroup, sellerId, isDomainValid ? domain : null);
      
      result.action = 'created_missing';
    } else {
      // Group mismatch - merge needed if currentGroupId exists
      if (currentGroupId) {
        result.action = 'merge_with_existing';
        
        // Get both groups
        const [existingGroupObj, currentGroupObj] = await Promise.all([
          prisma.sellerGroup.findUnique({
            where: { id: existingGroup },
            select: { id: true, rank: true, seller_ids: true, domains: true, name: true, domain_count: true, seller_count: true }
          }),
          prisma.sellerGroup.findUnique({
            where: { id: currentGroupId },
            select: { id: true, rank: true, seller_ids: true, domains: true, name: true, domain_count: true, seller_count: true }
          })
        ]);
        
        // Check if both groups exist before merging
        if (!existingGroupObj || !currentGroupObj) {
          console.error(`Cannot merge groups - existingGroup: ${existingGroupObj?.id || 'null'}, currentGroup: ${currentGroupObj?.id || 'null'}`);
          result.action = 'merge_failed';
          return result;
        }
        
        const { finalWinnerId } = await mergeGroups(existingGroupObj, currentGroupObj);
        result.merged = true;
        result.groupId = finalWinnerId;
        
                  // Create missing entity in winning group
          const finalGroupId = await findParentGroup('seller_id', sellerId) || 
                              await findParentGroup('domain', domain);
          if (finalGroupId) {
            if (sellerId && !sellerParent) {
              await createEntity('seller_id', sellerId, finalGroupId);
              result.created.push('seller_id');
            }
            if (isDomainValid && !domainParent) {
              await createEntity('domain', domain, finalGroupId);
              result.created.push('domain');
            }
            
            // Add entities to group arrays
            await addToGroupArrays(finalGroupId, sellerId, isDomainValid ? domain : null);
            
            // Update seller to point to winning group
            await prisma.amazonSeller.update({
              where: { id: seller.id },
              data: { seller_group_id: finalGroupId }
            });
            result.groupId = finalGroupId;
          }
      } else {
        // No current group - just assign to existing group
        result.action = 'assign_to_existing';
        result.groupId = existingGroup;
        
        // Create missing entity
        if (sellerId && !sellerParent) {
          await createEntity('seller_id', sellerId, existingGroup);
          result.created.push('seller_id');
        }
        if (isDomainValid && !domainParent) {
          await createEntity('domain', domain, existingGroup);
          result.created.push('domain');
        }
        
        // Add entities to group arrays
        await addToGroupArrays(existingGroup, sellerId, isDomainValid ? domain : null);
        
        // Update seller to point to existing group
        await prisma.amazonSeller.update({
          where: { id: seller.id },
          data: { seller_group_id: existingGroup }
        });
      }
    }
  } else {
    // No entities exist - create both with current group (or create new group if none)
    result.action = 'create_both';
    
    if (!currentGroupId) {
      // Create new group with initial arrays and counts
      const newGroup = await prisma.sellerGroup.create({
        data: {
          name: `Group_${sellerId || (isDomainValid ? domain : 'unknown')}`,
          seller_ids: sellerId ? [sellerId] : [],
          domains: isDomainValid ? [domain] : [],
          domain_count: isDomainValid ? 1 : 0,
          seller_count: sellerId ? 1 : 0,
          rank: 0
        }
      });
      
      currentGroupId = newGroup.id;
      result.groupId = currentGroupId;
      
      // Update seller with new group
      await prisma.amazonSeller.update({
        where: { id: seller.id },
        data: { seller_group_id: currentGroupId }
      });
      
      result.action = 'create_new_group';
    }
    
    if (sellerId) {
      await createEntity('seller_id', sellerId, currentGroupId);
      result.created.push('seller_id');
    }
    if (isDomainValid) {
      await createEntity('domain', domain, currentGroupId);
      result.created.push('domain');
    }
    
    // Add entities to group arrays (only for existing groups, new groups already have arrays set)
    if (result.action !== 'create_new_group') {
      await addToGroupArrays(currentGroupId, sellerId, isDomainValid ? domain : null);
    }
  }

  return result;
}

/**
 * Clear all seller tree data
 */
async function clearSellerTreeData(options = {}) {
  const { dryRun = false } = options;

  if (dryRun) {
    const counts = await Promise.all([
      prisma.sellerTree.count(),
      prisma.sellerGroup.count()
    ]);
    
    console.log(`DRY RUN: Would clear ${counts[0]} seller tree entries and ${counts[1]} seller groups`);
    return { cleared: { sellerTrees: counts[0], sellerGroups: counts[1] } };
  }

  console.log("Clearing existing seller tree data...");
  
  try {
    await prisma.$transaction(async (tx) => {
      // Clear seller group assignments from sellers
      await tx.amazonSeller.updateMany({
        data: { seller_group_id: null }
      });

      // Delete all seller tree entries
      await tx.sellerTree.deleteMany({});

      // Delete all seller groups
      await tx.sellerGroup.deleteMany({});
    });

    console.log("Seller tree data cleared successfully");
  } catch (error) {
    console.error("Error clearing seller tree data:", error.message);
    throw error;
  }
  
  return { cleared: true };
}

/**
 * Update group counts based on arrays
 */
async function updateGroupCounts(groupId) {
  try {
    // Get actual counts from database
    const [sellers, domains, group] = await Promise.all([
      prisma.amazonSeller.findMany({
        where: { seller_group_id: groupId },
        select: { amazon_seller_id: true }
      }),
      prisma.sellerTree.findMany({
        where: { 
          parent_id: groupId,
          entity_type: 'domain'
        },
        select: { entity_value: true }
      }),
      prisma.sellerGroup.findUnique({
        where: { id: groupId },
        select: { seller_ids: true, domains: true }
      })
    ]);
    
    if (!group) return { success: false, message: 'Group not found' };

    // Combine and normalize arrays
    const sellerIds = [
      ...normalizeJsonArray(group.seller_ids),
      ...sellers.map(s => s.amazon_seller_id).filter(Boolean)
    ];
    const domainList = [
      ...normalizeJsonArray(group.domains),
      ...domains.map(d => d.entity_value).filter(Boolean)
    ];

    // Clean arrays and update counts
    const validSellerIds = [...new Set(
      sellerIds.filter(id => id && id.trim && id.trim() !== '')
    )];
    
    const validDomains = [...new Set(
      domainList.filter(domain => domain && domain.trim && domain.trim() !== '')
    )];

    await prisma.sellerGroup.update({
      where: { id: groupId },
      data: {
        seller_ids: validSellerIds,
        domains: validDomains,
        seller_count: validSellerIds.length,
        domain_count: validDomains.length
      }
    });

    return {
      success: true,
      sellerCount: validSellerIds.length,
      domainCount: validDomains.length
    };
  } catch (error) {
    console.error(`Error updating group counts for group ${groupId}:`, error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Simple function to add entities to group arrays
 */
async function addToGroupArrays(groupId, sellerId = null, domain = null) {
  const group = await prisma.sellerGroup.findUnique({
    where: { id: groupId },
    select: { seller_ids: true, domains: true }
  });
  
  if (!group) return;

  let sellerIds = normalizeJsonArray(group.seller_ids);
  let domains = normalizeJsonArray(group.domains);
  
  if (sellerId && !sellerIds.includes(sellerId)) {
    sellerIds.push(sellerId);
  }
  if (domain && !domains.includes(domain)) {
    domains.push(domain);
  }

  // Remove duplicates
  sellerIds = [...new Set(sellerIds)];
  domains = [...new Set(domains)];

  await prisma.sellerGroup.update({
    where: { id: groupId },
    data: {
      seller_ids: sellerIds,
      domains: domains,
      seller_count: sellerIds.length,
      domain_count: domains.length
    }
  });
}



module.exports = {
  findParentGroup,
  createEntity,
  mergeGroups,
  chooseWinner,
  processSeller,
  clearSellerTreeData,
  updateGroupCounts,
  addToGroupArrays
};