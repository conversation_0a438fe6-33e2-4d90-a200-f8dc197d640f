const fs = require("fs");
const csv = require("csv-parser");
const { parse } = require("json2csv");
const { PrismaClient } = require("@prisma/client");
const {
  sellerLeadMatchSchemaMap,
  transformData,
} = require("../../middlewares/schema");

const prisma = new PrismaClient();

/**
 * Reads and parses a CSV file.
 */
async function readCSV(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on("data", (data) => results.push(data))
      .on("end", () => resolve(results))
      .on("error", (error) => reject(error));
  });
}

/**
 * Matches input CSV data with database records using `amazon_seller_id`
 */
async function matchAndMap(rawInputData) {
  const inputData = [];
  for (const row of rawInputData) {
    const data = await transformData(row, sellerLeadMatchSchemaMap);
    delete data.errors;
    inputData.push(data);
  }

  const BATCH_SIZE = 1000;
  async function fetchCompaniesInBatches(sellerIds) {
    const companies = [];
    for (let i = 0; i < sellerIds.length; i += BATCH_SIZE) {
      const batch = sellerIds.slice(i, i + BATCH_SIZE);
      const batchResults = await prisma.company.findMany({
        where: {
          amazon_seller_id: { in: batch },
        },
        select: {
          amazon_seller_id: true,
          website_status: true,
          derived_estimate_sales: true,
        },
      });
      companies.push(...batchResults);
    }
    return companies;
  }

  const sellerIds = inputData.map((row) => row.seller_id).filter(Boolean);
  const companies = await fetchCompaniesInBatches(sellerIds);

  const companyMap = {};
  companies.forEach((company) => {
    companyMap[company.amazon_seller_id] = company;
  });

  const mappedResults = inputData.map((row) => ({
    ...row,
    "Website Status": companyMap[row.seller_id]?.website_status || "Not Found",
    "Derived Estimated Sales":
      companyMap[row.seller_id]?.derived_estimate_sales || "N/A",
  }));

  return parse(mappedResults);
}

module.exports = matchAndMap;
