const { sheets } = require('../config/config');
const funnelConfig = require('../config/funnelConfig');
const { lookupSources } = require('../../middlewares/validator');

const SHEET_OPTIONS = [
  { title: "Enter Sheet URL", value: "url" },
  { title: "Manual Sheet", value: sheets.MANUAL_SHEET_ID },
  { title: "Errors Sheet", value: sheets.ERRORS_SHEET_ID },
  { title: "Output Tracking Sheet", value: sheets.OUTPUT_TRACKING_SHEET_ID },
];

// Create choices array from funnel config
const FUNNEL_CHOICES = Object.keys(funnelConfig).map(funnel => ({
  title: funnel,
  value: funnel
}));

// Legacy API endpoint choices (will be determined by funnel selection)
const API_ENDPOINT_CHOICES = [
  { title: "Update Companies", value: "UpdateCompanies" },
  { title: "ClientProspectMatching", value: "ClientProspectMatching" },
  { title: "Update Prospects", value: "UpdateProspects" }
];

const promptConfig = [
  {
    type: "select",
    name: "operationType",
    message: "Do you want to perform API operations or maintenance?",
    choices: [
      { title: "API Operations", value: "api" },
      { title: "Maintenance", value: "maintenance" },
    ],
    initial: 0,
  },
  {
    type: (prev) => (prev === "api" ? "select" : null),
    name: "funnel",
    message: "Please select the funnel to process:",
    choices: FUNNEL_CHOICES,
    initial: 0,
  },
  {
    type: (prev, values) => (values.operationType === "api" ? "select" : null),
    name: "endpointVersion",
    message: "Which API endpoint version would you like to use?",
    choices: [
      { title: "Old (Legacy)", value: "old" },
      { title: "New (Updated)", value: "new" },
      { title: "Both (New fields, ping both APIs)", value: "both" }
    ],
    initial: 0,
  },
  {
    type: (prev, values) => {
      // Only show sub-funnel selection for funnels that have sub-funnels defined
      const selectedFunnel = values.funnel ? funnelConfig[values.funnel] : null;
      return (values.operationType === "api" && selectedFunnel?.subFunnels) ? "select" : null;
    },
    name: "subFunnel",
    message: "Please select the sub-funnel:",
    choices: (prev, values) => {
      const selectedFunnel = funnelConfig[values.funnel];
      if (selectedFunnel?.subFunnels) {
        return Object.keys(selectedFunnel.subFunnels).map(subFunnel => ({
          title: subFunnel,
          value: subFunnel
        }));
      }
      return [];
    },
    initial: 0,
  },
  {
    type: (prev) => (prev === "maintenance" ? "select" : null),
    name: "maintenanceType",
    message: "What maintenance operation do you want to perform?",
    choices: [
      { title: "Delete Sub Sheets", value: "delete" },
      { title: "Hide Sub Sheets", value: "hide" }
    ],
    initial: 0,
  },
  {
    type: (prev, values) => (values.operationType === "maintenance" ? "text" : null),
    name: "subSheetsToProcess",
    message: "Please enter the comma-separated names of sub-sheets to process:",
    validate: (value) => (value ? true : "Sub sheet names are required"),
  },
  {
    type: (prev, values) => (values.operationType === "api" ? "select" : null),
    name: "sheetOption",
    message: "Please select the Google Sheet:",
    choices: SHEET_OPTIONS,
    initial: 0,
  },
  {
    type: (prev, values) => (values.operationType === "api" && values.sheetOption === "url" ? "text" : null),
    name: "sheetUrl",
    message: "Please enter the Google Sheet URL:",
    validate: (value) => (value ? true : "Sheet URL is required"),
  },
  {
    type: (prev, values) => (values.operationType === "api" && values.sheetOption === "url" ? "text" : null),
    name: "urlSheetName",
    message: "Please enter comma-separated names of the sub sheets to process:",
    validate: (value) => (value ? true : "Sub sheet names are required"),
  },
  {
    type: (prev, values) => (values.operationType === "api" && values.sheetOption !== "url" ? "text" : null),
    name: "sheetNames",
    message: "Please enter comma-separated names of the sub sheets to process:",
    validate: (value) => (value ? true : "Sub sheet names are required"),
  },
  {
    type: (prev, values) => (values.operationType === "api" && values.sheetOption === sheets.ERRORS_SHEET_ID ? "confirm" : null),
    name: "ignoreErrors",
    message: "Do you want to ignore rows with errors during sheet processing?",
    initial: true,
  },
  {
    type: (prev, values) => (values.operationType === "api" ? "confirm" : null),
    name: "skipErrorRows",
    message: "Do you want to skip error rows during API upload?",
    initial: true,
  },
];

module.exports = promptConfig;
