const PortkeyLangSmithWrapper = require('./portkeyLangsmithWrapper');
const { AzureOpenAI } = require('openai');
const { OpenAI } = require('openai');
const { validateConfig } = require('../../config/portkey');
require('dotenv').config();

/**
 * Centralized AI Service
 * 
 * This service provides a unified interface for all AI operations in the application.
 * It automatically routes requests through Portkey when available, with fallback to direct providers.
 * 
 * Features:
 * - Automatic Portkey routing with fallback
 * - Consistent error handling
 * - Request/response logging
 * - Cost tracking
 * - Performance monitoring
 */
class CentralizedAIService {
  constructor() {
    this.portkeyWrappers = {
      chat: new PortkeyLangSmithWrapper('chat'),
      azure: new PortkeyLangSmithWrapper('azure'),
      assistant: new PortkeyLangSmithWrapper('assistant'),
      analysis: new PortkeyLangSmithWrapper('analysis'),
    };
    
    this.fallbackClients = {};
    this.initializeFallbackClients();
  }

  /**
   * Initialize fallback clients for direct provider access
   */
  initializeFallbackClients() {
    try {
      // Azure OpenAI fallback
      if (process.env.AZURE_OPENAI_ENDPOINT && process.env.OPENAI_API_KEY) {
        this.fallbackClients.azure = new AzureOpenAI({
          apiKey: process.env.OPENAI_API_KEY,
          endpoint: process.env.AZURE_OPENAI_ENDPOINT,
          apiVersion: process.env.AZURE_OPENAI_API_VERSION,
          deployment: process.env.AZURE_OPENAI_DEPLOYMENT,
        });
      }

      // OpenAI fallback
      if (process.env.OPENAI_API_KEY) {
        this.fallbackClients.openai = new OpenAI({
          apiKey: process.env.OPENAI_API_KEY,
        });
      }
    } catch (error) {
      console.warn('Failed to initialize some fallback clients:', error.message);
    }
  }

  /**
   * Create a chat completion
   * @param {Array} messages - Array of message objects
   * @param {Object} options - Completion options
   * @param {string} useCase - Use case for routing (chat, azure, analysis)
   * @param {boolean} usePortkey - Whether to use Portkey (default: true)
   * @returns {Promise<Object>} Completion response
   */
  async createChatCompletion(messages, options = {}, useCase = 'azure', usePortkey = true) {
    const startTime = Date.now();
    
    try {
      if (usePortkey) {
        console.log(`Using Portkey + LangSmith wrapper for ${useCase} chat completion`);
        
        const wrapper = this.portkeyWrappers[useCase] || this.portkeyWrappers.chat;
        const completion = await wrapper.createChatCompletion(messages, options);
        
        const result = completion.usage || {};
        result.message = completion.choices[0]?.message?.content;
        result.prompt = messages;
        result.duration = Date.now() - startTime;
        result.provider = 'portkey';
        result.useCase = useCase;
        
        return result;
      } else {
        return await this.createFallbackChatCompletion(messages, options, useCase);
      }
    } catch (error) {
      console.error(`Portkey ${useCase} completion failed:`, error.message);
      
      if (usePortkey) {
        console.log('Falling back to direct provider...');
        return await this.createFallbackChatCompletion(messages, options, useCase);
      }
      
      throw error;
    }
  }

  /**
   * Create a chat completion using fallback clients
   * @param {Array} messages - Array of message objects
   * @param {Object} options - Completion options
   * @param {string} useCase - Use case for routing
   * @returns {Promise<Object>} Completion response
   */
  async createFallbackChatCompletion(messages, options = {}, useCase = 'azure') {
    const startTime = Date.now();
    
    try {
      let completion;
      let provider;
      
      if (useCase === 'azure' && this.fallbackClients.azure) {
        console.log('Using fallback Azure OpenAI implementation');
        provider = 'azure-direct';
        
        completion = await this.fallbackClients.azure.chat.completions.create({
          messages,
          model: options.model || process.env.OPENAI_MODEL_ID || 'gpt-4o',
          temperature: options.temperature || 0.7,
          max_tokens: options.max_tokens || options.maxTokens || 1000,
          presence_penalty: options.presence_penalty || 0,
          top_p: options.top_p || 1,
          ...options,
        });
      } else if (this.fallbackClients.openai) {
        console.log('Using fallback OpenAI implementation');
        provider = 'openai-direct';
        
        completion = await this.fallbackClients.openai.chat.completions.create({
          messages,
          model: options.model || 'gpt-4o',
          temperature: options.temperature || 0.7,
          max_tokens: options.max_tokens || options.maxTokens || 1000,
          presence_penalty: options.presence_penalty || 0,
          top_p: options.top_p || 1,
          ...options,
        });
      } else {
        throw new Error('No fallback clients available');
      }

      const result = completion.usage || {};
      result.message = completion.choices[0]?.message?.content;
      result.prompt = messages;
      result.duration = Date.now() - startTime;
      result.provider = provider;
      result.useCase = useCase;
      
      return result;
    } catch (error) {
      console.error('Fallback completion failed:', error.message);
      throw new Error(`Error fetching response from AI: ${error.message}`);
    }
  }

  /**
   * Create an assistant run
   * @param {string} assistantId - Assistant ID
   * @param {Array} messages - Array of message objects
   * @param {Object} options - Run options
   * @param {boolean} usePortkey - Whether to use Portkey (default: true)
   * @returns {Promise<Object>} Assistant response
   */
  async createAssistantRun(assistantId, messages, options = {}, usePortkey = true) {
    const startTime = Date.now();
    
    try {
      if (usePortkey) {
        console.log('Using Portkey + LangSmith wrapper for assistant response');
        
        const wrapper = this.portkeyWrappers.assistant;
        const run = await wrapper.createAssistantRun(assistantId, messages, options);
        
        const result = run.usage || {};
        result.gptDetails = run.model;
        result.prompt = JSON.stringify(run.instructions || '');
        result.duration = Date.now() - startTime;
        result.provider = 'portkey';
        
        if (run.status === 'completed' && run.message) {
          result.message = run.message.replace(/"""/g, ' ');
          return result;
        } else {
          console.log('Assistant run failed:', run.last_error?.message || 'Unknown error');
          return result;
        }
      } else {
        return await this.createFallbackAssistantRun(assistantId, messages, options);
      }
    } catch (error) {
      console.error('Portkey assistant run failed:', error.message);
      
      if (usePortkey) {
        console.log('Falling back to direct OpenAI...');
        return await this.createFallbackAssistantRun(assistantId, messages, options);
      }
      
      throw error;
    }
  }

  /**
   * Create an assistant run using fallback client
   * @param {string} assistantId - Assistant ID
   * @param {Array} messages - Array of message objects
   * @param {Object} options - Run options
   * @returns {Promise<Object>} Assistant response
   */
  async createFallbackAssistantRun(assistantId, messages, options = {}) {
    const startTime = Date.now();
    
    try {
      if (!this.fallbackClients.openai) {
        throw new Error('OpenAI fallback client not available');
      }
      
      console.log('Using fallback OpenAI implementation for assistant');
      
      const openai = this.fallbackClients.openai;
      openai.defaultHeaders = { 'OpenAI-Beta': 'assistants=v2' };
      
      let run = await openai.beta.threads.createAndRun({
        assistant_id: assistantId,
        thread: { messages },
        ...options,
      });

      while (['queued', 'in_progress', 'cancelling'].includes(run.status)) {
        console.log('Waiting for assistant run to complete...', run.status);
        await new Promise((resolve) => setTimeout(resolve, 1000));
        run = await openai.beta.threads.runs.retrieve(run.thread_id, run.id);
      }

      console.log('Assistant run completed:', run.status);
      const result = run.usage || {};
      result.gptDetails = run.model;
      result.prompt = JSON.stringify(run.instructions);
      result.duration = Date.now() - startTime;
      result.provider = 'openai-direct';

      if (run.status === 'completed') {
        const threadMessages = await openai.beta.threads.messages.list(run.thread_id);
        for (const message of threadMessages.data.reverse()) {
          if (message.role === 'assistant') {
            result.message = message.content[0].text.value.replace(/"""/g, ' ');
            return result;
          }
        }
      } else {
        console.log('Assistant run failed:', run.last_error?.message);
      }
      
      return result;
    } catch (error) {
      console.error('Fallback assistant run failed:', error.message);
      throw new Error(`Error fetching assistant response: ${error.message}`);
    }
  }

  /**
   * Get service health status
   * @returns {Object} Health status information
   */
  getHealthStatus() {
    try {
      const azureConfig = validateConfig('azure');
      const chatConfig = validateConfig('chat');

      return {
        portkey: {
          azure: azureConfig.isValid,
          chat: chatConfig.isValid,
          errors: [...(azureConfig.errors || []), ...(chatConfig.errors || [])],
        },
        fallback: {
          azure: !!this.fallbackClients.azure,
          openai: !!this.fallbackClients.openai,
        },
        environment: {
          hasAzureEndpoint: !!process.env.AZURE_OPENAI_ENDPOINT,
          hasAzureDeployment: !!process.env.AZURE_OPENAI_DEPLOYMENT,
          hasOpenAIKey: !!process.env.OPENAI_API_KEY,
          hasPortkeyUrl: !!process.env.PORTKEY_BASE_URL,
        },
      };
    } catch (error) {
      console.error('Error getting health status:', error.message);
      return {
        portkey: {
          azure: false,
          chat: false,
          errors: [error.message],
        },
        fallback: {
          azure: !!this.fallbackClients.azure,
          openai: !!this.fallbackClients.openai,
        },
        environment: {
          hasAzureEndpoint: !!process.env.AZURE_OPENAI_ENDPOINT,
          hasAzureDeployment: !!process.env.AZURE_OPENAI_DEPLOYMENT,
          hasOpenAIKey: !!process.env.OPENAI_API_KEY,
          hasPortkeyUrl: !!process.env.PORTKEY_BASE_URL,
        },
      };
    }
  }
}

// Create singleton instance
const centralizedAI = new CentralizedAIService();

module.exports = {
  CentralizedAIService,
  centralizedAI,
};
