const fs = require('fs');
const csv = require('csv-parser');
const createCsvWriter = require('csv-writer').createObjectCsvWriter;
const path = require('path');
const https = require('https');
const { ERRORS } = require('./emailClassificationErrors.js');

console.log(ERRORS.length)

// Enable detailed logging
const ENABLE_DETAILED_LOGGING = process.env.DEBUG === 'true' || process.argv.includes('--debug');

// Enable body column in output
const INCLUDE_BODY_COLUMN = process.env.INCLUDE_BODY === 'true' || process.argv.includes('--include-body');

// Enable API status fetching (default: true)
const FETCH_EMAIL_STATUS = process.env.FETCH_STATUS !== 'false' && !process.argv.includes('--no-fetch-status');

// API endpoint for email statuses
const EMAIL_STATUS_API = 'https://jeffcrm.equalcollective.com/api/public/emails';

function log(...args) {
  if (ENABLE_DETAILED_LOGGING) {
    console.log('🔍 [DEBUG]', ...args);
  }
}

/**
 * Parse error code from detected_error_code string
 * @param {string} errorCode - The detected error code string
 * @returns {object} - Parsed error code with category and threeLevelCode
 */
function parseErrorCode(errorCode) {
  log(`\n📥 Parsing error code: "${errorCode}"`);
  
  if (!errorCode || typeof errorCode !== 'string') {
    log('❌ Invalid input: empty or non-string error code');
    return { category: null, threeLevelCode: null };
  }

  const trimmed = errorCode.trim();
  log(`🧹 Trimmed input: "${trimmed}"`);
  
  // Pattern 1: xxx a.b.c (3-digit integer followed by space and dotted notation)
  const fullPattern = /^(\d{3})\s+(\d+\.\d+\.\d+)$/;
  const fullMatch = trimmed.match(fullPattern);
  
  if (fullMatch) {
    const result = {
      category: parseInt(fullMatch[1], 10),
      threeLevelCode: fullMatch[2]
    };
    log(`✅ Pattern 1 match (xxx a.b.c): category=${result.category}, threeLevelCode=${result.threeLevelCode}`);
    return result;
  }
  
  log('❌ Pattern 1 (xxx a.b.c) did not match');
  
  // Pattern 2: a.b.c (just dotted notation)
  const threeLevelPattern = /^(\d+\.\d+\.\d+)$/;
  const threeLevelMatch = trimmed.match(threeLevelPattern);
  
  if (threeLevelMatch) {
    const result = {
      category: null,
      threeLevelCode: threeLevelMatch[1]
    };
    log(`✅ Pattern 2 match (a.b.c): threeLevelCode=${result.threeLevelCode}`);
    return result;
  }
  
  log('❌ Pattern 2 (a.b.c) did not match');
  log('❌ No patterns matched - returning null values');
  return { category: null, threeLevelCode: null };
}

/**
 * Classify error based on parsed error code
 * @param {object} parsedError - Object with category and threeLevelCode
 * @returns {string} - Classification category
 */
function classifyError(parsedError) {
  const { category, threeLevelCode } = parsedError;
  
  log(`\n🔍 Classifying error: category=${category}, threeLevelCode=${threeLevelCode}`);
  
  if (!threeLevelCode) {
    log('❌ No threeLevelCode found - returning "Unknown"');
    return 'Unknown';
  }
  
  // If both category and threeLevelCode are present, search by both
  if (category !== null) {
    log(`🔎 Searching for exact match: Category=${category} AND 3 Level Code=${threeLevelCode}`);
    
    const exactMatch = ERRORS.find(error => 
      error.Category === category && error['3 Level Code'] === threeLevelCode
    );
    
    if (exactMatch) {
      log(`✅ Exact match found! Full Error Code: "${exactMatch['Full Error Code']}", Categorisation: "${exactMatch.Categorisation}"`);
      return exactMatch.Categorisation;
    }
    
    log('❌ No exact match found with both category and threeLevelCode');
  }
  
  // Search by threeLevelCode only
  log(`🔎 Searching by threeLevelCode only: ${threeLevelCode}`);
  
  const threeLevelMatch = ERRORS.find(error => 
    error['3 Level Code'] === threeLevelCode
  );
  
  if (threeLevelMatch) {
    log(`✅ ThreeLevelCode match found! Full Error Code: "${threeLevelMatch['Full Error Code']}", Category: ${threeLevelMatch.Category}, Categorisation: "${threeLevelMatch.Categorisation}"`);
    return threeLevelMatch.Categorisation;
  }
  
  log('❌ No match found for threeLevelCode - returning "Unknown"');
  return 'Unknown';
}

/**
 * Fetch email statuses from API
 * @returns {Promise<Object>} - Object mapping email to status
 */
async function fetchEmailStatuses() {
  return new Promise((resolve, reject) => {
    log('\n🌐 Fetching email statuses from API...');
    
    const request = https.get(EMAIL_STATUS_API, (response) => {
      let data = '';
      
      response.on('data', (chunk) => {
        data += chunk;
      });
      
      response.on('end', () => {
        try {
          const emailData = JSON.parse(data);
          
          if (!Array.isArray(emailData)) {
            throw new Error('API response is not an array');
          }
          
          // Create a mapping of email -> status
          const emailStatusMap = {};
          emailData.forEach(item => {
            if (item.email && item.status) {
              emailStatusMap[item.email] = item.status;
            }
          });
          
          log(`✅ Fetched ${Object.keys(emailStatusMap).length} email statuses from API`);
          resolve(emailStatusMap);
        } catch (parseError) {
          log(`❌ Error parsing API response: ${parseError.message}`);
          reject(parseError);
        }
      });
    });
    
    request.on('error', (error) => {
      log(`❌ API request failed: ${error.message}`);
      reject(error);
    });
    
    request.setTimeout(30000, () => {
      request.destroy();
      reject(new Error('API request timeout'));
    });
  });
}

/**
 * Process CSV file and classify emails
 * @param {string} inputFilePath - Path to input CSV file
 * @param {string} outputFilePath - Path to output CSV file
 * @param {boolean} includeBody - Whether to include body column in output
 * @param {boolean} fetchStatus - Whether to fetch email statuses from API
 */
async function classifyEmails(inputFilePath, outputFilePath, includeBody = false, fetchStatus = false) {
  const results = [];
  const errors = [];
  let rowNumber = 0;
  
  return new Promise((resolve, reject) => {
    fs.createReadStream(inputFilePath)
      .pipe(csv())
      .on('data', (row) => {
        rowNumber++; // Increment row number (starts from 1, excluding header)
        
        try {
          const detectedErrorCode = row.detected_error_code || row['detected_error_code'] || row['Detected Error Code'];
          
          if (!detectedErrorCode) {
            console.warn(`Row ${rowNumber}: No detected_error_code found in row:`, Object.keys(row));
          }
          
          log(`\n🚀 Processing Row ${rowNumber} with detected_error_code: "${detectedErrorCode}"`);
          const parsedError = parseErrorCode(detectedErrorCode);
          const classification = classifyError(parsedError);
          log(`📋 Row ${rowNumber} Final classification: "${classification}"`);
          log('─'.repeat(60));
          
          // Create processed row, optionally excluding body column
          const processedRow = {
            ...row,
            parsed_category: parsedError.category,
            parsed_three_level_code: parsedError.threeLevelCode,
            email_classification: classification
          };
          
          // Remove body column if not requested
          if (!includeBody && processedRow.body) {
            delete processedRow.body;
          }
          if (!includeBody && processedRow.Body) {
            delete processedRow.Body;
          }
          
          results.push(processedRow);
        } catch (error) {
          errors.push({ rowNumber, row, error: error.message });
          console.error(`Row ${rowNumber}: Error processing row:`, error.message);
        }
      })
      .on('end', async () => {
        try {
          if (results.length === 0) {
            throw new Error('No data processed successfully');
          }
          
          // Fetch email statuses if requested
          let emailStatusMap = {};
          if (fetchStatus) {
            try {
              emailStatusMap = await fetchEmailStatuses();
              
              // Add email status to results based on threadStartEmailId
              results.forEach(row => {
                const threadStartEmailId = row.threadStartEmailId || row['threadStartEmailId'] || row['Thread Start Email ID'];
                if (threadStartEmailId && emailStatusMap[threadStartEmailId]) {
                  row.email_status = emailStatusMap[threadStartEmailId];
                  log(`📧 Row ${results.indexOf(row) + 1}: Matched email ${threadStartEmailId} with status: ${emailStatusMap[threadStartEmailId]}`);
                } else {
                  row.email_status = 'Not Found';
                  if (threadStartEmailId) {
                    log(`❌ Row ${results.indexOf(row) + 1}: No status found for email: ${threadStartEmailId}`);
                  } else {
                    log(`❌ Row ${results.indexOf(row) + 1}: No threadStartEmailId found`);
                  }
                }
              });
              
              console.log(`\n📧 Email status matching completed!`);
              const statusSummary = results.reduce((acc, row) => {
                const status = row.email_status;
                acc[status] = (acc[status] || 0) + 1;
                return acc;
              }, {});
              
              console.log('📊 Email Status Summary:');
              Object.entries(statusSummary)
                .sort(([,a], [,b]) => b - a)
                .forEach(([status, count]) => {
                  console.log(`  ${status}: ${count}`);
                });
            } catch (apiError) {
              console.warn(`⚠️  Failed to fetch email statuses: ${apiError.message}`);
              console.warn('Continuing without email status data...');
              // Add empty status column to maintain consistency
              results.forEach(row => {
                row.email_status = 'API Error';
              });
            }
          }
          
          // Determine headers from the first result
          const headers = Object.keys(results[0]).map(key => ({
            id: key,
            title: key
          }));
          
          const csvWriter = createCsvWriter({
            path: outputFilePath,
            header: headers
          });
          
          await csvWriter.writeRecords(results);
          
          console.log(`\n✅ Email classification completed!`);
          console.log(`📊 Processed ${results.length} emails`);
          console.log(`❌ Errors: ${errors.length}`);
          console.log(`📁 Output saved to: ${outputFilePath}`);
          console.log(`📄 Body column ${includeBody ? 'included' : 'excluded'} from output`);
          console.log(`📧 Email status ${fetchStatus ? 'fetched and matched' : 'not fetched'}`);
          
          // Print classification summary
          const classificationSummary = results.reduce((acc, row) => {
            const classification = row.email_classification;
            acc[classification] = (acc[classification] || 0) + 1;
            return acc;
          }, {});
          
          console.log('\n📈 Classification Summary:');
          Object.entries(classificationSummary)
            .sort(([,a], [,b]) => b - a)
            .forEach(([classification, count]) => {
              console.log(`  ${classification}: ${count}`);
            });
          
          if (errors.length > 0) {
            console.log('\n⚠️  Errors encountered:');
            errors.forEach((error, index) => {
              console.log(`  ${index + 1}. Row ${error.rowNumber}: ${error.error}`);
            });
          }
          
          resolve({ results, errors, summary: classificationSummary });
        } catch (writeError) {
          reject(writeError);
        }
      })
      .on('error', (error) => {
        reject(error);
      });
  });
}

/**
 * Main function to run the classification
 */
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length < 1) {
    console.log('Usage: node classifyEmails.js <input-csv-file> [output-csv-file] [--debug] [--include-body] [--no-fetch-status]');
    console.log('Example: node classifyEmails.js emails.csv classified_emails.csv');
    console.log('Debug mode: node classifyEmails.js emails.csv --debug');
    console.log('Include body: node classifyEmails.js emails.csv --include-body');
    console.log('Disable email status fetching: node classifyEmails.js emails.csv --no-fetch-status');
    console.log('Environment variables: DEBUG=true INCLUDE_BODY=true FETCH_STATUS=false node classifyEmails.js emails.csv');
    process.exit(1);
  }
  
  const inputFile = args[0];
  const outputFile = args[1] || `classified_${path.basename(inputFile)}`;
  
  if (!fs.existsSync(inputFile)) {
    console.error(`❌ Input file not found: ${inputFile}`);
    process.exit(1);
  }
  
  try {
    console.log(`🚀 Starting email classification...`);
    console.log(`📁 Input file: ${inputFile}`);
    console.log(`📁 Output file: ${outputFile}`);
    console.log(`📋 Available error classifications: ${ERRORS.length}`);
    console.log(`📄 Body column: ${INCLUDE_BODY_COLUMN ? 'included' : 'excluded'}`);
    console.log(`📧 Email status fetching: ${FETCH_EMAIL_STATUS ? 'enabled' : 'disabled'}`);
    
    if (ENABLE_DETAILED_LOGGING) {
      console.log('\n🔍 DEBUG MODE ENABLED - Detailed logging will be shown');
      console.log('💡 To disable debug mode, remove --debug flag or set DEBUG=false');
      console.log('═'.repeat(80));
    }
    
    await classifyEmails(inputFile, outputFile, INCLUDE_BODY_COLUMN, FETCH_EMAIL_STATUS);
  } catch (error) {
    console.error('❌ Classification failed:', error.message);
    throw error;
    process.exit(1);
  }
}

// Export functions for testing
module.exports = {
  parseErrorCode,
  classifyError,
  classifyEmails
};

// Run main function if script is executed directly
if (require.main === module) {
  main();
}