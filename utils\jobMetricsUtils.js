const prisma = require("../database/prisma/getPrismaClient");

/**
 * Calculate metrics from leads data in the database
 * @param {number} jobId - The job ID to calculate metrics for
 * @returns {Promise<object>} Calculated job metrics
 */
const calculateJobMetrics = async (job) => {
  try {
    const statusCounts = await prisma.lead.groupBy({
      by: ['status'],
      where: { jobId: job.id },
      _count: true
    });

    // Process the status counts
    const statusMap = statusCounts.reduce((acc, item) => {
      acc[item.status] = item._count;
      return acc;
    }, {});
    const totalLeads = statusCounts.reduce((acc, item) => acc + item._count, 0);

    const isComplete = !Object.entries(statusMap)
    .some(([status, count]) => 
      status !== 'completed' && 
      status !== 'srp_failed' && 
      status !== 'failed' &&
      count > 0
    );
    const completedLeads = statusMap['completed'] || 0;
    const completionPercentage = totalLeads > 0 ? Math.round((completedLeads / totalLeads) * 100) : 100;

    return {
      isComplete: isComplete,
      statusMap,
      totalLeads,
      completionPercentage,
      status: isComplete ? 'completed' : 'pending'
    };
  } catch (error) {
    console.error(`Error calculating job metrics for job ${job.id}:`, error);
    throw error;
  }
};

/**
 * Gets job metrics either from cached resultJson or by calculating them
 * @param {object} job - The job object which may contain resultJson
 * @returns {Promise<object>} Job metrics including status counts and completion info
 */
const getJobMetrics = async (job) => {
  try {
    // If we have valid cached metrics in resultJson, use them
    if (job?.resultJson?.statusMap && 
        job?.resultJson?.totalLeads && 
        job?.resultJson?.completionPercentage) {
    
      const isComplete = !Object.entries(job.resultJson.statusMap)
        .some(([status, count]) => 
          status !== 'completed' && 
          status !== 'srp_failed' && 
          count > 0
        );

      return {
        isComplete,
        statusMap: job.resultJson.statusMap,
        totalLeads: job.resultJson.totalLeads,
        completionPercentage: job.resultJson.completionPercentage,
        status: isComplete ? 'completed' : 'pending'
      };
    }

    // If no valid cached metrics, calculate them
    return await calculateJobMetrics(job);
  } catch (error) {
    console.error(`Error getting job metrics for job ${job.id}:`, error);
    throw error;
  }
};

/**
 * Process job data to include metrics
 * @param {object} job - The job object with leads data
 * @returns {Promise<object>} Processed job with metrics
 */
const processJobWithMetrics = async (job) => {
  const metrics = await getJobMetrics(job);
  const { leads, _count, ...jobData } = job;
  
  return {
    ...jobData,
    ...metrics
  };
};

module.exports = {
  getJobMetrics,
  processJobWithMetrics,
  calculateJobMetrics
}; 