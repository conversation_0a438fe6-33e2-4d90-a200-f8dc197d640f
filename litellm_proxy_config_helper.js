#!/usr/bin/env node

/**
 * LiteLLM Proxy Configuration Helper
 * 
 * This script helps determine the correct configuration for your LiteLLM proxy
 * by testing various authentication methods and endpoints.
 */

require('dotenv').config();
const axios = require('axios');

const LITELLM_PROXY_URL = 'http://**************:4000';

function printHeader(title) {
    console.log('\n' + '='.repeat(50));
    console.log(`🔧 ${title}`);
    console.log('='.repeat(50));
}

async function checkServerConnectivity() {
    printHeader('Server Connectivity Check');
    
    const endpoints = [
        '',
        '/health',
        '/v1',
        '/v1/models',
        '/docs',
        '/openapi.json'
    ];
    
    for (const endpoint of endpoints) {
        try {
            const url = `${LITELLM_PROXY_URL}${endpoint}`;
            console.log(`🔄 Testing ${url}...`);
            
            const response = await axios.get(url, { 
                timeout: 5000,
                validateStatus: () => true // Accept any status code
            });
            
            console.log(`   Status: ${response.status} ${response.statusText}`);
            
            if (response.status === 200) {
                console.log(`   ✅ Endpoint accessible`);
                if (response.data && typeof response.data === 'object') {
                    console.log(`   Data preview:`, JSON.stringify(response.data).substring(0, 100) + '...');
                }
            } else if (response.status === 401) {
                console.log(`   🔐 Requires authentication`);
            } else if (response.status === 404) {
                console.log(`   ❌ Not found`);
            } else {
                console.log(`   ⚠️  Unexpected status`);
            }
            
        } catch (error) {
            if (error.code === 'ECONNREFUSED') {
                console.log(`   ❌ Connection refused - server may be down`);
            } else if (error.code === 'ETIMEDOUT') {
                console.log(`   ⏰ Timeout - server may be slow or unreachable`);
            } else {
                console.log(`   ❌ Error: ${error.message}`);
            }
        }
    }
}

async function testAuthenticationMethods() {
    printHeader('Authentication Methods Test');
    
    const authMethods = [
        { name: 'No Authentication', headers: {} },
        { name: 'Bearer sk-test', headers: { 'Authorization': 'Bearer sk-test' } },
        { name: 'Bearer sk-anything', headers: { 'Authorization': 'Bearer sk-anything' } },
        { name: 'Bearer test-key', headers: { 'Authorization': 'Bearer test-key' } },
        { name: 'X-API-Key header', headers: { 'X-API-Key': 'test-key' } },
        { name: 'API-Key header', headers: { 'API-Key': 'test-key' } }
    ];
    
    if (process.env.OPENAI_API_KEY) {
        authMethods.push({
            name: 'OpenAI API Key',
            headers: { 'Authorization': `Bearer ${process.env.OPENAI_API_KEY}` }
        });
    }
    
    const testEndpoint = '/v1/models';
    
    for (const method of authMethods) {
        try {
            console.log(`🔄 Testing ${method.name}...`);
            
            const response = await axios.get(`${LITELLM_PROXY_URL}${testEndpoint}`, {
                headers: {
                    'Content-Type': 'application/json',
                    ...method.headers
                },
                timeout: 10000,
                validateStatus: () => true
            });
            
            console.log(`   Status: ${response.status}`);
            
            if (response.status === 200) {
                console.log(`   ✅ Authentication successful!`);
                if (response.data && response.data.data) {
                    console.log(`   Found ${response.data.data.length} models`);
                    console.log(`   Sample models: ${response.data.data.slice(0, 3).map(m => m.id).join(', ')}`);
                }
                return method;
            } else if (response.status === 401) {
                console.log(`   🔐 Authentication failed`);
            } else {
                console.log(`   ⚠️  Unexpected response`);
            }
            
        } catch (error) {
            console.log(`   ❌ Error: ${error.message}`);
        }
    }
    
    return null;
}

async function testChatCompletion(authMethod) {
    if (!authMethod) {
        console.log('\n⚠️  Skipping chat completion test - no working auth method found');
        return false;
    }
    
    printHeader('Chat Completion Test');
    
    try {
        console.log(`🔄 Testing chat completion with ${authMethod.name}...`);
        
        const response = await axios.post(`${LITELLM_PROXY_URL}/v1/chat/completions`, {
            model: 'gpt-3.5-turbo',
            messages: [
                { role: 'user', content: 'Say "Hello from LiteLLM!" and nothing else.' }
            ],
            max_tokens: 20,
            temperature: 0
        }, {
            headers: {
                'Content-Type': 'application/json',
                ...authMethod.headers
            },
            timeout: 30000
        });
        
        if (response.status === 200 && response.data.choices) {
            const message = response.data.choices[0]?.message?.content;
            console.log(`✅ Chat completion successful!`);
            console.log(`Response: "${message}"`);
            console.log(`Model used: ${response.data.model || 'Unknown'}`);
            console.log(`Tokens used: ${response.data.usage?.total_tokens || 'Unknown'}`);
            return true;
        } else {
            console.log(`❌ Unexpected response format`);
            return false;
        }
        
    } catch (error) {
        console.log(`❌ Chat completion failed: ${error.message}`);
        if (error.response?.data) {
            console.log(`Error details:`, JSON.stringify(error.response.data, null, 2));
        }
        return false;
    }
}

async function generateConfigurationGuide(workingAuth) {
    printHeader('Configuration Guide');
    
    if (!workingAuth) {
        console.log('❌ No working authentication method found.');
        console.log('\n💡 Troubleshooting suggestions:');
        console.log('1. Check if the LiteLLM proxy is running on http://**************:4000');
        console.log('2. Verify the proxy configuration allows your IP address');
        console.log('3. Check if the proxy requires specific API keys or tokens');
        console.log('4. Review the LiteLLM proxy logs for error messages');
        return;
    }
    
    console.log('✅ Working configuration found!');
    console.log('\n📋 Configuration Details:');
    console.log(`   Proxy URL: ${LITELLM_PROXY_URL}`);
    console.log(`   Authentication: ${workingAuth.name}`);
    
    if (workingAuth.headers.Authorization) {
        const token = workingAuth.headers.Authorization.replace('Bearer ', '');
        console.log(`   API Key: ${token.substring(0, 10)}...`);
    }
    
    console.log('\n🔧 Usage Examples:');
    
    // JavaScript/Node.js example
    console.log('\n📝 JavaScript (Node.js) with OpenAI library:');
    console.log('```javascript');
    console.log('const OpenAI = require("openai");');
    console.log('');
    console.log('const client = new OpenAI({');
    if (workingAuth.headers.Authorization) {
        const token = workingAuth.headers.Authorization.replace('Bearer ', '');
        console.log(`    apiKey: "${token}",`);
    } else {
        console.log('    apiKey: "not-required", // or any string');
    }
    console.log(`    baseURL: "${LITELLM_PROXY_URL}/v1"`);
    console.log('});');
    console.log('```');
    
    // Python example
    console.log('\n📝 Python with OpenAI library:');
    console.log('```python');
    console.log('from openai import OpenAI');
    console.log('');
    console.log('client = OpenAI(');
    if (workingAuth.headers.Authorization) {
        const token = workingAuth.headers.Authorization.replace('Bearer ', '');
        console.log(`    api_key="${token}",`);
    } else {
        console.log('    api_key="not-required",  # or any string');
    }
    console.log(`    base_url="${LITELLM_PROXY_URL}/v1"`);
    console.log(')');
    console.log('```');
    
    // cURL example
    console.log('\n📝 cURL example:');
    console.log('```bash');
    console.log(`curl -X POST "${LITELLM_PROXY_URL}/v1/chat/completions" \\`);
    console.log('  -H "Content-Type: application/json" \\');
    if (workingAuth.headers.Authorization) {
        console.log(`  -H "${workingAuth.headers.Authorization}" \\`);
    }
    console.log('  -d \'{"model": "gpt-3.5-turbo", "messages": [{"role": "user", "content": "Hello!"}]}\'');
    console.log('```');
    
    console.log('\n💡 Integration Tips:');
    console.log('1. Replace your existing OpenAI baseURL with the LiteLLM proxy URL');
    console.log('2. Use the working API key format shown above');
    console.log('3. Test with simple requests first before integrating into your application');
    console.log('4. Monitor the proxy logs for any issues or rate limiting');
}

async function main() {
    console.log('🚀 LiteLLM Proxy Configuration Helper');
    console.log(`📍 Testing proxy at: ${LITELLM_PROXY_URL}\n`);
    
    try {
        // Step 1: Check basic connectivity
        await checkServerConnectivity();
        
        // Step 2: Test authentication methods
        const workingAuth = await testAuthenticationMethods();
        
        // Step 3: Test actual chat completion
        const chatWorking = await testChatCompletion(workingAuth);
        
        // Step 4: Generate configuration guide
        await generateConfigurationGuide(workingAuth && chatWorking ? workingAuth : null);
        
        console.log('\n🎯 Next Steps:');
        if (workingAuth && chatWorking) {
            console.log('✅ Your proxy is working! Use the configuration above in your applications.');
            console.log('🧪 Run the full test suite: node test_litellm_proxy.js');
        } else {
            console.log('❌ Proxy configuration needs attention. Check the troubleshooting suggestions above.');
        }
        
    } catch (error) {
        console.error('❌ Configuration helper failed:', error.message);
    }
}

if (require.main === module) {
    main();
}
