const cheerio = require("cheerio");
const { EMAIL_BLACKLIST_DOMAINS } = require("../constants");
const emailRegex = /\b[a-zA-Z0-9._%+\-]+@([a-zA-Z0-9\-]+\.)+[a-zA-Z]{2,}\b/i;

const emailPlusRegex =
  /([a-zA-Z0-9._%+-]+)\+([a-zA-Z0-9._%+-]+)@([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/;
/**
 * Cleans email addresses
 * @param {string} email - The email to clean
 * @returns {string} - Cleaned email address
 */
function cleanEmail(email) {
  if (!email) return "";

  // Remove basic URL encoding artifacts
  let cleaned = email
    .replace(/%20/g, "")
    .replace(/%2E/g, ".")
    .replace(/%40/g, "@");

  try {
    const emailPlusRegex = /^([^@+]+)\+([^@]+)@(.+)$/;
    const hasEmailPlus = emailPlusRegex.test(email);

    if (hasEmailPlus) {
      const matches = email.match(emailPlusRegex);
      if (matches) {
        const beforePlus = matches[1];
        const afterPlus = matches[2];
        const domain = matches[3];
        cleaned = `${beforePlus}+${afterPlus}@${domain}`;
      }
    } else {
      // Decode URI, replacing + with space before decoding
      cleaned = decodeURIComponent(cleaned?.replace(/\+/g, " "));
    }
  } catch (e) {
    console.error(`Error decoding email: ${email}`, e);
  }

  if (!cleaned) {
    return "";
  }

  // Remove leading/trailing non-email-safe characters
  cleaned = cleaned.replace(/^[^a-zA-Z0-9._%+-]+/, "");
  cleaned = cleaned.replace(/[^a-zA-Z0-9._%+-]+$/, "");

  // Remove known suffix junk
  cleaned = cleaned.replace(/\.CHANGES$/i, "");
  cleaned = cleaned.replace(/\.NOSPAM$/i, "");
  cleaned = cleaned.replace(/\.REMOVE$/i, "");

  // Fix common disguised email patterns
  if (!cleaned.includes("@") && cleaned.includes("(at)")) {
    cleaned = cleaned.replace(/\(at\)/gi, "@");
  }
  if (!cleaned.includes(".") && cleaned.includes("(dot)")) {
    cleaned = cleaned.replace(/\(dot\)/gi, ".");
  }

  // Collapse multiple @ signs
  const atCount = (cleaned.match(/@/g) || []).length;
  if (atCount > 1) {
    const parts = cleaned.split("@");
    if (parts.length >= 2) {
      cleaned = `${parts[0]}@${parts[1]}`;
    }
  }

  // Fix malformed domains using known TLDs
  if (cleaned.includes("@")) {
    const [localPart, domainPart] = cleaned.split("@");
    const lowerDomainPart = domainPart.toLowerCase();
    const commonTLDs = [
      "com",
      "org",
      "net",
      "edu",
      "gov",
      "io",
      "co",
      "info",
      "biz",
      "app",
      "us",
      "uk",
      "ca",
      "de",
      "jp",
      "fr",
      "au",
      "ru",
      "ch",
      "it",
      "nl",
      "in",
      "se",
      "no",
      "es",
      "mil",
      "int",
      "eu",
      "me",
      "tv",
      "xyz",
      "online",
      "site",
      "tech",
      "store",
      "blog",
      "dev",
      "cloud",
      "io",
      "ai",
      "art",
      "club",
      "design",
      "email",
      "life",
      "live",
      "news",
      "space",
      "today",
      "work",
      "zone",
      "fun",
      "shop",
      "website",
      "group",
      "company",
      "agency",
      "systems",
      "network",
      "digital",
      "solutions",
      "services",
    ];

    let fixed = false;
    for (const tld of commonTLDs) {
      const tldPattern = new RegExp(`\\.(${tld})`, "i"); // match the TLD anywhere
      const match = lowerDomainPart.match(tldPattern);
      if (match) {
        // Extract up to the end of the matched TLD
        const properDomain = lowerDomainPart.substring(
          0,
          match.index + tld.length + 1
        );
        fixed = true;
        cleaned = `${localPart}@${properDomain}`;
        break;
      }
    }
    // If domain is already valid, just normalize case
    if (!fixed) {
      cleaned = `${localPart}@${lowerDomainPart}`;
    }
  }

  return cleaned.trim();
}

/**
 * Validates if an email address is valid
 * @param {string} email - The email to validate
 * @returns {boolean} - True if email is valid
 */
function isValidEmail(email) {
  if (!email) return false;

  // Trim the email first
  email = email.trim();

  // Check if email passes basic regex validation
  if (!emailRegex.test(email)) return false;

  // Get the domain part of the email
  const domain = email.split("@")[1].toLowerCase();
  
  // Check against the blacklist
  if (EMAIL_BLACKLIST_DOMAINS.includes(domain)) {
    console.log(`Ignoring blacklisted email domain: ${domain}`);
    return false;
  }

  // Validate domain parts (each part should be 63 chars or less per RFC)
  const domainParts = domain.split(".");
  if (domainParts.some((part) => part.length > 63)) return false;

  // Check for repeated characters which might indicate fake emails
  const localPart = email.split("@")[0];
  if (/(.)\1{5,}/.test(localPart)) return false;

  // Don't allow emails with more than one @ sign after cleaning
  if ((email.match(/@/g) || []).length !== 1) return false;

  return true;
}

/**
 * Extracts emails from HTML body text
 * @param {string} html - HTML content
 * @param {string} pageUrl - URL of the page being processed
 * @returns {string[]} - Array of valid emails
 */
function extractEmailsFromText(html, pageUrl) {
  if (!html) return [];

  try {
    const $ = cheerio.load(html);
    // Remove script and style elements to clean the text
    $("script, style, noscript, iframe").remove();
    // Get all text from the page and remove HTML tags
    let text = $("body").html() || "";

    // Replace HTML tags with spaces
    text = text?.replace(/<[^>]*>/g, " ").trim();
    // Improved regex for email addresses that requires proper domain format
    // Extract emails and clean them
    const matches = text?.match(emailRegex) || [];
    let foundEmails = matches.map((email) => cleanEmail(email));

    // Filter only valid emails
    foundEmails = foundEmails.filter((email) => isValidEmail(email));
    return [...new Set(foundEmails)]; // Remove duplicates
  } catch (error) {
    console.error(
      `Error extracting emails from text for ${pageUrl}: ${error.message}`
    );
    return [];
  }
} /**
 * Extracts emails from mailto: links
 * @param {string} html - HTML content
 * @returns {string[]} - Array of valid emails
 */
function extractEmailsFromMailto(html) {
  if (!html) return [];

  try {
    const $ = cheerio.load(html);
    const emails = new Set();

    // Find all <a> elements with href that starts with 'mailto:'
    $('a[href^="mailto:"]').each((_, element) => {
      const href = $(element).attr("href");

      if (href) {
        // Extract email from mailto: link
        let email = href?.replace(/^mailto:/i, "").split("?")[0];

        // Force lowercase domain part
        if (email.includes("@")) {
          const [localPart, domainPart] = email.split("@");
          email = `${localPart}@${domainPart.toLowerCase()}`;
        }

        // Clean and validate the email
        email = cleanEmail(email);

        if (isValidEmail(email)) {
          emails.add(email);
        }
      }
    });

    return [...emails];
  } catch (error) {
    console.error(
      `Error extracting emails from mailto links: ${error.message}`
    );
    return [];
  }
}

/**
 * Combines both email extraction methods
 * @param {string} html - HTML content
 * @param {string} pageUrl - URL of the page being processed
 * @returns {string[]} - Array of unique valid emails
 */
function extractAllEmails(html, pageUrl) {
  try {
    const textEmails = extractEmailsFromText(html, pageUrl);
    const mailtoEmails = extractEmailsFromMailto(html);
    const obfuscatedEmails = extractObfuscatedEmails(html);
    // Combine and deduplicate emails
    return [...new Set([...textEmails, ...mailtoEmails, ...obfuscatedEmails])];
  } catch (error) {
    console.error(`Error extracting all emails: ${error.message}`);
    return [];
  }
}

/**
 * Extracts and decodes email addresses obfuscated using Cloudflare's email protection.
 *
 * @param {string} html - The HTML content as a string.
 * @returns {string[] | null} - An array of decoded email addresses if found, otherwise `null`.
 */
function extractObfuscatedEmails(html) {
  try {
    const $ = cheerio.load(html);
    let emails = [];

    // 1. Cloudflare protected emails using `data-cfemail` attribute
    $("[data-cfemail]").each((_, el) => {
      const encoded = $(el).attr("data-cfemail");
      if (encoded) {
        emails.push(decodeCFEmail(encoded));
      }
    });

    // 2. Custom hex strings in visible text (e.g., <a class="eml-protected">...</a>)
    $("a.eml-protected").each((_, el) => {
      const text = $(el).text().trim();
      if (/^[a-fA-F0-9]{4,}$/.test(text)) {
        // crude check for hex
        emails.push(decodeCFEmail(text));
      }
    });

    return emails.length ? emails : [];
  } catch (error) {
    console.error("ERROR in getObsfucatedEmails:", error);
    return null;
  }
}

/**
 * Decodes a Cloudflare-obfuscated email address.
 * Cloudflare uses a simple XOR-based hex encoding to hide email addresses in HTML.
 * This function reverses the encoding to retrieve the original email string.
 *
 * @param {string} cfemail - The obfuscated email string in hexadecimal format (e.g., "6a0f2e2f212c").
 * @returns {string} - The decoded, human-readable email address.
 */
function decodeCFEmail(cfemail) {
  const hexToInt = (hex) => parseInt(hex, 16);

  let email = "";
  const key = hexToInt(cfemail.slice(0, 2));

  for (let i = 2; i < cfemail.length; i += 2) {
    const charCode = hexToInt(cfemail.slice(i, i + 2)) ^ key;
    email += String.fromCharCode(charCode);
  }

  return email;
}

module.exports = {
  cleanEmail,
  isValidEmail,
  extractEmailsFromText,
  extractEmailsFromMailto,
  extractAllEmails,
  extractObfuscatedEmails,
};

async function main(url) {
  if (!url) {
    return {
      emails: [],
      status: "Error: No URL provided",
    };
  }

  try {
    // Fetch the URL
    const response = await fetch(url, {
      headers: {
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
      },
      timeout: 30000, // 30 seconds timeout
    });

    if (!response.ok) {
      return {
        emails: [],
        status: `Error: Failed to fetch URL (${response.status} ${response.statusText})`,
      };
    }

    // Get HTML content
    const html = await response.text();

    // Extract emails from the HTML
    const emails = extractAllEmails(html, url);
    return {
      emails,
      status: "Success",
      url,
    };
  } catch (error) {
    console.error(`Error in main function: ${error.message}`);
    return {
      emails: [],
      status: `Error: ${error.message}`,
    };
  }
}

// main("https://mybag.no/pages/vare-butikker");
