const fs = require("fs");
const csv = require("csv-parser");
const createCsvWriter = require("csv-writer").createObjectCsvWriter;

const inputFile = "sample/smartlead-data/input.csv";
const outputFile = "sample/smartlead-data/output.csv";

const timeFields = ["Sent Time", "Opened Time", "Clicked Time", "Replied Time"];

// Convert PDT to UTC
function convertToUTC(dateStr) {
  if (!dateStr || dateStr.trim() === "") return dateStr;
  const localDate = new Date(dateStr); // PDT recognized by JS
  return localDate.toISOString().replace("T", " ").replace("Z", " UTC");
}

const rows = [];

fs.createReadStream(inputFile)
  .pipe(csv())
  .on("data", (row) => {
    for (const field of timeFields) {
      if (row[field]) {
        row[field] = convertToUTC(row[field]);
      }
    }
    rows.push(row);
  })
  .on("end", () => {
    const headers = Object.keys(rows[0]).map((key) => ({
      id: key,
      title: key,
    }));

    const csvWriter = createCsvWriter({
      path: outputFile,
      header: headers,
    });

    csvWriter.writeRecords(rows).then(() => {
      console.log("✅ Converted times to UTC and saved to", outputFile);
    });
  });
