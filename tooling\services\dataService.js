const { sheets } = require('../config/index');
const funnelConfig = require('../config/funnelConfig');
const { sanitizeObject, sanitizeString } = require('../utils/sanitizer');
const createCsvWriter = require('csv-writer').createObjectCsvWriter;
const { companySchemaMap } = require('../../middlewares/schema');
const { extractMarketplaceFromUrl, isValidSellerUrlFormat } = require('../../utils/marketplaceDetector');

class DataService {
  async processData(data, lookupSource, mode = 'CompanyUpdate', funnelName = null, subFunnelName = null, useNewEndpoints = false) {
    console.log(`Processing data in ${mode} mode with${lookupSource ? '' : 'out'} lookup source...`);
    console.log(`Funnel: ${funnelName || 'Not specified'}`);
    if (subFunnelName) {
      console.log(`SubFunnel: ${subFunnelName}`);
    }

    try {
      // Determine funnel configuration if provided
      let funnelSettings = null;
      let subFunnelSettings = null;
      
      if (funnelName && funnelConfig[funnelName]) {
        funnelSettings = funnelConfig[funnelName];
        console.log(`Found main funnel configuration: ${funnelName}`);
        
        // Check if subfunnel exists and get its settings
        if (subFunnelName && funnelSettings.subFunnels && funnelSettings.subFunnels[subFunnelName]) {
          subFunnelSettings = funnelSettings.subFunnels[subFunnelName];
          console.log(`Found subfunnel configuration: ${subFunnelName}`);
        }
        
        // Log if we're using new API endpoints
        if (useNewEndpoints) {
          console.log(`Using NEW API endpoints for ${funnelName}${subFunnelName ? ' > ' + subFunnelName : ''}`);
        }
      }

      // For modes other than CompanyUpdate, skip validation and just sanitize the data
      if (mode === 'CompanyUpdate') {
        // Company Update mode with full validation
        console.log('Performing full validation in CompanyUpdate mode');
        
        // Prioritize subfunnel field requirements over parent funnel
        // For required fields:
        // 1. If using new API and subfunnel has updatedApiRequiredFields, use those
        // 2. If using new API and main funnel has updatedApiRequiredFields, use those
        // 3. Use subfunnel's requiredFields if available
        // 4. Otherwise, use main funnel's requiredFields
        // 5. If neither exists, use default field requirements
        
        let requiredFields;
        
        // Check for updated API required fields if using new endpoints
        if (useNewEndpoints) {
          requiredFields = subFunnelSettings?.updatedApiRequiredFields || funnelSettings?.updatedApiRequiredFields;
          if (requiredFields) {
            console.log('Using updatedApiRequiredFields for new API endpoints');
          }
        }
        
        // Fall back to regular required fields if no updated fields were found or not using new endpoints
        requiredFields = requiredFields || subFunnelSettings?.requiredFields || funnelSettings?.requiredFields || {
          website_status: companySchemaMap.website_status,
          website: companySchemaMap.website,
          amazon_seller_id: companySchemaMap.amazon_seller_id,
          name: companySchemaMap.name
        };
        
        // Similarly for optional fields: prioritize subfunnel first
        const optionalFields = subFunnelSettings?.optionalFields || funnelSettings?.optionalFields || {};
        
        // Log which field requirements we're using
        if (subFunnelSettings?.requiredFields) {
          console.log(`Using field requirements from subfunnel: ${subFunnelName}`);
        } else if (funnelSettings?.requiredFields) {
          console.log(`Using field requirements from funnel: ${funnelName}`);
        } else {
          console.log('Using default field requirements');
        }

        // Track all known fields (required + optional) to ensure we only include defined fields
        const allKnownFields = new Set(Object.keys(requiredFields).concat(Object.keys(optionalFields)));
        console.log(`Total known fields from configuration: ${allKnownFields.size}`);
        
        const processedData = data.map((row, rowIndex) => {
          const sanitizedRow = sanitizeObject(row);
          const filteredData = {};
          
          // Process each required field
          for (const [field, possibleNames] of Object.entries(requiredFields)) {
            if (!Array.isArray(possibleNames)) {
              console.error(`Schema mapping missing for field: ${field}`);
              throw new Error(`Schema mapping missing for field: ${field}`);
            }
            
            const value = possibleNames.reduce((found, name) => {
              if (found) return found;
              const key = Object.keys(sanitizedRow).find(k => k.toLowerCase() === name.toLowerCase());
              return key ? sanitizedRow[key] : undefined;
            }, undefined);
            
            // Check if the field exists in the headers (is present)
            const fieldExists = possibleNames.some(name => 
              Object.keys(sanitizedRow).some(k => k.toLowerCase() === name.toLowerCase())
            );
            
            // Validate field presence based on requirements
            // Allow marketplace to be missing if using new endpoints (it can be auto-detected from seller_url)
            const isMarketplaceAutoDetectable = field === 'marketplace' && useNewEndpoints;
            if (!fieldExists && field !== 'seller_url' && !isMarketplaceAutoDetectable && !(field === 'website' && filteredData.website_status !== 'Final Correct')) {
              throw new Error(`Required field "${field}" not found in data at row ${rowIndex + 2} (Excel row number). Possible column names: ${possibleNames.join(', ')}`);
            }
            
            // Special handling for website field based on website_status
            if (field === 'website') {
              const websiteStatus = filteredData.website_status; // We process fields in order, so website_status is already set
              if (websiteStatus === 'Final Correct' || websiteStatus === 'Maybe') {
                if (websiteStatus === 'Final Correct' && !value) {
                  throw new Error(`Website is required when website_status is 'Final Correct' at row ${rowIndex + 2} (Excel row number)`);
                }
                filteredData[field] = value;
              } else {
                filteredData[field] = '';
              }
            } else if (field === 'seller_url') {
              filteredData[field] = value || ''; // Set empty string if seller_url not found
            } else if (field === 'marketplace') {
              // Marketplace can be auto-detected from seller_url when using new endpoints
              if (useNewEndpoints && !fieldExists) {
                // Will be populated later by marketplace detection logic
                filteredData[field] = value || '';
              } else {
                // Standard validation when marketplace column is present
                if (!value) {
                  throw new Error(`Required field "${field}" cannot be empty at row ${rowIndex + 2} (Excel row number)`);
                }
                filteredData[field] = value;
              }
            } else {
              if (!value) {
                throw new Error(`Required field "${field}" cannot be empty at row ${rowIndex + 2} (Excel row number)`);
              }
              filteredData[field] = value;
            }
          }
          
          // Process optional fields
          for (const [field, possibleNames] of Object.entries(optionalFields)) {
            if (!Array.isArray(possibleNames)) {
              console.log(`Schema mapping missing for optional field: ${field}, skipping`);
              continue;
            }
            
            const value = possibleNames.reduce((found, name) => {
              if (found) return found;
              const key = Object.keys(sanitizedRow).find(k => k.toLowerCase() === name.toLowerCase());
              return key ? sanitizedRow[key] : undefined;
            }, undefined);
            
            // For optional fields, just set the value if it exists
            if (field === 'seller_url') {
              filteredData[field] = value || ''; // Set empty string if seller_url not found
            } else if (value !== undefined) {
              filteredData[field] = value;
            }
          }

          // Marketplace detection and validation logic
          // This handles the business rules for seller_url and marketplace validation
          if (useNewEndpoints) {
            const hasSellerUrl = 'seller_url' in filteredData;
            const hasMarketplace = 'marketplace' in filteredData;
            const sellerUrlValue = filteredData.seller_url;
            const marketplaceValue = filteredData.marketplace;

            // Check if marketplace column exists in the input data
            const marketplaceColumnExists = possibleNames => 
              possibleNames.some(name => 
                Object.keys(sanitizedRow).some(k => k.toLowerCase() === name.toLowerCase())
              );
            
            const marketplaceFieldExists = marketplaceColumnExists(requiredFields.marketplace || []);

            console.log(`Row ${rowIndex + 2}: hasSellerUrl=${hasSellerUrl}, hasMarketplace=${hasMarketplace}, sellerUrlValue="${sellerUrlValue}", marketplaceValue="${marketplaceValue}", marketplaceColumnExists=${marketplaceFieldExists}`);

            // Rule 1: If seller URL is specified and it is blank, then an error
            if (hasSellerUrl && sellerUrlValue === '') {
              throw new Error(`Seller URL cannot be blank when specified at row ${rowIndex + 2} (Excel row number)`);
            }

            // Rule 2: If marketplace is specified and seller URL is not specified and no input entry for marketplace then an error
            if (hasMarketplace && !hasSellerUrl && !marketplaceValue) {
              throw new Error(`Marketplace is specified but seller URL is not provided and marketplace value is empty at row ${rowIndex + 2} (Excel row number)`);
            }

            // Rule 3: If marketplace column is not in input data OR marketplace is empty, and seller URL is specified, try to extract marketplace
            if ((!marketplaceFieldExists || !marketplaceValue) && hasSellerUrl && sellerUrlValue) {
              // Validate seller URL format first
              if (!isValidSellerUrlFormat(sellerUrlValue)) {
                throw new Error(`Invalid seller URL format at row ${rowIndex + 2} (Excel row number). Expected format: amazon.{domain}/{path}`);
              }

              // Try to extract marketplace from seller URL
              const detectedMarketplace = extractMarketplaceFromUrl(sellerUrlValue);
              if (!detectedMarketplace) {
                throw new Error(`Unable to detect marketplace from seller URL "${sellerUrlValue}" at row ${rowIndex + 2} (Excel row number). Please provide marketplace explicitly or use a supported Amazon domain.`);
              }

              // Set the detected marketplace
              filteredData.marketplace = detectedMarketplace;
              console.log(`Detected marketplace "${detectedMarketplace}" from seller URL "${sellerUrlValue}" at row ${rowIndex + 2}`);
            }

            // Additional validation: If both seller_url and marketplace are provided, verify they match
            if (hasSellerUrl && hasMarketplace && sellerUrlValue && marketplaceValue) {
              const detectedMarketplace = extractMarketplaceFromUrl(sellerUrlValue);
              if (detectedMarketplace && detectedMarketplace !== marketplaceValue) {
                console.warn(`Marketplace mismatch at row ${rowIndex + 2}: seller URL suggests "${detectedMarketplace}" but marketplace is set to "${marketplaceValue}". Using provided marketplace value.`);
              }
            }

            // Remove seller_url from the final data for new API endpoints
            // The seller_url was only needed for marketplace detection and should not be sent to the API
            if (hasSellerUrl) {
              delete filteredData.seller_url;
              console.log(`Removed seller_url from output data for row ${rowIndex + 2} (used only for marketplace detection)`);
            }
          }

          // Handle lookup source based on rules for UpdateCompanies mode
          let determinedLookupSource = null;
          
          // For lookup source rules, prioritize subfunnel over parent funnel
          // Check if there are lookup source rules for this funnel/subfunnel
          const lookupSourceRules = subFunnelSettings?.lookupSourceRules || funnelSettings?.lookupSourceRules;
          if (lookupSourceRules) {
            const websiteStatus = filteredData.website_status || '';
            let defaultSource = null;
            
            // First check for direct match rules if website_status exists
            const directRuleSource = websiteStatus ? lookupSourceRules[websiteStatus] : null;
            if (directRuleSource) {
              // console.log(`Applying direct lookup source rule for status '${websiteStatus}': ${directRuleSource}`);
              determinedLookupSource = directRuleSource;
            } else {
              // Check for 'EXCEPT:' rules if no direct match was found and website_status exists
              let exceptRuleApplied = false;
              
              for (const [rule, source] of Object.entries(lookupSourceRules)) {
                if (rule.startsWith('EXCEPT:')) {
                  // Only apply EXCEPT rules if website_status exists
                  if (websiteStatus) {
                    const exceptValue = rule.replace('EXCEPT:', '');
                    if (websiteStatus !== exceptValue) {
                      console.log(`Applying 'EXCEPT' lookup source rule: ${rule} with value: ${source}`);
                      determinedLookupSource = source;
                      exceptRuleApplied = true;
                      break; // Use the first matching EXCEPT rule
                    }
                  }
                } else if (rule === 'DEFAULT:') {
                  // Store the default rule for potential use if no other rules match
                  defaultSource = source;
                }
              }
              
              // Apply DEFAULT rule if:
              // 1. No EXCEPT rule was applied AND
              // 2. Either website_status is empty OR no direct match was found
              if (!exceptRuleApplied && defaultSource) {
                console.log(`Applying 'DEFAULT' lookup source rule with value: ${defaultSource}`);
                determinedLookupSource = defaultSource;
              }
            }
          }
          
          // Set the determined lookup source if one was found
          if (determinedLookupSource) {
            filteredData.lookup_source = sanitizeString(determinedLookupSource);
            // console.log(`Using rule-based lookup source: ${determinedLookupSource}`);
          } else {
            // No lookup source determined from rules
            console.log('No lookup source determined from funnel rules');
          }
          // Removed sheet-based lookup source as per requirement

          // Convert "No Validator Match (New Process)" to "Failed"
          if (filteredData.website_status === 'No Validator Match (New Process)') {
            filteredData.website_status = 'Failed';
          }
          
          // Special handling for lookup_source - we want to include it even if not explicitly in required/optional fields
          if (filteredData.lookup_source && !allKnownFields.has('lookup_source')) {
            allKnownFields.add('lookup_source');
          }

          // Return an object with only the known fields to ensure we're not sending extra data to the API
          const finalData = {};
          Object.keys(filteredData).forEach(key => {
            if (allKnownFields.has(key)) {
              finalData[key] = filteredData[key];
            }
          });
          
          return finalData;
        });

        await this._writeProcessedData(processedData);
        return processedData;
      } else {
        console.log("coming here or not?? - 2")
        // For UpdateProspects mode, handle source differently
        console.log(`Bypassing validation checks in ${mode} mode`);
        
        // Even for non-validation modes, get the required fields if available to filter output
        let requiredFieldsList = [];
        if (mode === 'UpdateProspects') {
          // For UpdateProspects, determine the required fields to include from config
          if (subFunnelSettings?.requiredFields) {
            requiredFieldsList = Object.keys(subFunnelSettings.requiredFields);
            console.log(`Using required fields from subfunnel: ${subFunnelName}`);
          } else if (funnelSettings?.requiredFields) {
            requiredFieldsList = Object.keys(funnelSettings.requiredFields);
            console.log(`Using required fields from funnel: ${funnelName}`);
          }
          // Always include 'source' for UpdateProspects mode
          if (!requiredFieldsList.includes('source')) {
            requiredFieldsList.push('source');
          }
        }
        
        const processedData = data.map(row => {
          const sanitizedRow = sanitizeObject(row);
          const filteredData = {}; // Start with an empty object and only add required fields
          
          // Handle source in UpdateProspects mode (different field name: source instead of lookup_source)
          if (mode === 'UpdateProspects') {
            // Priority for source value:
            // 1. User-provided lookupSource parameter
            // 2. sourceValueRules (if defined)
            // 3. SubFunnel-specific sourceValue
            // 4. Funnel-specific sourceValue 
            // 5. Value from the sheet
            
            let sourceValue = null;
            
            // First check for user-provided lookupSource
            if (lookupSource !== null) {
              sourceValue = sanitizeString(lookupSource);
              console.log(`Using user-provided source: ${lookupSource}`);
            } else {
              // Check for sourceValueRules (similar to lookupSourceRules but for prospect mode)
              const sourceValueRules = subFunnelSettings?.sourceValueRules || funnelSettings?.sourceValueRules;
              
              if (sourceValueRules) {
                // Get the email_status from the current row
                const emailStatus = sanitizedRow?.email_status || '';
                let defaultSource = null;
                
                // First check for direct match rules if email_status exists
                const directRuleSource = emailStatus ? sourceValueRules[emailStatus] : null;
                if (directRuleSource) {
                  console.log(`Applying direct source value rule for email_status '${emailStatus}': ${directRuleSource}`);
                  sourceValue = sanitizeString(directRuleSource);
                } else {
                  // Check for 'EXCEPT:' rules if no direct match was found and email_status exists
                  let exceptRuleApplied = false;
                  
                  for (const [rule, source] of Object.entries(sourceValueRules)) {
                    if (rule.startsWith('EXCEPT:')) {
                      // Only apply EXCEPT rules if email_status exists
                      if (emailStatus) {
                        const exceptValue = rule.replace('EXCEPT:', '');
                        if (emailStatus !== exceptValue) {
                          console.log(`Applying 'EXCEPT' source value rule: ${rule} with value: ${source}`);
                          sourceValue = sanitizeString(source);
                          exceptRuleApplied = true;
                          break; // Use the first matching EXCEPT rule
                        }
                      }
                    } else if (rule === 'DEFAULT:') {
                      // Store the default rule for potential use if no other rules match
                      defaultSource = source;
                    }
                  }
                  
                  // Apply DEFAULT rule if no other rules matched
                  if (!exceptRuleApplied && defaultSource) {
                    console.log(`Applying 'DEFAULT' source value rule with value: ${defaultSource}`);
                    sourceValue = sanitizeString(defaultSource);
                  }
                }
                
                if (sourceValue) {
                  console.log(`Using rule-based source value: ${sourceValue}`);
                }
              }
              
              // Fall back to configured sourceValue if no rules matched
              if (!sourceValue) {
                if (subFunnelSettings?.sourceValue) {
                  sourceValue = sanitizeString(subFunnelSettings.sourceValue);
                  console.log(`Using subfunnel-defined source: ${subFunnelSettings.sourceValue}`);
                } else if (funnelSettings?.sourceValue) {
                  sourceValue = sanitizeString(funnelSettings.sourceValue);
                  console.log(`Using funnel-defined source: ${funnelSettings.sourceValue}`);
                }
              }
            }
            
            if (sourceValue) {
              filteredData.source = sourceValue;
            }
            
            // Copy only required fields from input to output
            requiredFieldsList.forEach(field => {
              if (field !== 'source') { // 'source' is already handled above
                // Get the schema mapping array for this field
                const schemaMapping = (subFunnelSettings?.requiredFields && subFunnelSettings.requiredFields[field]) || 
                                     (funnelSettings?.requiredFields && funnelSettings.requiredFields[field]);
                
                if (Array.isArray(schemaMapping)) {
                  // Use field mapping array like in CompanyUpdate mode
                  let foundValue = undefined;
                  let foundKey = null;
                  
                  // Try to find a match using the schema mapping
                  for (const name of schemaMapping) {
                    const key = Object.keys(sanitizedRow).find(k => k.toLowerCase() === name.toLowerCase());
                    if (key) {
                      foundValue = sanitizedRow[key];
                      foundKey = key;
                      break;
                    }
                  }
                  
                  if (foundValue !== undefined) {
                    filteredData[field] = foundValue;
                    console.log(`Found ${field} using schema mapping: ${foundKey}`);
                  }
                } else {
                  // Fall back to direct field name matching (case-insensitive)
                  const key = Object.keys(sanitizedRow).find(k => k.toLowerCase() === field.toLowerCase());
                  if (key) {
                    filteredData[field] = sanitizedRow[key];
                  }
                }
              }
            });
            console.log(`Filtered data to include only ${requiredFieldsList.length} required fields`);
            // No backward compatibility - if no required fields, only source will be included
          } else {
            // For other non-CompanyUpdate modes, use lookup_source field
            if (lookupSource !== null) {
              filteredData.lookup_source = sanitizeString(lookupSource);
            } else {
              const sheetLookupSource = companySchemaMap.lookup_source?.reduce((found, name) => {
                return found || sanitizedRow[name];
              }, undefined);
              if (sheetLookupSource) {
                filteredData.lookup_source = sanitizeString(sheetLookupSource);
              }
            }
            
            // Only include lookup_source field - strict mode, no backward compatibility
            // Other fields will not be included unless they are explicitly required
          }
          return filteredData;
        });
        await this._writeProcessedData(processedData);
        return processedData;
      }
    } catch (error) {
      console.error("Error processing data:", error);
      throw error;
    }
  }

  async _writeProcessedData(data) {
    if (!data.length) return;

    const csvWriter = createCsvWriter({
      path: sheets.OUTPUT_CSV_FILE,
      header: Object.keys(data[0]).map(key => ({
        id: key,
        title: key
      })),
      alwaysQuote: true
    });

    await csvWriter.writeRecords(data);
    console.log(`✅ Processed CSV file saved: ${sheets.OUTPUT_CSV_FILE}`);
  }
}

module.exports = new DataService();
