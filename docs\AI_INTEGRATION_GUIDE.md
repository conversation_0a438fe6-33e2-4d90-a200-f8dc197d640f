# AI Integration Guide

This guide explains how Azure OpenAI is integrated with Portkey throughout the SellerBot application.

## Overview

The application now uses a **Centralized AI Service** that automatically routes all AI requests through Portkey when available, with intelligent fallback to direct Azure OpenAI when needed.

## Architecture

```
Application Code
       ↓
Centralized AI Service
       ↓
   Portkey Gateway ← (Primary)
       ↓
   Azure OpenAI
       
   Direct Azure OpenAI ← (Fallback)
```

## Key Components

### 1. Centralized AI Service (`services/ai/centralizedAIService.js`)

The main service that handles all AI operations:
- **Automatic routing** through Portkey with fallback
- **Consistent error handling** across the application
- **Performance monitoring** and logging
- **Health status** checking

### 2. Updated ScrapeGPT Services

- `services/scrapeGPT/request.js` - Chat completions
- `services/scrapeGPT/assistant.js` - Assistant API calls
- `services/scrapeGPT/factory.js` - Template-based completions

### 3. Enhanced Configuration (`config/portkey.js`)

- Azure OpenAI deployment configuration
- Model routing based on use case
- Environment variable validation

## Environment Variables

### Required Variables
```bash
# Azure OpenAI Configuration
OPENAI_API_KEY=your_azure_openai_api_key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com
AZURE_OPENAI_DEPLOYMENT=gpt-4o
AZURE_OPENAI_API_VERSION=2024-12-01-preview
OPENAI_MODEL_ID=gpt-4o

# Portkey Configuration
PORTKEY_BASE_URL=http://**************:8787
```

### Optional Variables
```bash
# Portkey API Key (for advanced features)
PORTKEY_API_KEY=your_portkey_api_key

# Virtual Keys for different providers
PORTKEY_VIRTUAL_KEY_AZURE=your_azure_virtual_key
PORTKEY_VIRTUAL_KEY_OPENAI=your_openai_virtual_key

# LangSmith Integration
LANGSMITH_API_KEY=your_langsmith_api_key
LANGSMITH_PROJECT=SellerBot
```

## Usage Examples

### Basic Chat Completion
```javascript
const { centralizedAI } = require('./services/ai/centralizedAIService');

const messages = [
  { role: 'system', content: 'You are a helpful assistant.' },
  { role: 'user', content: 'Hello!' }
];

const result = await centralizedAI.createChatCompletion(messages, {
  temperature: 0.7,
  max_tokens: 100,
}, 'azure', true); // useCase: 'azure', usePortkey: true

console.log(result.message); // AI response
console.log(result.provider); // 'portkey' or 'azure-direct'
```

### Using ScrapeGPT Services
```javascript
const { getChatGPTResponse } = require('./services/scrapeGPT/request');

const result = await getChatGPTResponse(
  'You are a helpful assistant.',
  'Analyze this text...',
  true // usePortkey
);
```

### Using Factory Methods
```javascript
const { completionFactory } = require('./services/scrapeGPT/factory');

const result = await completionFactory('match_text', {
  textContent: 'Company website content...',
  businessKeywords: ['Company Name', 'Product'],
  url: 'https://example.com'
});
```

## Testing

### Run All Tests
```bash
npm test
```

### Run Specific Tests
```bash
# Test Azure OpenAI and Portkey integration
npm run test:azure

# Test Azure deployments and list available models
npm run test:deployments

# Test individual components
node test_complete_ai_integration.js
```

### Test Output Example
```
🧪 Centralized AI Service Test
✅ Chat Completion: PASSED - Response: "Centralized AI Service test successful!"
✅ Provider Routing: PASSED - Used: portkey
✅ Response Time: PASSED - Duration: 1250ms
```

## Health Monitoring

Check the health status of the AI integration:

```javascript
const healthStatus = centralizedAI.getHealthStatus();
console.log(healthStatus);
```

Output:
```json
{
  "portkey": {
    "azure": true,
    "chat": true,
    "errors": []
  },
  "fallback": {
    "azure": true,
    "openai": false
  },
  "environment": {
    "hasAzureEndpoint": true,
    "hasAzureDeployment": true,
    "hasOpenAIKey": true,
    "hasPortkeyUrl": true
  }
}
```

## Benefits

### 1. **Reliability**
- Automatic fallback to direct Azure OpenAI if Portkey is unavailable
- Consistent error handling and retry logic

### 2. **Observability**
- Request/response logging through Portkey
- LangSmith integration for tracing
- Performance monitoring

### 3. **Cost Management**
- Token usage tracking
- Cost monitoring and alerts
- Rate limiting

### 4. **Flexibility**
- Easy switching between providers
- A/B testing capabilities
- Model routing based on use case

## Troubleshooting

### Common Issues

1. **"Missing required Azure OpenAI environment variables"**
   - Check that all required environment variables are set
   - Verify the `.env` file is loaded correctly

2. **"Portkey failed, trying fallback"**
   - Check Portkey base URL accessibility
   - Verify Azure OpenAI configuration for fallback

3. **"No fallback clients available"**
   - Ensure Azure OpenAI credentials are correct
   - Check deployment name matches your Azure resource

### Debug Mode

Enable detailed logging:
```bash
PORTKEY_LOGGING=true
PORTKEY_LOG_LEVEL=debug
PORTKEY_LOG_REQUESTS=true
PORTKEY_LOG_RESPONSES=true
```

## Migration Notes

### What Changed

1. **All AI calls now go through the Centralized AI Service**
2. **Portkey is used by default** with automatic fallback
3. **Environment variables updated** to include Azure deployment
4. **Test scripts enhanced** with comprehensive integration testing

### Backward Compatibility

- All existing function signatures remain the same
- Fallback behavior ensures no service interruption
- Gradual migration path available

## Performance

- **Average response time**: ~1-3 seconds
- **Fallback time**: <500ms if Portkey unavailable
- **Caching**: Semantic caching through Portkey reduces costs
- **Rate limiting**: Automatic handling of API limits

## Next Steps

1. **Monitor performance** using the test scripts
2. **Configure LangSmith** for advanced tracing
3. **Set up cost alerts** in Portkey dashboard
4. **Implement A/B testing** for different models
