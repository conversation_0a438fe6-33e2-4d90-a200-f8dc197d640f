const {
  removeSpecialCharacters,
} = require("../../../utils/removeSpecialCharacters");
const stringSimilarity = require("string-similarity");

async function checkSerpOrganicSnippet(organicData, keywords) {
  // console.log("   Checking for Snippets: ", keywords);
  let matchedKeywords = [];

  try {
    const cleanedKeywords = keywords.map((keyword) =>
      removeSpecialCharacters(keyword),
    );
    const searchSnippet = removeSpecialCharacters(organicData.snippet);
    const searchTitle = removeSpecialCharacters(organicData.title);
    const searchTitleSite = removeSpecialCharacters(organicData.site_title);

    for (const keyword of cleanedKeywords) {
      const isFullMatch =
        searchSnippet.includes(keyword) ||
        searchTitle.includes(keyword) ||
        searchTitleSite.includes(keyword);
      if (isFullMatch) {
        matchedKeywords.push(keyword);
        continue;
      }
      for (const words of searchSnippet.split(" ")) {
        if (stringSimilarity.compareTwoStrings(keyword, words) > 0.5) {
          matchedKeywords.push(keyword);
        }
      }
    }

    if (matchedKeywords.length > 0) {
      return {
        matchedKeywords,
        finalScore:
          matchedKeywords.length * checkSerpOrganicSnippet.config.score,
        url: organicData.originalUrl,
        keywords: keywords,
      };
    }
    return {
      matchedKeywords,
      finalScore: 0,
      url: organicData.originalUrl,
      keywords: keywords,
    };
  } catch (error) {
    console.error(error);
    return {
      matchedKeywords,
      url: organicData.originalUrl,
      finalScore: 0,
      error: error.message,
      keywords: keywords,
    };
  }
}
checkSerpOrganicSnippet.config = {
  score: 0.5,
  key: "checkSerpOrganicSnippet",
};

module.exports = checkSerpOrganicSnippet;
