#!/usr/bin/env node

/**
 * Comprehensive AI Integration Test Script
 * 
 * This script tests the complete AI integration including:
 * - Azure OpenAI direct connection
 * - Portkey gateway integration
 * - Centralized AI service
 * - ScrapeGPT services (chat and assistant)
 * - Factory methods
 */

require('dotenv').config();
const { centralizedAI } = require('./services/ai/centralizedAIService');
const { getChatGPTResponse } = require('./services/scrapeGPT/request');
const { getAssistantResponse } = require('./services/scrapeGPT/assistant');
const { completionFactory } = require('./services/scrapeGPT/factory');

function printTestHeader(title) {
    console.log('\n' + '='.repeat(60));
    console.log(`🧪 ${title}`);
    console.log('='.repeat(60));
}

function printTestResult(testName, success, details = '') {
    const icon = success ? '✅' : '❌';
    console.log(`${icon} ${testName}: ${success ? 'PASSED' : 'FAILED'}`);
    if (details) {
        console.log(`   ${details}`);
    }
}

async function testEnvironmentConfiguration() {
    printTestHeader('Environment Configuration Test');
    
    const requiredVars = [
        'OPENAI_API_KEY',
        'AZURE_OPENAI_ENDPOINT',
        'AZURE_OPENAI_DEPLOYMENT',
        'AZURE_OPENAI_API_VERSION',
        'OPENAI_MODEL_ID'
    ];
    
    const optionalVars = [
        'PORTKEY_BASE_URL',
        'PORTKEY_API_KEY',
        'LANGSMITH_API_KEY'
    ];
    
    console.log('Required Environment Variables:');
    let allRequired = true;
    for (const varName of requiredVars) {
        const value = process.env[varName];
        const hasValue = !!value;
        allRequired = allRequired && hasValue;
        console.log(`  ${hasValue ? '✅' : '❌'} ${varName}: ${hasValue ? value.substring(0, 20) + '...' : 'NOT SET'}`);
    }
    
    console.log('\nOptional Environment Variables:');
    for (const varName of optionalVars) {
        const value = process.env[varName];
        const hasValue = !!value;
        console.log(`  ${hasValue ? '✅' : '⚠️ '} ${varName}: ${hasValue ? value.substring(0, 20) + '...' : 'NOT SET'}`);
    }
    
    return allRequired;
}

async function testCentralizedAIService() {
    printTestHeader('Centralized AI Service Test');
    
    try {
        // Test health status
        console.log('Getting health status...');
        const healthStatus = centralizedAI.getHealthStatus();
        console.log('Health Status:', JSON.stringify(healthStatus, null, 2));
        
        // Test chat completion
        const messages = [
            { role: 'system', content: 'You are a helpful assistant.' },
            { role: 'user', content: 'Say "Centralized AI Service test successful!" and nothing else.' }
        ];
        
        const result = await centralizedAI.createChatCompletion(messages, {
            temperature: 0,
            max_tokens: 50,
        }, 'azure', true);
        
        printTestResult('Chat Completion', !!result.message, `Response: "${result.message}"`);
        printTestResult('Provider Routing', result.provider === 'portkey', `Used: ${result.provider}`);
        printTestResult('Response Time', result.duration < 10000, `Duration: ${result.duration}ms`);
        
        return true;
    } catch (error) {
        printTestResult('Centralized AI Service', false, error.message);
        return false;
    }
}

async function testScrapeGPTServices() {
    printTestHeader('ScrapeGPT Services Test');
    
    let allPassed = true;
    
    try {
        // Test getChatGPTResponse
        console.log('Testing getChatGPTResponse...');
        const chatResult = await getChatGPTResponse(
            'You are a helpful assistant.',
            'Say "ScrapeGPT chat test successful!" and nothing else.',
            true
        );
        
        const chatPassed = !!chatResult.message;
        printTestResult('getChatGPTResponse', chatPassed, `Response: "${chatResult.message}"`);
        allPassed = allPassed && chatPassed;
        
    } catch (error) {
        printTestResult('getChatGPTResponse', false, error.message);
        allPassed = false;
    }
    
    // Note: Assistant test requires a valid assistant ID, so we'll skip it for now
    console.log('⚠️  Assistant test skipped (requires valid assistant ID)');
    
    return allPassed;
}

async function testFactoryMethods() {
    printTestHeader('Factory Methods Test');
    
    let allPassed = true;
    
    try {
        // Test match_text completion
        console.log('Testing completionFactory with match_text...');
        const factoryResult = await completionFactory('match_text', {
            textContent: 'This is a test company called Acme Corp that sells widgets and gadgets.',
            businessKeywords: ['Acme Corp', 'widgets'],
            url: 'https://example.com'
        });
        
        const factoryPassed = !!factoryResult.message;
        printTestResult('Factory match_text', factoryPassed, `Response: "${factoryResult.message?.substring(0, 100)}..."`);
        allPassed = allPassed && factoryPassed;
        
    } catch (error) {
        printTestResult('Factory Methods', false, error.message);
        allPassed = false;
    }
    
    return allPassed;
}

async function testFallbackBehavior() {
    printTestHeader('Fallback Behavior Test');
    
    try {
        // Test with Portkey disabled
        console.log('Testing fallback to direct Azure OpenAI...');
        const messages = [
            { role: 'system', content: 'You are a helpful assistant.' },
            { role: 'user', content: 'Say "Fallback test successful!" and nothing else.' }
        ];
        
        const fallbackResult = await centralizedAI.createChatCompletion(messages, {
            temperature: 0,
            max_tokens: 50,
        }, 'azure', false);
        
        const fallbackPassed = !!fallbackResult.message && fallbackResult.provider.includes('direct');
        printTestResult('Fallback Behavior', fallbackPassed, `Provider: ${fallbackResult.provider}`);
        
        return fallbackPassed;
    } catch (error) {
        printTestResult('Fallback Behavior', false, error.message);
        return false;
    }
}

async function runPerformanceTest() {
    printTestHeader('Performance Test');
    
    try {
        const messages = [
            { role: 'system', content: 'You are a helpful assistant.' },
            { role: 'user', content: 'Say "Performance test!" and nothing else.' }
        ];
        
        const iterations = 3;
        const times = [];
        
        console.log(`Running ${iterations} iterations...`);
        
        for (let i = 0; i < iterations; i++) {
            const startTime = Date.now();
            await centralizedAI.createChatCompletion(messages, {
                temperature: 0,
                max_tokens: 20,
            }, 'azure', true);
            const duration = Date.now() - startTime;
            times.push(duration);
            console.log(`  Iteration ${i + 1}: ${duration}ms`);
        }
        
        const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
        const maxTime = Math.max(...times);
        const minTime = Math.min(...times);
        
        console.log(`\nPerformance Results:`);
        console.log(`  Average: ${avgTime.toFixed(2)}ms`);
        console.log(`  Min: ${minTime}ms`);
        console.log(`  Max: ${maxTime}ms`);
        
        const performancePassed = avgTime < 5000; // Less than 5 seconds average
        printTestResult('Performance', performancePassed, `Average response time: ${avgTime.toFixed(2)}ms`);
        
        return performancePassed;
    } catch (error) {
        printTestResult('Performance Test', false, error.message);
        return false;
    }
}

async function main() {
    console.log('🚀 Starting Comprehensive AI Integration Tests\n');
    
    const results = {
        environment: await testEnvironmentConfiguration(),
        centralizedAI: await testCentralizedAIService(),
        scrapeGPT: await testScrapeGPTServices(),
        factory: await testFactoryMethods(),
        fallback: await testFallbackBehavior(),
        performance: await runPerformanceTest(),
    };
    
    // Final summary
    printTestHeader('Test Summary');
    
    const totalTests = Object.keys(results).length;
    const passedTests = Object.values(results).filter(Boolean).length;
    const failedTests = totalTests - passedTests;
    
    console.log(`📊 Test Results: ${passedTests}/${totalTests} passed`);
    console.log(`✅ Passed: ${passedTests}`);
    console.log(`❌ Failed: ${failedTests}`);
    
    for (const [testName, passed] of Object.entries(results)) {
        printTestResult(testName.charAt(0).toUpperCase() + testName.slice(1), passed);
    }
    
    if (passedTests === totalTests) {
        console.log('\n🎉 All tests passed! Your AI integration is working perfectly.');
    } else {
        console.log('\n⚠️  Some tests failed. Please check the configuration and try again.');
    }
    
    process.exit(failedTests > 0 ? 1 : 0);
}

if (require.main === module) {
    main().catch(error => {
        console.error('❌ Test suite failed:', error);
        process.exit(1);
    });
}

module.exports = {
    testEnvironmentConfiguration,
    testCentralizedAIService,
    testScrapeGPTServices,
    testFactoryMethods,
    testFallbackBehavior,
    runPerformanceTest,
};
