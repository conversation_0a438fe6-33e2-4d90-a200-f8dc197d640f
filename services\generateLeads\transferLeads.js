const prisma = require("../../database/prisma/getPrismaClient");

async function organizeLeads() {
  try {
    // Step 1: Move completed leads to job 1
    const completedResult = await prisma.lead.updateMany({
      where: {
        status: "completed",
      },
      data: {
        jobId: 1,
      },
    });

    // Step 2: Move SRP failed leads to job 2
    const srpFailedResult = await prisma.lead.updateMany({
      where: {
        status: "srp_failed",
      },
      data: {
        jobId: 2,
      },
    });

    // Step 3: Move all remaining leads to job 3
    const remainingResult = await prisma.lead.updateMany({
      where: {
        status: {
          notIn: ["completed", "srp_failed"],
        },
      },
      data: {
        jobId: 3,
      },
    });

    // Log results
    console.log("Transfer Results:");
    console.log(`Moved ${completedResult.count} completed leads to job ID 1`);
    console.log(`Moved ${srpFailedResult.count} SRP failed leads to job ID 2`);
    console.log(`Moved ${remainingResult.count} remaining leads to job ID 3`);
  } catch (error) {
    console.error("Error organizing leads:", error);
  } finally {
    await prisma.$disconnect();
  }
}

organizeLeads();
