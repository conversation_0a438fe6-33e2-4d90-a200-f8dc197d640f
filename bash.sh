#!/bin/bash

# Directory containing the backup files
BACKUP_DIR="/Users/<USER>/personal/SellerBot/backups"

# Data containing the time and filenames
DATA=$(cat <<EOF
Oct-10 08:06 7701661407a44658d724b8dcebd3e109
Oct-10 08:02 a2bb15c8910a4f1dcdd2616499dd06c0
Oct-10 06:51 899f40bb34865444f45d912ac6337eb6
Oct-10 06:49 9c22dd6c50845130fe42bfa2a0f41963
Oct-10 06:48 3dd4909c71f4e99754f68299d139a4ea
Oct-10 06:47 5f2ff49b338c101e4d0ba83262202a47
Oct-10 06:42 d136bf7c343932e6b7a3319eed355348
Oct-9 17:59 89a6c01f79c1d5a1de6be37f3cee5ffa
Oct-9 17:55 b5ad8de1eb83c304248b2447e3181c5d
Oct-9 17:53 1f536043ca01b246644cd4b5cec754a9
Oct-9 17:52 75bad0fe1e82856f957e9c069c19222b
Oct-9 17:47 2f45aeaad72d82216ce4a145373df96a
Oct-9 17:45 69de56c5fc61f548756185398c329e0b
Oct-9 13:06 faf8e8c150b5fd2fe9ede0edc1a0dff2
Oct-9 09:11 55c0fe8e88762ca5a1ab61ee8527c132
Oct-9 09:08 3b38dbdf1c8a747b6fc1ab6bf4595c82
Oct-9 07:44 d7207fe0569d68e29c0815d54de3fb8e
Oct-9 07:43 c7db9452042e554ae94f4bb8d3314bbf
Oct-9 07:41 2dae3d47f8d5db303e3e0d0e04ab717d
Oct-9 07:40 80b9e702e90296e47e5a9cdf38731b60
Oct-9 07:39 b1cfe71983cfab00abb856eabbc04405
Oct-9 07:11 f3e0b7b095b82ed9d6b85e736b42d3be
Oct-9 07:07 044c603e49a79236c94566f171d3cd06
Oct-9 06:51 7c2bb3383723e55730601c54804e8122
Oct-9 06:47 ecfc195f836387436b0bfdb7797a1e72
Oct-9 06:45 8081bc04b1adb713c214ef70e39c3345
Oct-9 06:43 ce9f88206101f75b55b22e5bbade1411
Oct-9 06:41 5aad7a3ddbff0375a44bfc726b2a4760
Oct-9 06:38 e88c7769867ed02b8978d20d258ae8ff
Oct-9 06:34 8c2c7df2549d793a70fd4681cac22cea
Oct-9 06:29 84430d4ec1f2ba0b6a96d4f36018792d
Oct-9 06:21 159da75e40526225378020254466acbb
Oct-9 06:20 f36da721b653e9340366d7a81b0ae2fe
Oct-9 06:17 7bf7dd1cad041f92b7b8e3288ca0d74e
Oct-9 06:11 e0a7608a77cab62ff4285d9b95a7d9e6
Oct-9 06:11 ef1c651874c4b5e014f878bedcbe05f6
Oct-9 06:05 b9aef126b1df697d5e4ff039087bdf22
Oct-6 17:01 1bec090031831a9aab6df7b60c10bb83
Oct-6 16:44 2d8253d061cc164506c4c32ec661ac66
Oct-6 12:42 d0c7401568f7c958fcbbec5648849aee
Oct-6 11:58 7105dd37c6e90c4aabb0a5b106ef07cc
Oct-6 11:39 00051a09409d101e300ffe01ea7f499e
Oct-6 10:07 fb6b1a3fc86b3fa9bbc5a7f7e5c1bf8d
Oct-6 10:06 6e47d646313770e14e182c0fd08fd310
Oct-6 09:57 5d9c4b6f12bd130b213e3abf14b671ec
Oct-6 08:29 8ce5109cff1be5afedd50c60f3f9ef43
Oct-6 08:23 b10afafcc424b38e2912dc8e50074feb
Oct-6 08:18 04dc35165b43cabcaf23dfd097186672
Oct-6 08:16 ed47f5c34a2f724897a83dc29b1fa6ca
Oct-5 19:15 f43bb842128f7aded2757e820ae7a3f4
Oct-5 19:13 9c09daad25b69c6e4c53576a782b8961
Oct-5 19:10 d8b23dda17750c4e7e28e230e42b35af
Oct-5 17:59 d5507afa6e6856241fb50a0506c6c7d7
Oct-5 17:56 656de4b92ed525ce37c69434c014943a
Oct-5 17:53 759a8a68032881ffce77a4169d4ded11
Oct-5 16:30 f59884ca557b73f1c320a89a2182056c
Oct-5 16:22 7a9682cbaec101c857bf64ac0dfa7566
Oct-5 13:11 b4127947bf0881f4d020ea1bd9a0fab8
Oct-5 12:11 7e214336f3bf799f821a73689c1bb443
Oct-5 12:04 bddba86b81e3b401131f3eefc4d720b6
Oct-5 09:53 6007ab21afaeac616a4f87419eceb643
Oct-4 16:20 a8a01deb90e15a3a3da5657264bade92
Oct-4 15:56 677230ce7a813ff91913e10da8c8f668
Oct-4 13:00 ca10fad4679b0fd037b6191860d58cb9
Oct-4 12:52 50a8f305f154f62611a5edc8c4a0b573
Oct-4 12:44 3455490c1782f946463a790729d71d1c
Oct-4 12:22 554ebd774ca23bfce000e535e2b0dced
Oct-4 12:20 a27fefa38ba99e29aab44d0651af4eb1
Oct-4 07:08 b7c3e404ce52c365be7a744e7bc4b797
Oct-3 18:56 edfff26dde867cd8c8f9bc0f2accfd0e
Oct-3 18:47 6b10829023ac49d97970fbccb77e0658
Oct-3 18:33 30054ad2972422b3a583b10e2b48cc9b
Oct-3 18:24 0a977cc89c821d78b8c82618f27f2db5
Oct-3 18:18 3d9406ca33866d2f59e3f14aade0719c
Oct-3 18:12 fb1d2e8e1ff3e7b3a2527a20cec97946
Oct-3 18:03 ddadb57c276fd26ebf7ea3248fc45862
Oct-3 17:55 e361d489044f002027d62b65245ea272
Oct-3 17:27 10f39dc2b7f729c038b2bb6b3e369f4e
Oct-3 17:13 926f9f2a86a1eaea8a0b5f733d57a6ed
Oct-3 17:00 cd774e9969d77c94e7e00b5bcfbab930
Oct-3 16:51 c2ae0ddfd7be83b831be4394d43af6a3
Oct-3 16:37 1365faef9d3d5dc0adb275b0e0e1c42e
Oct-3 16:12 e6df9b3c7f7edaf5f9a3ae921b097ad1
Oct-3 15:53 8ee8597d1cd73827e9a5d275f3a6136f
EOF
)

# Iterate over each line in the data
while IFS= read -r line; do
  # Extract the time and filename
  date=$(echo "$line" | awk '{print $1}')
  time=$(echo "$line" | awk '{print $2}')
  filename=$(echo "$line" | awk '{print $3}')

  # Construct the new filename
  new_filename="${date}-${time}-${filename}"

  # Rename the file in the backup directory
  if [ -f "${BACKUP_DIR}/${filename}" ]; then
    mv "${BACKUP_DIR}/${filename}" "${BACKUP_DIR}/${new_filename}"
    echo "Renamed ${filename} to ${new_filename}"
  else
    echo "File ${filename} not found in ${BACKUP_DIR}"
  fi
done <<< "$DATA"