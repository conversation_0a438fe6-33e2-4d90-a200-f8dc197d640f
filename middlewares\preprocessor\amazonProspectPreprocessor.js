const BasePreprocessor = require("./basePreprocessor");
const { extractDomain } = require("../../utils/domainHelper");
const prisma = require("../../database/prisma/getPrismaClient");

/**
 * AmazonProspect-specific preprocessor
 */
class AmazonProspectPreprocessor extends BasePreprocessor {

  async after(data) {
    // Extract domain from website if domain is not provided
    if (!data.domain && data.website) {
      data.domain = extractDomain(data.website);
    }

    // Delete website if domain is provided
    if (data.website) {
      delete data.website;
    }

    // Normalize email status
    if (data.email_status) {
      data.email_status = data.email_status.toUpperCase();
    }
    
    // Move source to sources if needed
    if (data.source && (!data.sources || (Object.keys(data.sources) && Object.keys(data.sources).length === 0))) {
      data.sources = [data.source];
    }
    
    return data;
  }

}

module.exports = AmazonProspectPreprocessor; 