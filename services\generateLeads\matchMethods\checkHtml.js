const {
  removeSpecial<PERSON>haracters,
} = require("../../../utils/removeSpecialCharacters");
const fs = require("fs").promises;
const path = require("path");
const crypto = require("crypto");
const Bottleneck = require("bottleneck");
const { getOrFetchHtml } = require("../../../utils/fileCache");

const limiter = new Bottleneck({
  maxConcurrent: 30,
  minTime: 30,
});

/**
 * Checks HTML content for matching keywords and calculates a score
 * @param {Object} organicDatum - The data object containing URL information
 * @param {string} organicDatum.originalUrl - The URL to fetch and check HTML from
 * @param {string[]} keywordsToMatch - Array of keywords to search for in the HTML
 * @param {boolean} [removeSpaces=false] - Whether to remove spaces during normalization
 * @param {string} [normalizationMode="default"] - The mode for text normalization ('default' or 'lowered_spaced')
 * @returns {Promise<Object>} Result object containing matched keywords, score, and metadata
 * @returns {string[]} result.matchedKeywords - Array of keywords found in the HTML
 * @returns {number} result.finalScore - Calculated score based on matches
 * @returns {string} result.url - Original URL that was checked
 * @returns {string[]} result.keywordsToMatch - Original keywords that were searched
 * @returns {string} [result.error] - Error message if any occurred during processing
 */

async function checkHtmlRaw(
  organicDatum,
  keywordsToMatch,
  removeSpaces = false,
  normalizationMode = "default"
) {
  // console.log("   Checking score for HTML: ", keywordsToMatch);
  const url = organicDatum.originalUrl;
  try {
    let html = await getOrFetchHtml(url);

    const normalizedHtml =
      normalizationMode === "lowered_spaced"
        ? typeof html === "string"
          ? html.trim().toLowerCase()
          : ""
        : removeSpecialCharacters(html, removeSpaces);

    // Normalize keywords by removing extra spaces
    const normalizedKeywords = keywordsToMatch.map((keyword) =>
      normalizationMode === "lowered_spaced"
        ? typeof keyword === "string"
          ? keyword.trim().toLowerCase()
          : ""
        : removeSpecialCharacters(keyword, removeSpaces)
    );

    const matchedKeywords = [];

    // Check for normalized keywords in the normalized HTML
    for (const keyword of normalizedKeywords) {
      if (normalizedHtml.includes(keyword)) {
        matchedKeywords.push(keyword);
      }
    }

    if (matchedKeywords.length === 0) {
      return {
        matchedKeywords: [],
        finalScore: 0,
        url,
        keywordsToMatch,
      };
    }

    return {
      matchedKeywords,
      finalScore: matchedKeywords.length * checkHtml.config.score,
      url,
      keywordsToMatch,
    };
  } catch (error) {
    console.error(error);
    return {
      matchedKeywords: [],
      finalScore: 0,
      url,
      keywordsToMatch,
      error: error.message,
    };
  }
}

const checkHtml = limiter.wrap(checkHtmlRaw);

async function main() {
  const url = "https://www.countrymanufacturing.com";

  if (!url) {
    console.log("Please provide a URL as an argument");
    process.exit(1);
  }

  const result = await checkHtml({ originalUrl: url }, ["8003351880"]);

  console.log("HTML Check Results:", result);
}

if (require.main === module) {
  main();
}

checkHtml.config = {
  score: 2,
  key: "checkHtml",
};

module.exports = { checkHtml };
