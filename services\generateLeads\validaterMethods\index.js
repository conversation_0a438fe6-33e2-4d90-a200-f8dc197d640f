const validateHtmlText = require("./validateHtmlText");
const csv = require("csv-parser");
const createCsvWriter = require("csv-writer").createObjectCsvWriter;
const fs = require("fs");

async function validateLeads(threshold = "5") {
  const verifiedLeads = [];
  const results = [];

  // Read CSV using Promise
  await new Promise((resolve, reject) => {
    fs.createReadStream("leads.csv")
      .pipe(csv())
      .on("data", (row) => {
        results.push(row);
      })
      .on("error", (error) => {
        console.error("Error reading CSV:", error);
        reject(error);
      })
      .on("end", () => {
        resolve(results);
      });
  });

  // Setup CSV writer for verified leads
  const csvWriter = createCsvWriter({
    path: "leads_verified.csv",
    header: [
      { id: "Keywords", title: "Keywords" },
      { id: "Confidence", title: "Confidence" },
      { id: "Confident_Url", title: "Confident Url" },
      { id: "Seller_Url", title: "Seller Url" },
      { id: "Individual", title: "Individual" },
      { id: "Is_Verified", title: "Is Verified" },
      { id: "content", title: "Content" },
    ],
  });

  const totalLeads = results.length;
  let count = 0;

  // Process each lead
  for (const lead of results) {
    try {
      count++;
      console.log(`Processing ${lead.Keywords} (${count}/${totalLeads})`);
      if (
        !lead ||
        lead["Confident Url"] === "" ||
        !lead.Keywords ||
        lead.Confidence < threshold
      ) {
        lead.Is_Verified = "less than threshold";
        continue;
      }
      console.log({ lead });
      const validationResult = await validateHtmlText(
        lead["Confident Url"],
        lead.Keywords.split("|"),
      );
      lead.Is_Verified = validationResult.message;
      lead.content = validationResult.textContent;
      verifiedLeads.push(lead);
    } catch (error) {
      console.error(`Error processing ${lead.Keywords}:`, error);
    }
  }

  await csvWriter.writeRecords(results);
  return verifiedLeads;
}

function exampleUsage() {
  validateLeads()
    .then((verifiedLeads) => {
      console.log("Verified leads:", verifiedLeads);
    })
    .catch((error) => {
      console.error("Error:", error);
    });
}

exampleUsage();

module.exports = {
  validateLeads,
};
