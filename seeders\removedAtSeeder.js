const { PrismaClient } = require("@prisma/client");
const fs = require("fs");
const path = require("path");
const prisma = new PrismaClient();

// Function to parse CSV and convert date format
function parseCSV(csvContent) {
  const lines = csvContent.trim().split('\n');
  const headers = lines[0].split(',');
  const data = [];

  for (let i = 1; i < lines.length; i++) {
    const values = lines[i].split(',');
    const row = {};

    headers.forEach((header, index) => {
      row[header.trim()] = values[index] ? values[index].trim() : '';
    });

    if (row.reviewID && row.removedAt) {
      data.push(row);
    }
  }

  return data;
}

// Function to convert DD/MM/YYYY to ISO date string
function convertToISODate(dateString) {
  if (!dateString || dateString === '') return null;
  console.log(dateString);
  const [day, month, year] = dateString.split('/');
  if (day && month && year) {
    return new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`).toISOString();
  }
  return null;
}

async function run() {
  try {
    // Read the CSV file
    const csvPath = path.join(__dirname, '../sample/removedAtDates/Removed review date - 5 July.csv');
    const csvContent = fs.readFileSync(csvPath, 'utf8');

    // Parse CSV data
    const csvData = parseCSV(csvContent);
    console.log(`📄 Read ${csvData.length} records from CSV`);

    // Create a map of reviewID to removedAt date
    const reviewDateMap = new Map();
    csvData.forEach(row => {
      const isoDate = convertToISODate(row.removedAt);
      if (isoDate) {
        reviewDateMap.set(row.reviewID, isoDate);
      }
    });

    console.log(`🗺️  Mapped ${reviewDateMap.size} valid reviewID-date pairs`);

    // Get all review IDs from the map
    const reviewIds = Array.from(reviewDateMap.keys());

    // Find existing reviews in database
    const existingReviews = await prisma.review.findMany({
      where: {
        reviewId: {
          in: reviewIds
        }
      },
      select: { reviewId: true }
    });

    console.log(`🔍 Found ${existingReviews.length} existing reviews in database`);

    // Update reviews in batches
    const batchSize = 100;
    let updatedCount = 0;

    for (let i = 0; i < existingReviews.length; i += batchSize) {
      const batch = existingReviews.slice(i, i + batchSize);

      await Promise.all(
        batch.map((review) => {
          const removedAtDate = reviewDateMap.get(review.reviewId);
          return prisma.review.update({
            where: { reviewId: review.reviewId },
            data: { removedAt: removedAtDate }
          });
        })
      );

      updatedCount += batch.length;
      console.log(`✅ Processed ${updatedCount} / ${existingReviews.length} reviews`);
    }

    console.log(`🎉 Successfully updated ${updatedCount} reviews with removedAt dates`);

    // Log reviews that were in CSV but not found in database
    const foundReviewIds = existingReviews.map(r => r.reviewId);
    const notFoundReviews = reviewIds.filter(id => !foundReviewIds.includes(id));

    if (notFoundReviews.length > 0) {
      console.log(`⚠️  ${notFoundReviews.length} review IDs from CSV were not found in database:`);
      notFoundReviews.slice(0, 10).forEach(id => console.log(`   - ${id}`));
      if (notFoundReviews.length > 10) {
        console.log(`   ... and ${notFoundReviews.length - 10} more`);
      }
    }

  } catch (error) {
    console.error("❌ ERROR:", error);
  } finally {
    await prisma.$disconnect();
  }
}

run();
