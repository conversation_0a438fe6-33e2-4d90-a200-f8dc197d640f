const Joi = require("joi");
const prisma = require("../database/prisma/getPrismaClient");
const { findParentGroup } = require("../utils/enhancedSellerGroupUnionFind");

const lookupSources = [
  // **********************
  // *** Website Search ***
  // **********************

  // Apollo Phase 1
  { source: "WEBSITE_SEARCH_APOLLO_P1" },
  { source: "WEBSITE_SEARCH_APOLLO_P1:success" },
  { source: "WEBSITE_SEARCH_APOLLO_P1:fail" },

  // SERP Phase 4
  { source: "WEBSITE_SEARCH_FULL_SERP_P4" },
  { source: "WEBSITE_SEARCH_FULL_SERP_P4:success" },
  { source: "WEBSITE_SEARCH_FULL_SERP_P4:fail" },

  // SERP Phase 5
  { source: "WEBSITE_SEARCH_FULL_SERP_P5" },
  { source: "WEBSITE_SEARCH_FULL_SERP_P5:success" },
  { source: "WEBSITE_SEARCH_FULL_SERP_P5:fail" },

  // Storeleads
  { source: "STORELEADS_P1:success" },
  { source: "STORELEADS_P1:fail" },

  // *************************
  // **** Prospect Search ****
  // *************************
  { source: "PROSPECT_SEARCH_APOLLO_STEP1_P1" },
  { source: "PROSPECT_SEARCH_APOLLO_STEP2_P1" },
  { source: "PROSPECT_SEARCH_APOLLO_STEP3_P1" },
  { source: "PROSPECT_SEARCH_ANYMAIL_FINDER_P1" },

  // *************************
  // **** eMail Harvesting ***
  // *************************
  { source: "PROSPECT_SEARCH_AMZ_SF_EMAIL_HARVEST_P1" },
  { source: "PROSPECT_SEARCH_DOMAIN_EMAIL_HARVEST_P1" },

  // ****************************
  // **** eMail Verification ****
  // ****************************
  // P1
  { source: "EMAIL_VERIFY_BULK_CHECKER_STEP1_P1" },
  { source: "EMAIL_VERIFY_BULK_CHECKER_STEP1_P1:success" },
  { source: "EMAIL_VERIFY_BULK_CHECKER_STEP1_P1:fail" },
  { source: "EMAIL_VERIFY_BULK_CHECKER_STEP1_P1:inconclusive" },
  // P2
  { source: "EMAIL_VERIFY_BULK_CHECKER_STEP2_P1" },
  { source: "EMAIL_VERIFY_BULK_CHECKER_STEP2_P1:success" },
  { source: "EMAIL_VERIFY_BULK_CHECKER_STEP2_P1:fail" },
  { source: "EMAIL_VERIFY_BULK_CHECKER_STEP2_P1:inconclusive" },
];

// Define the schema for the company data validation
const companySchema = Joi.object({
  name: Joi.string().allow("").default(""),
  amazon_seller_id: Joi.string().required(),
  primary_category_id: Joi.string().allow("").default(""),
  primary_category: Joi.string().allow("").default(""),
  primary_sub_category: Joi.string().allow("").default(""),
  estimate_sales: Joi.string().allow("").default(""),
  derived_estimate_sales: Joi.number().allow("").default(0),
  percent_fba: Joi.string().allow("").default(""),
  number_winning_brands: Joi.string().allow("").default(""),
  number_asins: Joi.string().allow("").default(""),
  number_top_asins: Joi.string().allow("").default(""),
  state: Joi.string().allow("").default(""),
  country: Joi.string().allow("").default(""),
  business_name: Joi.string().allow("").default(""),
  number_brands_1000: Joi.string().allow("").default(""),
  mom_growth: Joi.string().allow("").default(""),
  mom_growth_count: Joi.string().allow("").default(""),
  started_selling_date: Joi.date().allow("").default(""),
  smartscout_country: Joi.string().allow("").default(""),
  website: Joi.string().allow("").default(""),
  domain: Joi.string().allow("").default(""),
  website_status: Joi.string()
    .valid(
      // updated set of statuses supported
      "Final Correct",
      "Final Correct - UNVERIFIED",
      "Maybe",
      "Processing",
      "Failed",
      // empty stuff
      ""
    )
    .allow("")
    .default(""),
  find_website_batch: Joi.string().allow("").default(""),
  lookup_source: Joi.string()
    .valid(...lookupSources.map((item) => item.source))
    .allow("")
    .default(""),
  employee_count: Joi.string().allow("").default(""),
  company_linkedin: Joi.string().allow("").default(""),
  company_twitter: Joi.string().allow("").default(""),
  company_fb: Joi.string().allow("").default(""),
  company_location: Joi.string().allow("").default(""),
  company_address: Joi.string().allow("").default(""),
  company_pincode: Joi.string().allow("").default(""),
  company_id: Joi.number().optional(),
  errors: Joi.any().optional(),
});

// Define the schema for the Amazon Seller data validation
const amazonSellerSchema = Joi.object({
  name: Joi.allow(null),
  amazon_seller_id: Joi.required(),
  marketplace: Joi.required(),
  primary_category: Joi.allow(null),
  primary_sub_category: Joi.allow(null),
  estimate_sales: Joi.allow(null),
  avg_price: Joi.allow(null),
  percent_fba: Joi.allow(null),
  number_reviews_lifetime: Joi.allow(null),
  number_reviews_30days: Joi.allow(null),
  number_winning_brands: Joi.allow(null),
  number_asins: Joi.allow(null),
  number_top_asins: Joi.allow(null),
  street: Joi.allow(null),
  city: Joi.allow(null),
  adr_state: Joi.allow(null),
  adr_country: Joi.allow(null),
  adr_zip_code: Joi.allow(null),
  business_name: Joi.allow(null),
  number_brands_1000: Joi.allow(null),
  mom_growth: Joi.allow(null),
  mom_growth_count: Joi.allow(null),
  is_suspended: Joi.allow(null),
  last_suspended_date: Joi.allow(null),
  started_selling_date: Joi.allow(null),
  website: Joi.allow(null),
  domain: Joi.allow(null),
  website_status: Joi.valid(
    // updated set of statuses supported
    "Final Correct",
    "Final Correct - UNVERIFIED",
    "Maybe",
    "Processing",
    "Failed",
    // empty stuff
    ""
  ).allow(null),
  lookup_source: Joi.valid(...lookupSources.map((item) => item.source)).allow(
    null
  ),
  lookup_sources: Joi.object().default({}),
  seller_url: Joi.allow(null),
  seller_id: Joi.number().optional(),
  seller_group_id: Joi.number().optional(),
  errors: Joi.any().optional(),
});

// Define the schema for the prospect data validation
const prospectSchema = Joi.object({
  amazon_seller_id: Joi.string().allow("").default(""),
  person_name: Joi.string().allow("").default(""),
  website: Joi.string().allow("").default(""),
  domain: Joi.string().allow("").default(""),
  person_linkedin: Joi.string().allow("").default(""),
  phone: Joi.string().allow("").default(""),
  email: Joi.string().allow("").default(""),
  job_title: Joi.string().allow("").default(""),
  contact_location: Joi.string().allow("").default(""),
  apollo_company_name: Joi.string().allow("").default(""),
  apollo_website: Joi.string().allow("").default(""),
  smartscout_country: Joi.string().allow("").default(""),
  source: Joi.string()
    .valid(...lookupSources.map((item) => item.source))
    .allow("")
    .default(""),
  email_status: Joi.string()
    .valid(
      "UNAVAILABLE",
      "VERIFIED",
      "UNVERIFIED",
      "FAILED",
      "CATCHALL",
      "GREYLISTING",
      "INCONCLUSIVE",
      "SL_ERROR",
    )
    .allow("")
    .default(""),
  company_id: Joi.number().optional(),
  errors: Joi.any().optional(),
  address: Joi.string().allow("").default(""),
  pincode: Joi.string().allow("").default(""),
});

// Define the schema for the client-prospect match data validation
const clientProspectMatchSchema = Joi.object({
  person_linkedin: Joi.string().allow("").default(""),
  phone: Joi.string().allow("").default(""),
  email: Joi.string().allow("").default(""),
  client: Joi.string()
    .valid(
      "Accrue",
      "MrPrime",
      "AMZ Ads",
      "Amazing Marketing Co",
      "BrandBuddy",
      "Riverguide",
      "Sellerplex"
    )
    .required(),
  jeff_output_status: Joi.string()
    .valid(
      "success-without-revenue",
      "success-with-revenue",
      "unsuccessful",
      "To Be Updated",
      "processing"
    )
    .required(),
  amazon_seller_id: Joi.string().allow("").default(""),
  date_reached_out: Joi.date().default(new Date()),
  prospect_id: Joi.number().required(),
  errors: Joi.any().optional(),
});

// Define the schema for the AmazonProspect data validation
const amazonProspectSchema = Joi.object({
  // Note: amazon_seller_id, marketplace, and domain are not part of AmazonProspect model
  // They are used for lookup purposes only and should be removed before database insertion
  amazon_seller_id: Joi.string().allow("").default(""),
  marketplace: Joi.string().allow("").default(""),
  domain: Joi.string().allow("").default(""),
  person_name: Joi.string().allow("").default(""),
  person_linkedin: Joi.string().allow("").default(""),
  email: Joi.string().allow("").default(""),
  job_title: Joi.string().allow("").default(""),
  source: Joi.string()
    .valid(...lookupSources.map((item) => item.source))
    .allow("")
    .default(""),
  sources: Joi.array().items(Joi.string()).default([]),
  email_status: Joi.string()
    .valid(
      "UNAVAILABLE",
      "VERIFIED",
      "UNVERIFIED",
      "FAILED",
      "CATCHALL",
      "GREYLISTING",
      "INCONCLUSIVE"
    )
    .allow("")
    .default(""),
  seller_id: Joi.number().optional(),
  errors: Joi.any().optional(),
});

// Function to validate company data
async function validateCompany(data, isValid, dict) {
  try {
    if (!data.amazon_seller_id) {
      isValid[0] = false;
      data.errors = data.errors
        ? [...data.errors, "Amazon Seller ID is required"]
        : ["Amazon Seller ID is required"];
      return;
    }
    // Check if website is present but website status is empty
    if (data.website && data.website_status === "") {
      isValid[0] = false;
      data.errors = data.errors
        ? [...data.errors, "Website Status is required when website is present"]
        : ["Website Status is required when website is present"];
    }
    // Add Amazon Seller mess up
    // Check if the domain is present and website status is correct and amazon seller id is matching
    if (data.domain && data.website_status === "Final Correct") {
      // Creating th Key for the domain and smartscout_country
      const key = `${data.domain}`;
      console.log("Validating data for key:-", key);
      // Check if the domain is already associated with another Amazon Seller ID
      // Else we will let the data pass and create a new row / Update the row in the database based on the seller Id and Country
      if (dict[`Stored:-${key}`]) {
        // Check if the stored Amazon Seller ID is different from the current Amazon Seller ID
        if (dict[`Stored:-${key}`] !== data.amazon_seller_id) {
          isValid[0] = false;
          data.errors = data.errors
            ? [
                ...data.errors,
                `Correct Domain:- ${
                  data.domain
                } is already associated with Amazon Seller ID:- ${
                  dict[`Stored:-${key}`]
                }`,
              ]
            : [
                `Correct Domain:- ${
                  data.domain
                } is already associated with Amazon Seller ID:- ${
                  dict[`Stored:-${key}`]
                }`,
              ];
        } else {
          // If the Amazon Seller ID is same, we will update the company_id in the data and update the rest of data
          data.company_id = dict[`Company:-${key}`];
        }
      }
      if (dict[`${key}`] && dict[`${key}`] !== data.amazon_seller_id) {
        isValid[0] = false;
        data.errors = data.errors
          ? [
              ...data.errors,
              `Correct Domain:- ${
                data.domain
              } is associated with Amazon Seller ID:- ${
                dict[`${key}`]
              } in the same file`,
            ]
          : [
              `Correct Domain:- ${
                data.domain
              } is associated with Amazon Seller ID:- ${
                dict[`${key}`]
              } in the same file`,
            ];
      } else {
        dict[`${key}`] = data.amazon_seller_id;
      }
    }
  } catch (err) {
    console.log(err);
  }
}

// Function to validate Amazon Seller data
async function validateAmazonSeller(data, isValid, dict) {
  try {
    // Check Basic Required Fields
    if (!data.amazon_seller_id) {
      isValid[0] = false;
      data.errors = data.errors
        ? [...data.errors, "Amazon Seller ID is required"]
        : ["Amazon Seller ID is required"];
      return;
    }

    if (!data.marketplace) {
      isValid[0] = false;
      data.errors = data.errors
        ? [...data.errors, "Marketplace is required"]
        : ["Marketplace is required"];
      return;
    }
    // Check Website Status and Domain
    if (
      data.domain &&
      (data.website_status === "" || data.website_status === null)
    ) {
      isValid[0] = false;
      data.errors = data.errors
        ? [...data.errors, "Website Status is required when domain is present"]
        : ["Website Status is required when domain is present"];
    }

    if (data.website_status === "Final Correct" && !data.domain) {
      isValid[0] = false;
      data.errors = data.errors
        ? [
            ...data.errors,
            "Domain is required when website status is Final Correct",
          ]
        : ["Domain is required when website status is Final Correct"];
    }
    // Check Seller Group ID
    // If seller group id is not present, we will try to find the seller_id from the dict
    if (!data.seller_group_id) {
      const key = `${data.amazon_seller_id}-${data.marketplace}`;
      if (dict[`SellerGroup:-${key}`]) {
        // You have the seller group id from the amazon_seller_id and marketplace 
        //[ Meaning You have the seller Present in the database , So Assign the exsisting seller group id]
        data.seller_group_id = dict[`SellerGroup:-${key}`];
      }
    }else{
      // If the seller group id is present, then check if we have conflicting seller group 
      if(dict[`SellerGroup:-${data.amazon_seller_id}`]){
        const previousSellerGroup = dict[`SellerGroup:-${data.amazon_seller_id}`]
        if(previousSellerGroup !== data.seller_group_id){
          isValid[0] = false;
          data.errors = data.errors
            ? [...data.errors, `Mismatch in seller group id for ${data.amazon_seller_id} : Previous Seller Group ID ${previousSellerGroup} and New Seller Group ID ${data.seller_group_id}`]
            : [`Mismatch in seller group id for ${data.amazon_seller_id} : Previous Seller Group ID ${previousSellerGroup} and New Seller Group ID ${data.seller_group_id}`];
        }
      }
      if(dict[`SellerGroup:-${data.domain}`]){
        const previousSellerGroup = dict[`SellerGroup:-${data.domain}`]
        if(previousSellerGroup !== data.seller_group_id){
          isValid[0] = false;
          data.errors = data.errors
            ? [...data.errors, `Mismatch in seller group id for ${data.domain} : Previous Seller Group ID ${previousSellerGroup} and New Seller Group ID ${data.seller_group_id}`]
            : [`Mismatch in seller group id for ${data.domain} : Previous Seller Group ID ${previousSellerGroup} and New Seller Group ID ${data.seller_group_id}`];
        }
      }
    }
    // FAll Backs Violation Check...............................
    // We need to choose seller group id based on the amazon_seller_id or domain
    //  If both are present and not matching we will return an error
    if(dict[`SellerGroup:-${data.amazon_seller_id}`] && dict[`SellerGroup:-${data.domain}`]){
      if(dict[`SellerGroup:-${data.amazon_seller_id}`] !== dict[`SellerGroup:-${data.domain}`]){
        isValid[0] = false;
        data.errors = data.errors
          ? [...data.errors, `Multiple seller group id for valid ${data.amazon_seller_id} and ${data.domain}`]
          : [`Multiple seller group id for valid ${data.amazon_seller_id} and ${data.domain}`];
      }
    }
    // FAll Backs ...............................
    if(dict[`SellerGroup:-${data.amazon_seller_id}`]){
      // If seller group id is from amazon_seller_id, we will update the seller_group_id
      data.seller_group_id = dict[`SellerGroup:-${data.amazon_seller_id}`];
    }else if(dict[`SellerGroup:-${data.domain}`]){
      // If seller group id is from domain, we will update the seller_group_id
      data.seller_group_id = dict[`SellerGroup:-${data.domain}`];
    }

    // Check for conflicting parent groups between domain and seller_id
    if (data.amazon_seller_id && data.domain && data.website_status === "Final Correct") {
      try {
        const sellerParent = await findParentGroup('seller_id', data.amazon_seller_id);
        const domainParent = await findParentGroup('domain', data.domain.toLowerCase().trim());
        
        if (sellerParent && domainParent && sellerParent !== domainParent) {
          isValid[0] = false;
          data.errors = data.errors
            ? [
                ...data.errors,
                `Conflict detected: Seller ID ${data.amazon_seller_id} belongs to group ${sellerParent} but domain ${data.domain} belongs to group ${domainParent}. These entities cannot have different parent groups.`
              ]
            : [
                `Conflict detected: Seller ID ${data.amazon_seller_id} belongs to group ${sellerParent} but domain ${data.domain} belongs to group ${domainParent}. These entities cannot have different parent groups.`
              ];
        }
      } catch (parentCheckError) {
        console.warn(`Could not check parent groups for seller ${data.amazon_seller_id}:`, parentCheckError.message);
        // Don't fail validation for parent check errors, just log
      }
    }
  } catch (err) {
    console.log(err);
    isValid[0] = false;
    data.errors = data.errors ? [...data.errors, err.message] : [err.message];
  }
}

// Function to validate prospect data
async function validateProspect(data, isValid, dict) {
  try {
    if (!((data.amazon_seller_id && data.marketplace) || data.domain)) {
      isValid[0] = false;
      data.errors = data.errors
        ? [...data.errors, "Amazon Seller ID and Marketplace or Domain is required"]
        : ["Amazon Seller ID and Marketplace or Domain is required"];
      return;
    }

    if (data.email_status && data.email_status === "VERIFIED" && !data.email) {
      isValid[0] = false;
      data.errors = data.errors
        ? [...data.errors, "Email is required when Email Status is VERIFIED"]
        : ["Email is required when Email Status is VERIFIED"];
    }

    if (data.amazon_seller_id) {
      const company = await prisma.company.findFirst({
        where: {
          amazon_seller_id: data.amazon_seller_id,
        },
      });
      if (!company) {
        isValid[0] = false;
        data.errors = data.errors
          ? [
              ...data.errors,
              `Company with Amazon Seller ID:- ${data.amazon_seller_id} does not exist in the Company Data`,
            ]
          : [
              `Company with Amazon Seller ID:- ${data.amazon_seller_id} does not exist in the Company Data`,
            ];
      } else {
        data.company_id = company.id;
        data.amazon_seller_id = company.amazon_seller_id;
        return;
      }
    }
    if (data.domain) {
      const key = `${data.domain}`;
      if (dict[`Stored:-${key}`]) {
        data.amazon_seller_id = dict[`Stored:-${key}`];
        data.company_id = dict[`Company:-${key}`];
      } else {
        console.log("Domain not found in the company data");
      }
    }
  } catch (err) {
    console.log(err);
  }
}

// Function to validate client-prospect matching data
async function validateMatching(data, isValid, dict) {
  try {
    const conditions = [];
    if (!data.email) {
      isValid[0] = false;
      data.errors = data.errors
        ? [...data.errors, "Email is required"]
        : ["Email is required"];
      return;
    }
    if (data.person_linkedin && data.person_linkedin.includes("linkedin")) {
      conditions.push({ person_linkedin: data.person_linkedin });
    }
    if (data.phone) conditions.push({ phone: data.phone });
    if (data.email) conditions.push({ email: data.email });

    if (conditions.length === 0) {
      isValid[0] = false;
      data.errors = data.errors
        ? [
            ...data.errors,
            "At least one of LinkedIn, Phone, or Email is required",
          ]
        : ["At least one of LinkedIn, Phone, or Email is required"];
      return;
    }
    // Find prospects matching the person_linkedin, phone, or email
    const prospects = await prisma.prospect.findMany({
      where: {
        OR: conditions,
      },
    });

    // Check if multiple prospects are found
    if (prospects.length > 1) {
      isValid[0] = false;
      data.errors = data.errors
        ? [
            ...data.errors,
            `Multiple prospects found with LinkedIn:- ${data.person_linkedin}, Phone:- ${data.phone} and Email:- ${data.email}`,
          ]
        : [
            `Multiple prospects found with LinkedIn:- ${data.person_linkedin}, Phone:- ${data.phone} and Email:- ${data.email}`,
          ];
    }

    const prospect = prospects[0];

    // Check if prospect does not exist
    if (!prospect) {
      isValid[0] = false;
      data.errors = data.errors
        ? [...data.errors, "Prospect does not exist"]
        : ["Prospect does not exist"];
    } else {
      data.prospect_id = prospect.prospect_id;
      data.amazon_seller_id = prospect.amazon_seller_id;
    }
  } catch (err) {
    console.log(err);
  }
}

async function validate_update(data, isValid, type) {
  try {
    console.log("Validating update operation");
    switch (type) {
      case "company":
        const company = await prisma.company.findFirst({
          where: data.company_id
            ? { id: data.company_id }
            : { amazon_seller_id: data.amazon_seller_id },
        });

        if (!company) {
          isValid[0] = false;
          data.errors = data.errors
            ? [
                ...data.errors,
                "Cannot perform update - record would be inserted as new company",
              ]
            : [
                "Cannot perform update - record would be inserted as new company",
              ];
        }

        // Check if existing record has Final Correct status
        if (
          company &&
          company.website_status === "Final Correct" &&
          data.website
        ) {
          // const currentDomain = extractDomain(company.website);
          // const newDomain = extractDomain(data.website);
          if (
            (data.domain && data.domain.length < 3) ||
            // data.website !== company.website ||
            (data.domain && data.domain !== company.domain)
            // currentDomain !== newDomain
          ) {
            isValid[0] = false;
            data.errors = data.errors
              ? [
                  ...data.errors,
                  `Domain do not match with the existing record:- ${company.domain} Update Record:- ${data.domain}`,
                ]
              : [
                  `Domain do not match with the existing record:-  ${company.domain} Update Record:- ${data.domain}`,
                ];
          }
        }
        break;

      case "amazon_seller":
        // For AmazonSeller, we need both amazon_seller_id and marketplace to identify a record
        if (!data.marketplace) {
          isValid[0] = false;
          data.errors = data.errors
            ? [...data.errors, "Marketplace is required for update operation"]
            : ["Marketplace is required for update operation"];
          return;
        }
        if (!data.amazon_seller_id) {
          isValid[0] = false;
          data.errors = data.errors
            ? [
                ...data.errors,
                "Amazon Seller ID is required for update operation",
              ]
            : ["Amazon Seller ID is required for update operation"];
          return;
        }

        const seller = await prisma.amazonSeller.findFirst({
          where: {
            amazon_seller_id: data.amazon_seller_id,
            marketplace: data.marketplace,
          },
        });

        if (!seller) {
          isValid[0] = false;
          data.errors = data.errors
            ? [
                ...data.errors,
                "Cannot perform update - record would be inserted as new seller",
              ]
            : [
                "Cannot perform update - record would be inserted as new seller",
              ];
        } else {
          data.id = seller.id;
        }

        // Check if existing record has Final Correct status
        // Once a Final correct Domain Is set It can not be changed
        if (
          seller &&
          seller.website_status === "Final Correct" &&
          data.domain &&
          (data.domain.length < 3 || data.domain !== seller.domain)
        ) {
          isValid[0] = false;
          data.errors = data.errors
            ? [
                ...data.errors,
                `Domain does not match with the existing record:- ${seller.domain} Update Record:- ${data.domain}`,
              ]
            : [
                `Domain does not match with the existing record:-  ${seller.domain} Update Record:- ${data.domain}`,
              ];
        }
        break;
    }
  } catch (err) {
    console.error(err);
    isValid[0] = false;
    data.errors = data.errors ? [...data.errors, err.message] : [err.message];
  }
}

// Function to validate AmazonProspect data
async function validateAmazonProspect(data, isValid, dict) {
  try {
      // Require either email or LinkedIn for identification
      if (
        (!data.email || data.email.trim() === "") &&
        (!data.person_linkedin || !data.person_linkedin.includes("linkedin"))
      ) {
        isValid[0] = false;
        data.errors = data.errors
          ? [
              ...data.errors,
              "Either Email or LinkedIn URL is required for prospect identification",
            ]
          : [
              "Either Email or LinkedIn URL is required for prospect identification",
            ];
      }
  
      // Validate LinkedIn URL format if provided
      if (data.person_linkedin && data.person_linkedin.trim() !== "") {
        if (!data.person_linkedin.includes("linkedin.com/")) {
          isValid[0] = false;
          data.errors = data.errors
            ? [...data.errors, "Invalid LinkedIn URL format"]
            : ["Invalid LinkedIn URL format"];
        }
      }
  
      // Validate email format if provided
      if (data.email && data.email.trim() !== "") {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(data.email)) {
          isValid[0] = false;
          data.errors = data.errors
            ? [...data.errors, "Invalid email format"]
            : ["Invalid email format"];
        }
      }
  
      // Validate source if provided
      if (data.source && data.source.trim() !== "") {
        const validSources = lookupSources.map((item) => item.source);
        if (!validSources.includes(data.source)) {
          // Just warning, not invalidating
          console.warn(
            `Warning: Source '${data.source}' is not in the predefined list of sources`
          );
        }
      }
    // If seller_id is not provided
    if (!data.seller_id) {

      let errorMessage = `Cannot find seller id from the data provided `;
      if (data.amazon_seller_id && data.marketplace){
        const key = `${data.amazon_seller_id}-${data.marketplace}`;
        if (dict[`Stored:-${key}`]) {
          data.seller_id = dict[`Stored:-${key}`];
        }
        errorMessage += ` ${data.amazon_seller_id} and ${data.marketplace}`;
        if (data.domain){
          errorMessage += ` or`;
        }
      }
      // If domain is provided we will try to find the seller_id from the dict
      if (
        data.domain && !data.seller_id
      ) {
        // We will have a order of binding here US -> CA -> Other
        if (dict[`ProspectFlow:-${data.domain}-US`]) {
          data.seller_id = dict[`ProspectFlow:-${data.domain}-US`];
        } else if (dict[`ProspectFlow:-${data.domain}-CA`]) {
          data.seller_id = dict[`ProspectFlow:-${data.domain}-CA`];
        } else if (dict[`ProspectFlow:-${data.domain}-UK`]) {
          data.seller_id = dict[`ProspectFlow:-${data.domain}-UK`];
        }
        // If seller_id is not found, we will return an error
        // if (!data.seller_id) {
        //   isValid[0] = false;
        //   data.errors = data.errors
        //     ? [...data.errors, `Cannot find seller id from the data provided ${data.domain}`]
        //     : [`Cannot find seller id from the data provided ${data.domain}`];
        // }
        errorMessage += ` ${data.domain}`;
      }

      if (!data.seller_id){
        isValid[0] = false;
        data.errors = data.errors
          ? [...data.errors, errorMessage]
          : [errorMessage];
      }
    }



    // Additional business logic validations can be added here
    // delete data.domain;
    // delete data.amazon_seller_id;
    // delete data.marketplace;
  } catch (error) {
    console.error("Error validating amazon prospect data:", error);
    isValid[0] = false;
    data.errors = data.errors
      ? [...data.errors, `Validation error: ${error.message}`]
      : [`Validation error: ${error.message}`];
  }
}

// Export the schemas and validation functions
module.exports = {
  companySchema,
  amazonSellerSchema,
  prospectSchema,
  clientProspectMatchSchema,
  validateCompany,
  validateAmazonSeller,
  validateProspect,
  validateMatching,
  validate_update,
  lookupSources,
  amazonProspectSchema,
  validateAmazonProspect,
};
