const express = require("express");
const router = express.Router();
const PortkeyLangSmithWrapper = require("../services/ai/portkeyLangsmithWrapper");
const { getAssistantResponse } = require("../services/scrapeGPT/assistant");
const { getChatGPTResponse } = require("../services/scrapeGPT/request");
const { adminAuth } = require("../middlewares/jwt");

/**
 * @swagger
 * /api/portkey/chat:
 *   post:
 *     summary: Test Portkey + LangSmith chat completion
 *     description: Demonstrates chat completion using Portkey AI Gateway with LangSmith tracing
 *     tags: [Portkey Demo]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               message:
 *                 type: string
 *                 description: User message
 *                 example: "What are the best practices for Amazon SEO?"
 *               system_prompt:
 *                 type: string
 *                 description: System prompt (optional)
 *                 example: "You are an Amazon selling expert"
 *               temperature:
 *                 type: number
 *                 description: Temperature setting (0-1)
 *                 example: 0.7
 *               max_tokens:
 *                 type: integer
 *                 description: Maximum tokens to generate
 *                 example: 200
 *     responses:
 *       200:
 *         description: Successful chat completion
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 response:
 *                   type: string
 *                 usage:
 *                   type: object
 *                 model:
 *                   type: string
 *                 trace_id:
 *                   type: string
 */
router.post("/api/portkey/chat", adminAuth, async (req, res) => {
  try {
    const { 
      message, 
      system_prompt = "You are a helpful AI assistant specialized in e-commerce and Amazon selling.",
      temperature = 0.7,
      max_tokens = 200 
    } = req.body;

    if (!message) {
      return res.status(400).json({
        success: false,
        error: "Message is required"
      });
    }

    const wrapper = new PortkeyLangSmithWrapper('chat');
    
    const messages = [
      {
        role: 'system',
        content: system_prompt
      },
      {
        role: 'user',
        content: message
      }
    ];

    const response = await wrapper.createChatCompletion(messages, {
      temperature,
      max_tokens,
    });

    res.json({
      success: true,
      response: response.choices[0]?.message?.content,
      usage: response.usage,
      model: response.model,
      trace_id: response.id,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error("Portkey chat error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to process chat request",
      details: error.message
    });
  }
});

/**
 * @swagger
 * /api/portkey/assistant:
 *   post:
 *     summary: Test Portkey + LangSmith assistant
 *     description: Demonstrates assistant API using Portkey AI Gateway with LangSmith tracing
 *     tags: [Portkey Demo]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               assistant_id:
 *                 type: string
 *                 description: OpenAI Assistant ID
 *                 example: "asst_example123"
 *               data:
 *                 type: object
 *                 description: Data to send to assistant
 *                 example: {"company": "Example Corp", "revenue": "$1M"}
 *     responses:
 *       200:
 *         description: Successful assistant response
 */
router.post("/api/portkey/assistant", adminAuth, async (req, res) => {
  try {
    const { assistant_id, data } = req.body;

    if (!assistant_id || !data) {
      return res.status(400).json({
        success: false,
        error: "assistant_id and data are required"
      });
    }

    const response = await getAssistantResponse(
      assistant_id,
      JSON.stringify(data),
      true // Use Portkey
    );

    res.json({
      success: true,
      response: response.message,
      usage: {
        prompt_tokens: response.prompt_tokens,
        completion_tokens: response.completion_tokens,
        total_tokens: response.total_tokens
      },
      model: response.gptDetails,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error("Portkey assistant error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to process assistant request",
      details: error.message
    });
  }
});

/**
 * @swagger
 * /api/portkey/analyze:
 *   post:
 *     summary: Analyze text using Portkey + LangSmith
 *     description: Demonstrates text analysis with low temperature for consistent results
 *     tags: [Portkey Demo]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               text:
 *                 type: string
 *                 description: Text to analyze
 *               analysis_type:
 *                 type: string
 *                 enum: [sentiment, keywords, summary, selling_points]
 *                 description: Type of analysis to perform
 *     responses:
 *       200:
 *         description: Successful text analysis
 */
router.post("/api/portkey/analyze", adminAuth, async (req, res) => {
  try {
    const { text, analysis_type = 'summary' } = req.body;

    if (!text) {
      return res.status(400).json({
        success: false,
        error: "Text is required"
      });
    }

    const wrapper = new PortkeyLangSmithWrapper('analysis');
    
    const analysisPrompts = {
      sentiment: 'Analyze the sentiment of the following text and provide a score from -1 (negative) to 1 (positive) with explanation.',
      keywords: 'Extract the most important keywords and phrases from the following text.',
      summary: 'Provide a concise summary of the following text.',
      selling_points: 'Identify the key selling points and value propositions in the following text.'
    };

    const messages = [
      {
        role: 'system',
        content: `You are a text analysis expert. ${analysisPrompts[analysis_type]}`
      },
      {
        role: 'user',
        content: text
      }
    ];

    const response = await wrapper.createChatCompletion(messages, {
      temperature: 0.1, // Low temperature for consistent analysis
      max_tokens: 300,
    });

    res.json({
      success: true,
      analysis_type,
      result: response.choices[0]?.message?.content,
      usage: response.usage,
      model: response.model,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error("Portkey analysis error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to analyze text",
      details: error.message
    });
  }
});

/**
 * @swagger
 * /api/portkey/batch:
 *   post:
 *     summary: Process multiple requests in batch
 *     description: Demonstrates batch processing with Portkey + LangSmith
 *     tags: [Portkey Demo]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               requests:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     message:
 *                       type: string
 *     responses:
 *       200:
 *         description: Batch processing results
 */
router.post("/api/portkey/batch", adminAuth, async (req, res) => {
  try {
    const { requests } = req.body;

    if (!requests || !Array.isArray(requests)) {
      return res.status(400).json({
        success: false,
        error: "requests array is required"
      });
    }

    const wrapper = new PortkeyLangSmithWrapper('chat');
    const results = [];

    for (const request of requests) {
      try {
        const messages = [
          {
            role: 'system',
            content: 'You are a helpful assistant. Provide concise answers.'
          },
          {
            role: 'user',
            content: request.message
          }
        ];

        const response = await wrapper.createChatCompletion(messages, {
          temperature: 0.5,
          max_tokens: 150,
        });

        results.push({
          id: request.id,
          success: true,
          response: response.choices[0]?.message?.content,
          usage: response.usage
        });

        // Add delay to respect rate limits
        await new Promise(resolve => setTimeout(resolve, 500));

      } catch (error) {
        results.push({
          id: request.id,
          success: false,
          error: error.message
        });
      }
    }

    res.json({
      success: true,
      results,
      total_requests: requests.length,
      successful_requests: results.filter(r => r.success).length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error("Portkey batch error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to process batch requests",
      details: error.message
    });
  }
});

/**
 * @swagger
 * /api/portkey/stats:
 *   get:
 *     summary: Get Portkey usage statistics
 *     description: Retrieve usage statistics and monitoring data
 *     tags: [Portkey Demo]
 *     responses:
 *       200:
 *         description: Usage statistics
 */
router.get("/api/portkey/stats", adminAuth, async (req, res) => {
  try {
    const wrapper = new PortkeyLangSmithWrapper('chat');
    const stats = await wrapper.getUsageStats('24h');

    res.json({
      success: true,
      timeframe: '24h',
      stats: stats || {
        requests: 0,
        tokens: 0,
        cost: 0,
        errors: 0,
        avgLatency: 0
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error("Portkey stats error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to retrieve statistics",
      details: error.message
    });
  }
});

module.exports = router;
