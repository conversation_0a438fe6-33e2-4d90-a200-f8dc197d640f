const express = require("express");
const router = express.Router();
const prisma = require("../database/prisma/getPrismaClient"); // Adjust the path as necessary
const { adminAuth } = require("../middlewares/jwt");
const multer = require("multer");
const upload = multer({ dest: "uploads/" });
const converter = require("json-2-csv");
const { processLexCSV } = require("../services/lexReviewsChecker/lexCsvParser");
const { ReviewStatus } = require("@prisma/client");

/**
 * Formats a date to mm/dd/yyyy format without time
 * @param {Date|string|null} date - The date to format
 * @returns {string} Formatted date string or empty string if no date
 */
const formatDate = (date) => {
  if (!date) return "";

  try {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return "";

    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');
    const year = dateObj.getFullYear();

    return `${day}/${month}/${year}`;
  } catch (error) {
    console.error("Error formatting date:", error);
    return "";
  }
};

/**
 * Gets the latest date from an array of dates and formats it
 * @param {Array} dateArray - Array of date strings or Date objects
 * @returns {string} Formatted latest date string or empty string if no valid dates
 */
const getLatestDateFromArray = (dateArray) => {
  if (!Array.isArray(dateArray) || dateArray.length === 0) return "";

  try {
    // Filter out invalid dates and convert to Date objects
    const validDates = dateArray
      .map(date => new Date(date))
      .filter(date => !isNaN(date.getTime()));

    if (validDates.length === 0) return "";

    // Find the latest date
    const latestDate = new Date(Math.max(...validDates));
    return formatDate(latestDate);
  } catch (error) {
    console.error("Error getting latest date from array:", error);
    return "";
  }
};

/**
 * @swagger
 * /api/lex/jobs:
 *   get:
 *     summary: Get all review jobs
 *     description: Retrieves a list of all review jobs with their status and review counts.
 *     tags: [Review(Lex)]
 *     responses:
 *       200:
 *         description: List of review jobs
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: integer
 *                     example: 42
 *                   fileName:
 *                     type: string
 *                     example: "amazon_reviews.csv"
 *                   totalReviews:
 *                     type: integer
 *                     example: 150
 *                   pendingReviews:
 *                     type: integer
 *                     example: 25
 *                   removedReviews:
 *                     type: integer
 *                     example: 30
 *                   presentReviews:
 *                     type: integer
 *                     example: 85
 *                   failedReviews:
 *                     type: integer
 *                     example: 10
 *                   csvStatus:
 *                     type: string
 *                     example: "completed"
 *                   createdAt:
 *                     type: string
 *                     format: date-time
 *                     example: "2023-10-15T14:30:00Z"
 *                   updatedAt:
 *                     type: string
 *                     format: date-time
 *                     example: "2023-10-15T15:45:00Z"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 */

router.get(
  "/api/lex/jobs",
  // userAuth,
  async (req, res) => {
    try {
      const jobs = await prisma.reviewJob.findMany({
        include: {
          ReviewOutputData: true,
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      // Send all data to the client
      const response = jobs.map((job) => {
        const totalReviews = job.ReviewOutputData.length;

        const pendingReviews = job.ReviewOutputData.filter(
          (review) => review.status === ReviewStatus.PENDING
        ).length;
        const removedReviews = job.ReviewOutputData.filter(
          (review) => review.status === ReviewStatus.REMOVED
        ).length;
        const presentReviews = job.ReviewOutputData.filter(
          (review) => review.status === ReviewStatus.PRESENT
        ).length;
        const failedReviews = job.ReviewOutputData.filter(
          (review) => review.status === ReviewStatus.FAILED
        ).length;

        return {
          id: job.id,
          fileName: job.name,
          totalReviews,
          pendingReviews,
          removedReviews,
          presentReviews,
          failedReviews,
          csvStatus: job.status,
          createdAt: job.createdAt,
          updatedAt: job.updatedAt,
        };
      });

      res.status(200).json(response);
    } catch (error) {
      console.error("Error Stack:", error.stack);
      console.error("Error fetching jobs:", error.message);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

/**
 * @swagger
 * /api/lex/jobs/download-input-csv/{jobId}:
 *   get:
 *     summary: Download input CSV file for a specific job
 *     description: Retrieves the original input CSV file for a specific review job.
 *     tags: [Review(Lex)]
 *     parameters:
 *       - in: path
 *         name: jobId
 *         required: true
 *         schema:
 *           type: integer
 *         description: The job ID
 *     responses:
 *       200:
 *         description: CSV file containing the original input data
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 *         headers:
 *           Content-Disposition:
 *             schema:
 *               type: string
 *             description: Attachment header with filename
 *             example: attachment; filename="input_data_42.csv"
 *           Content-Type:
 *             schema:
 *               type: string
 *             example: text/csv
 *       404:
 *         description: Job not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Job not found"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 */
router.get(
  "/api/lex/jobs/download-input-csv/:jobId",
  adminAuth,
  async (req, res) => {
    try {
      const jobId = parseInt(req.params.jobId);
      const job = await prisma.reviewJob.findUnique({ where: { id: jobId } });
      if (!job) {
        return res.status(404).json({ error: "Job not found" });
      }
      // Check if the user is authorized to access this job
      // if (job.clientId !== req.user.userId && req.user.userType !== "admin") {
      //   return res.status(403).json({ error: "Unauthorized" });
      // };
      const inputCSV = await prisma.review.findMany({
        where: {
          reviewJobId: jobId,
        },
      });

      const csvContent = json2csv(inputCSV);

      // Set the filename for the CSV file
      const inputCsvFileName = `input_data_${job.id}.csv`;

      // Send the CSV file in response
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=${inputCsvFileName}`
      );
      res.setHeader("Content-Type", "text/csv");
      res.status(200).send(csvContent);
    } catch (error) {
      console.error("Error fetching output data:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

/**
 * @swagger
 * /api/lex/jobs:
 *   post:
 *     summary: Create a new review job
 *     description: Upload a CSV file to create a new review processing job.
 *     tags: [Review(Lex)]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               csvFile:
 *                 type: string
 *                 format: binary
 *                 description: CSV file containing review data to process
 *             required:
 *               - csvFile
 *     responses:
 *       200:
 *         description: Job created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "File uploaded successfully"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal server error"
 */
router.post(
  "/api/lex/jobs",
  adminAuth,
  upload.single("csvFile"),
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({
          error: "No file uploaded",
          details: "Please upload a CSV file",
        });
      }

      // Validate file type
      if (!req.file.originalname.toLowerCase().endsWith(".csv")) {
        return res.status(400).json({
          error: "Invalid file type",
          details: "Please upload a CSV file",
        });
      }

      const csvFilePath = req.file.path;
      const originalFileName = req.file.originalname;

      // Create job first
      const job = await prisma.reviewJob.create({
        data: {
          name: originalFileName,
        },
      });

      try {
        await processLexCSV(csvFilePath, 1, job);
        res.status(200).json({
          message: "File uploaded and processed successfully",
          jobId: job.id,
        });
      } catch (error) {
        // If CSV processing fails, delete the job
        await prisma.reviewJob.delete({
          where: { id: job.id },
        });

        if (error.type === "VALIDATION_ERROR") {
          return res.status(400).json({
            error: "CSV validation failed",
            details: error.errors.map((err) => `Row ${err.row}: ${err.error}`),
            invalidRows: error.errors.map((err) => err.row),
          });
        }

        throw error; // Re-throw other errors
      }
    } catch (error) {
      console.error("Error Stack:", error.stack);
      console.error("Error posting job:", error.message);
      res.status(500).json({
        error: "Internal server error",
        details: error.message,
      });
    }
  }
);

/**
 * @swagger
 * /api/lex/jobs/download-all-reviews:
 *   get:
 *     summary: Download all reviews as CSV
 *     description: Downloads all reviews from all jobs as a single CSV file.
 *     tags: [Review(Lex)]
 *     responses:
 *       200:
 *         description: CSV file containing all review data
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 *         headers:
 *           Content-Disposition:
 *             schema:
 *               type: string
 *             description: Attachment header with filename
 *             example: attachment; filename="lex_reviews_output_2023-10-15T14-30-00Z.csv"
 *           Content-Type:
 *             schema:
 *               type: string
 *             example: text/csv
 *       404:
 *         description: No data found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "No data found."
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 */
router.get(
  "/api/lex/jobs/download-all-reviews",
  adminAuth,
  async (req, res) => {
    try {
      // Fetch output data including the original inputData (JSON) and new fields
      const { removed } = req.query;
      const whereClause =
        removed === "true"
          ? {
            status: {
              in: [ReviewStatus.REMOVED, ReviewStatus.RESURRECTED],
            },
          }
          : {};
      const reviews = await prisma.review.findMany({
        where: whereClause,
      });

      if (!reviews.length) {
        return res.status(404).json({ error: "No data found." });
      }

      if (!reviews.length) {
        return res.status(404).json({ error: "No data found." });
      }
      const finalData = reviews.map((item) => ({
        "Review ID": item.reviewId || "",
        "Review URL": item.reviewUrl || "",
        "Review Title": item?.inputData["Review Title"] || "",
        Status: item.status || "",
        ASIN: item.asin || "",
        "Brand Name": item.brandName || "",
        "Removed At": formatDate(item.removedAt),
        "Run Frequency": item.run_frequency || "",
        "Latest Removed At": getLatestDateFromArray(item.removedHistory),
        "Latest Returned At": getLatestDateFromArray(item.returnedHistory),
        "Removed History": item.removedHistory || "",
        "Returned History": item.returnedHistory || "",
        "Total Runs": item.totalRuns || "",
        "Next Run": formatDate(item.next_run),
        "Recent Comments": item.comments || "",
        "Created At": formatDate(item.createdAt),
        "Updated At": formatDate(item.updatedAt),
        "Input Data": JSON.stringify(item?.inputData) || "",
      }));
      // Convert the combined data to CSV format
      const csvContent = converter.json2csv(finalData);

      const analyzableCsvFileName = `lex_reviews_output_${new Date().toISOString().replace(/[:.]/g, "-")}.csv`;

      // Send the CSV file in response
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=${analyzableCsvFileName}`
      );
      res.setHeader("Content-Type", "text/csv");
      res.status(200).send(csvContent);
    } catch (error) {
      console.error("Error fetching output data:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

/**
 * @swagger
 * /api/lex/reviews/removed:
 *   get:
 *     summary: Get all removed reviews
 *     description: Retrieves a paginated list of all reviews with "REMOVED" status.
 *     tags: [Review(Lex)]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: Sort order for updatedAt field
 *     responses:
 *       200:
 *         description: Paginated list of removed reviews
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 123
 *                       reviewJobId:
 *                         type: integer
 *                         example: 42
 *                       status:
 *                         type: string
 *                         example: "REMOVED"
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                         example: "2023-10-15T14:30:00Z"
 *                       updatedAt:
 *                         type: string
 *                         format: date-time
 *                         example: "2023-10-15T15:45:00Z"
 *                 meta:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                       example: 45
 *                     page:
 *                       type: integer
 *                       example: 1
 *                     limit:
 *                       type: integer
 *                       example: 10
 *                     totalPages:
 *                       type: integer
 *                       example: 5
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal server error"
 */
router.get("/api/lex/reviews/removed", adminAuth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const sort = req.query.sort === "asc" ? "asc" : "desc";

    const [removedReviews, totalCount] = await Promise.all([
      prisma.review.findMany({
        where: { status: ReviewStatus.REMOVED },
        orderBy: { updatedAt: sort },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.review.count({
        where: { status: ReviewStatus.REMOVED },
      }),
    ]);

    res.status(200).json({
      data: removedReviews,
      meta: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching removed reviews:", error.message);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @swagger
 * /api/jobs/download-csv/{jobId}:
 *   get:
 *     summary: Download job data as CSV
 *     tags: [Jobs(Email Harvesting From Seller Page)]
 *     parameters:
 *       - in: path
 *         name: jobId
 *         required: true
 *         schema:
 *           type: integer
 *         description: The job ID
 *     responses:
 *       200:
 *         description: CSV file containing combined input and scraped data |
 *           [Download output CSV](/examples/output_job.csv)
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 *             example: |
 *               "Name","Seller URL","Amazon Seller ID","contactNumber","sellerEmail","sellerAddress","sellerPincode","inputStatus"
 *               "Acme Inc","https://acme.com","A1B2C3D4E5","123-456-7890","<EMAIL>","123 Main St, City, State","12345","completed"
 *               "XYZ Corp","https://xyz-corp.com","F6G7H8I9J0","987-654-3210","<EMAIL>","456 Oak Ave, Town, State","67890","completed"
 *       404:
 *         description: Job or data not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Job not found"
 *       500:
 *         description: Internal server error
 */
router.get("/api/lex/jobs/download-csv/:jobId", async (req, res) => {
  try {
    const reviewJobId = parseInt(req.params.jobId);
    console.log("Downloading CSV for Job ID:", reviewJobId);

    // Find the Job with the given ID
    const job = await prisma.reviewJob.findUnique({
      where: { id: reviewJobId },
    });
    if (!job) {
      return res.status(404).json({ error: "Job not found" });
    }

    // Fetch output data including the original inputData (JSON) and new fields
    const outputData = await prisma.reviewOutputData.findMany({
      where: {
        reviewJobId,
      },
      include: {
        review: true,
      },
    });

    if (!outputData.length) {
      return res.status(404).json({ error: "No data found for this job" });
    }
    const finalData = outputData.map((item) => ({
      "Review ID": item.review?.reviewId || "",
      "Review URL": item.review?.reviewUrl || "",
      "Review Title": item.review?.inputData["Review Title"] || "",
      Status: item.review?.status || "",
      ASIN: item.review?.asin || "",
      "Brand Name": item.review?.brandName || "",
      "Recent Comments": item.review?.comments || "N/A",
      "Run Frequency": item.review?.run_frequency || "",
      "Removed At": formatDate(item.review?.removedAt),
      "Created At": formatDate(item.review?.createdAt),
      "Updated At": formatDate(item.review?.updatedAt),
      "Input Data": JSON.stringify(item.review?.inputData) || "",
    }));
    // Convert the combined data to CSV format
    const csvContent = converter.json2csv(finalData);

    // Set the filename for the CSV file
    const analyzableCsvFileName = `lex_job_data_${job.id}.csv`;

    // Send the CSV file in response
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=${analyzableCsvFileName}`
    );
    res.setHeader("Content-Type", "text/csv");
    res.status(200).send(csvContent);
  } catch (error) {
    console.error("Error fetching output data:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

module.exports = router;
