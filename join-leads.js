const csv = require("csv-parser");
const createCsvWriter = require("csv-writer").createObjectCsvWriter;
const fs = require("fs");
const { join } = require("path");

async function joinCsvs(inputPath, outputPath) {
  const inputData = [];
  const outputData = [];

  // Read input CSV
  await new Promise((resolve) => {
    fs.createReadStream(inputPath)
      .pipe(csv())
      .on("data", (row) => {
        inputData.push(row);
      })
      .on("end", resolve);
  });

  // Read output CSV
  await new Promise((resolve) => {
    fs.createReadStream(outputPath)
      .pipe(csv())
      .on("data", (row) => {
        outputData.push(row);
      })
      .on("end", resolve);
  });

  // Join data based on keywords with flattened JSON fields
  const joinedData = outputData.map((outputRow) => {
    if (!outputRow || !outputRow["Keywords"]) return {};

    // Parse JSON fields
    let individual = {};
    let validationResults = {};

    try {
      individual = JSON.parse(outputRow["Individual"] || "{}");
      validationResults = JSON.parse(outputRow["Validation Results"] || "{}");
    } catch (e) {
      console.log("JSON parsing error:", e);
    }

    const keywords = outputRow["Keywords"].split(/\s*[\|+]\s*/);
    const matchingInput = inputData.find(
      (inputRow) =>
        keywords.includes(inputRow.Name) &&
        keywords.includes(inputRow["Business Name"]),
    );

    return {
      // Output columns
      Keywords: outputRow["Keywords"],
      Confidence: outputRow["Confidence"],
      "Confident Url": outputRow["Confident Url"],
      "Seller Url": outputRow["Seller Url"],
      // Flattened Individual JSON
      Individual_Raw: outputRow["Individual"],
      Individual_CheckFuzzyDomain:
        individual.checkFuzzyDomain || "Url Not Found",
      Individual_CheckSerpOrganicSnippet:
        individual.checkSerpOrganicSnippet || "Snippet Not Found",
      Individual_CheckHtml: individual.checkHtml || "Html Not Found",
      // Flattened Validation Results JSON
      Validation_Raw: outputRow["Validation Results"],
      Validation_Text:
        validationResults.textValidationResult || "Text Not Found",
      Validation_Image:
        validationResults.imageValidationResult || "Image Not Found",
      Validation_Address:
        JSON.stringify(validationResults.addressValidationResult) ||
        "Address Not Found",
      // Flattened address validation fields
      Validation_Address_Matched:
        validationResults.addressValidationResult?.matchedAddress ||
        "Not Matched",
      Validation_Address_Confidence:
        validationResults.addressValidationResult?.bestConfidence || 0,
      Validation_Address_Score:
        validationResults.addressValidationResult?.finalScore || 0,
      "Company Address": outputRow["Company Address"],
      "Google Position": outputRow["Google Position"],
      "Filter Domain Position": outputRow["Filter Domain Position"],
      "Custom Status": outputRow["Custom Status"],
      // Input columns
      ...matchingInput,
    };
  });

  // Updated header with new flattened columns
  const csvWriter = createCsvWriter({
    path: `joined-leads-${Date.now()}.csv`,
    header: [
      // Output columns with flattened JSON fields
      { id: "Keywords", title: "Keywords" },
      { id: "Confidence", title: "Confidence" },
      { id: "Confident Url", title: "Confident Url" },
      { id: "Seller Url", title: "Seller Url" },
      { id: "Individual_Raw", title: "Individual Raw" },
      {
        id: "Individual_CheckFuzzyDomain",
        title: "Individual Check Fuzzy Domain",
      },
      {
        id: "Individual_CheckSerpOrganicSnippet",
        title: "Individual Check Serp Organic Snippet",
      },
      { id: "Individual_CheckHtml", title: "Individual Check Html" },
      { id: "Validation_Raw", title: "Validation Raw" },
      { id: "Validation_Text", title: "Validation Text" },
      { id: "Validation_Image", title: "Validation Image" },
      { id: "Validation_Address", title: "Validation Address" },
      { id: "Validation_Address_Matched", title: "Validation Address Matched" },
      {
        id: "Validation_Address_Confidence",
        title: "Validation Address Confidence",
      },
      { id: "Validation_Address_Score", title: "Validation Address Score" },
      { id: "Company Address", title: "Company Address" },
      { id: "Google Position", title: "Google Position" },
      { id: "Filter Domain Position", title: "Filter Domain Position" },
      { id: "Custom Status", title: "Custom Status" },
      // ... rest of the input columns remain the same
      { id: "ID", title: "ID" },
      { id: "Name", title: "Name" },
      { id: "Amazon Seller ID", title: "Amazon Seller ID" },
      { id: "Primary Category ID", title: "Primary Category ID" },
      { id: "Primary Category", title: "Primary Category" },
      { id: "Primary Sub Category", title: "Primary Sub Category" },
      { id: "Estimate Sales", title: "Estimate Sales" },
      { id: "Percent Fba", title: "Percent Fba" },
      { id: "Number Winning Brands", title: "Number Winning Brands" },
      { id: "Number Asins", title: "Number Asins" },
      { id: "Number Top Asins", title: "Number Top Asins" },
      { id: "State", title: "State" },
      { id: "Country", title: "Country" },
      { id: "Business Name", title: "Business Name" },
      { id: "Number Brands 1000", title: "Number Brands 1000" },
      { id: "Mom Growth", title: "Mom Growth" },
      { id: "Mom Growth Count", title: "Mom Growth Count" },
      { id: "Started Selling Date", title: "Started Selling Date" },
      { id: "Website", title: "Website" },
      { id: "Domain", title: "Domain" },
      { id: "Website Status", title: "Website Status" },
      { id: "Employee Count", title: "Employee Count" },
      { id: "Company Linkedin", title: "Company Linkedin" },
      { id: "Company Twitter", title: "Company Twitter" },
      { id: "Company Fb", title: "Company Fb" },
      { id: "Company Location", title: "Company Location" },
      { id: "CreatedAt", title: "CreatedAt" },
      { id: "UpdatedAt", title: "UpdatedAt" },
      { id: "Lookup Source", title: "Lookup Source" },
      { id: "Lookup Sources", title: "Lookup Sources" },
      { id: "Find Website Batch", title: "Find Website Batch" },
      { id: "Derived Estimate Sales", title: "Derived Estimate Sales" },
      { id: "Company Pincode", title: "Company Pincode" },
    ],
  });

  await csvWriter.writeRecords(joinedData);
  console.log("CSVs joined successfully with flattened JSON fields!");
}

joinCsvs(join(__dirname, "input.csv"), join(__dirname, "output.csv"));
