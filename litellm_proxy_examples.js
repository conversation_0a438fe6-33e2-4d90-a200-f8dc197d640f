#!/usr/bin/env node

/**
 * LiteLLM Proxy Usage Examples
 * 
 * This file demonstrates how to use the LiteLLM proxy in various scenarios
 * and how to integrate it with your existing SellerBot codebase.
 */

require('dotenv').config();
const OpenAI = require('openai');
const axios = require('axios');

// Configuration
const LITELLM_PROXY_URL = 'http://74.225.248.239:4000';
const API_KEY = process.env.OPENAI_API_KEY || 'test-key';

// Initialize OpenAI client with LiteLLM proxy
const litellmClient = new OpenAI({
    apiKey: API_KEY,
    baseURL: `${LITELLM_PROXY_URL}/v1`
});

/**
 * Example 1: Basic Chat Completion
 */
async function basicChatExample() {
    console.log('🔹 Example 1: Basic Chat Completion');
    
    try {
        const completion = await litellmClient.chat.completions.create({
            model: 'gpt-3.5-turbo',
            messages: [
                {
                    role: 'system',
                    content: 'You are a helpful assistant for e-commerce sellers.'
                },
                {
                    role: 'user',
                    content: 'What are the key factors for Amazon seller success?'
                }
            ],
            max_tokens: 200,
            temperature: 0.7
        });
        
        console.log('Response:', completion.choices[0].message.content);
        console.log('Usage:', completion.usage);
        
    } catch (error) {
        console.error('Error:', error.message);
    }
}

/**
 * Example 2: Streaming Response
 */
async function streamingExample() {
    console.log('\n🔹 Example 2: Streaming Response');
    
    try {
        const stream = await litellmClient.chat.completions.create({
            model: 'gpt-3.5-turbo',
            messages: [
                {
                    role: 'user',
                    content: 'Write a brief product description for a wireless bluetooth headphone.'
                }
            ],
            max_tokens: 150,
            temperature: 0.8,
            stream: true
        });
        
        console.log('Streaming response:');
        for await (const chunk of stream) {
            const content = chunk.choices[0]?.delta?.content || '';
            if (content) {
                process.stdout.write(content);
            }
        }
        console.log('\n');
        
    } catch (error) {
        console.error('Error:', error.message);
    }
}

/**
 * Example 3: Multiple Models Comparison
 */
async function multiModelExample() {
    console.log('\n🔹 Example 3: Multiple Models Comparison');
    
    const models = ['gpt-3.5-turbo', 'gpt-4'];
    const prompt = 'Explain the benefits of using AI for e-commerce in one sentence.';
    
    for (const model of models) {
        try {
            console.log(`\nTesting ${model}:`);
            
            const completion = await litellmClient.chat.completions.create({
                model: model,
                messages: [{ role: 'user', content: prompt }],
                max_tokens: 100,
                temperature: 0.5
            });
            
            console.log(`${model} response:`, completion.choices[0].message.content);
            
        } catch (error) {
            console.log(`${model} failed:`, error.message);
        }
    }
}

/**
 * Example 4: Integration with SellerBot Pattern
 * This shows how to integrate with your existing AI service patterns
 */
class LiteLLMAIService {
    constructor(proxyUrl = LITELLM_PROXY_URL, apiKey = API_KEY) {
        this.client = new OpenAI({
            apiKey: apiKey,
            baseURL: `${proxyUrl}/v1`
        });
        this.proxyUrl = proxyUrl;
    }
    
    async getHealthStatus() {
        try {
            const response = await axios.get(`${this.proxyUrl}/health`);
            return {
                status: 'healthy',
                data: response.data
            };
        } catch (error) {
            return {
                status: 'unhealthy',
                error: error.message
            };
        }
    }
    
    async getAvailableModels() {
        try {
            const response = await axios.get(`${this.proxyUrl}/v1/models`, {
                headers: {
                    'Authorization': `Bearer ${this.client.apiKey}`
                }
            });
            return response.data.data || [];
        } catch (error) {
            console.error('Failed to fetch models:', error.message);
            return [];
        }
    }
    
    async createChatCompletion(messages, options = {}) {
        const defaultOptions = {
            model: 'gpt-3.5-turbo',
            max_tokens: 500,
            temperature: 0.7,
            ...options
        };
        
        try {
            const startTime = Date.now();
            
            const completion = await this.client.chat.completions.create({
                messages,
                ...defaultOptions
            });
            
            const duration = Date.now() - startTime;
            
            return {
                success: true,
                message: completion.choices[0]?.message?.content,
                usage: completion.usage,
                duration,
                model: defaultOptions.model,
                provider: 'litellm-proxy'
            };
            
        } catch (error) {
            return {
                success: false,
                error: error.message,
                provider: 'litellm-proxy'
            };
        }
    }
    
    // Method compatible with your existing scrapeGPT patterns
    async getChatGPTResponse(systemPrompt, userPrompt, usePortkey = false) {
        const messages = [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
        ];
        
        return await this.createChatCompletion(messages);
    }
}

/**
 * Example 5: Using the LiteLLM AI Service
 */
async function aiServiceExample() {
    console.log('\n🔹 Example 4: LiteLLM AI Service Integration');
    
    const aiService = new LiteLLMAIService();
    
    // Check health
    const health = await aiService.getHealthStatus();
    console.log('Health Status:', health);
    
    // Get available models
    const models = await aiService.getAvailableModels();
    console.log('Available Models:', models.slice(0, 3).map(m => m.id));
    
    // Test chat completion
    const result = await aiService.createChatCompletion([
        {
            role: 'system',
            content: 'You are an AI assistant specialized in e-commerce and Amazon selling.'
        },
        {
            role: 'user',
            content: 'What are the top 3 metrics Amazon sellers should track?'
        }
    ]);
    
    if (result.success) {
        console.log('AI Response:', result.message);
        console.log('Duration:', result.duration + 'ms');
        console.log('Tokens Used:', result.usage?.total_tokens);
    } else {
        console.log('Error:', result.error);
    }
    
    // Test with scrapeGPT-compatible method
    const scrapeResult = await aiService.getChatGPTResponse(
        'You are a product description analyzer.',
        'Analyze this product title: "Wireless Bluetooth Headphones with Noise Cancellation"'
    );
    
    console.log('ScrapeGPT-style result:', scrapeResult.success ? scrapeResult.message : scrapeResult.error);
}

/**
 * Example 6: Error Handling and Fallback
 */
async function errorHandlingExample() {
    console.log('\n🔹 Example 5: Error Handling and Fallback');
    
    const aiService = new LiteLLMAIService();
    
    // Test with an invalid model
    const result = await aiService.createChatCompletion([
        { role: 'user', content: 'Hello' }
    ], { model: 'invalid-model-name' });
    
    if (!result.success) {
        console.log('Expected error for invalid model:', result.error);
        
        // Fallback to a known working model
        console.log('Falling back to gpt-3.5-turbo...');
        const fallbackResult = await aiService.createChatCompletion([
            { role: 'user', content: 'Hello' }
        ], { model: 'gpt-3.5-turbo' });
        
        if (fallbackResult.success) {
            console.log('Fallback successful:', fallbackResult.message);
        }
    }
}

/**
 * Example 7: Direct HTTP Request (without OpenAI library)
 */
async function directHttpExample() {
    console.log('\n🔹 Example 6: Direct HTTP Request');
    
    try {
        const response = await axios.post(`${LITELLM_PROXY_URL}/v1/chat/completions`, {
            model: 'gpt-3.5-turbo',
            messages: [
                {
                    role: 'user',
                    content: 'What is the capital of France?'
                }
            ],
            max_tokens: 50
        }, {
            headers: {
                'Authorization': `Bearer ${API_KEY}`,
                'Content-Type': 'application/json'
            }
        });
        
        console.log('Direct HTTP Response:', response.data.choices[0].message.content);
        
    } catch (error) {
        console.error('Direct HTTP Error:', error.message);
    }
}

/**
 * Main function to run all examples
 */
async function main() {
    console.log('🚀 LiteLLM Proxy Usage Examples\n');
    console.log(`📍 Proxy URL: ${LITELLM_PROXY_URL}`);
    console.log(`🔑 API Key: ${API_KEY.substring(0, 10)}...\n`);
    
    try {
        await basicChatExample();
        await streamingExample();
        await multiModelExample();
        await aiServiceExample();
        await errorHandlingExample();
        await directHttpExample();
        
        console.log('\n✅ All examples completed!');
        console.log('\n💡 Integration Tips:');
        console.log('1. Replace your existing OpenAI baseURL with the LiteLLM proxy URL');
        console.log('2. Use the LiteLLMAIService class as a drop-in replacement for your AI services');
        console.log('3. The proxy supports multiple models - check /v1/models endpoint for available options');
        console.log('4. Implement proper error handling and fallback mechanisms');
        
    } catch (error) {
        console.error('❌ Examples failed:', error);
    }
}

// Export the service class for use in other files
module.exports = {
    LiteLLMAIService,
    LITELLM_PROXY_URL,
    litellmClient
};

// Run examples if this file is executed directly
if (require.main === module) {
    main();
}
