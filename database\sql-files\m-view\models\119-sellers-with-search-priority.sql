DROP MATERIALIZED VIEW IF EXISTS "public"."SellersWithSearchPriority";
CREATE MATERIALIZED VIEW "public"."SellersWithSearchPriority" AS
WITH company_data AS (
  SELECT
    c.name,
    c.amazon_seller_id,
    c.primary_category,
    c.business_name,
    c.domain,
    c.website,
    c.website_status,
    c.lookup_sources,
    c.lookup_source,
    c.country,
    c.derived_estimate_sales,
    COUNT(CASE WHEN p.email_status IN ('CATCHALL', 'VERIFIED', 'GREYLISTING') THEN p.prospect_id END) AS jeff_num_prospects,
    COUNT(CASE WHEN p.email_status = 'INCONCLUSIVE' THEN p.prospect_id END) AS jeff_num_inconclusive,
    COUNT(CASE WHEN p.email_status = 'UNAVAILABLE' THEN p.prospect_id END) AS jeff_num_unavailable,
    COUNT(CASE WHEN p.email_status = 'FAILED' THEN p.prospect_id END) AS jeff_num_failed,
    COUNT(CASE WHEN p.email_status = 'UNVERIFIED' THEN p.prospect_id END) AS jeff_num_unverified,
    CASE
      WHEN c.country = 'CN' THEN 'SP9'
      WHEN (c.derived_estimate_sales IS NULL  OR c.derived_estimate_sales = 0)
           AND c.country IN ('US','GB','AU','IL','DE','ES','FR','NL','NZ','IE','CH','EE','SE','BE', 'CA')
        THEN 'SP4'
      WHEN (c.derived_estimate_sales IS NULL OR c.derived_estimate_sales = 0)
           AND c.country NOT IN ('US','GB','AU','IL','DE','ES','FR','NL','NZ','IE','CH','EE','SE','BE', 'CA')
        THEN 'SP8'
      WHEN c.derived_estimate_sales > 0
           AND c.derived_estimate_sales <= 10000
           AND c.country IN ('US','GB','AU','IL','DE','ES','FR','NL','NZ','IE','CH','EE','SE','BE', 'CA')
        THEN 'SP3B'
      WHEN c.derived_estimate_sales > 10000
           AND c.derived_estimate_sales < 20000
           AND c.country IN ('US','GB','AU','IL','DE','ES','FR','NL','NZ','IE','CH','EE','SE','BE', 'CA')
        THEN 'SP3A'
      WHEN c.derived_estimate_sales > 0
           AND c.derived_estimate_sales < 20000
           AND c.country NOT IN ('US','GB','AU','IL','DE','ES','FR','NL','NZ','IE','CH','EE','SE','BE', 'CA')
        THEN 'SP7'
      WHEN c.derived_estimate_sales > 20000
           AND c.derived_estimate_sales < 50000
           AND c.country IN ('US','GB','AU','IL','DE','ES','FR','NL','NZ','IE','CH','EE','SE','BE', 'CA')
        THEN 'SP2'
      WHEN c.derived_estimate_sales > 20000
           AND c.derived_estimate_sales < 50000
           AND c.country NOT IN ('US','GB','AU','IL','DE','ES','FR','NL','NZ','IE','CH','EE','SE','BE', 'CA')
        THEN 'SP6'
      WHEN c.derived_estimate_sales > 50000
           AND c.country IN ('US','GB','AU','IL','DE','ES','FR','NL','NZ','IE','CH','EE','SE','BE', 'CA')
        THEN 'SP1'
      WHEN c.derived_estimate_sales > 50000
           AND c.country NOT IN ('US','GB','AU','IL','DE','ES','FR','NL','NZ','IE','CH','EE','SE','BE', 'CA')
        THEN 'SP5'
      ELSE NULL
    END AS jeff_search_priority,
    c.id
  FROM
    "public"."Company" c
  LEFT JOIN 
    "public"."Prospect" p ON p."company_id" = c."id"
  GROUP BY
    c.id,
    c.name,
    c.amazon_seller_id,
    c.primary_category_id,
    c.primary_category,
    c.primary_sub_category,
    c.business_name,
    c.state,
    c.domain,
    c.website,
    c.website_status,
    c.lookup_sources,
    c.lookup_source,
    c.country,
    c.derived_estimate_sales,
    c.estimate_sales
)

SELECT
  *,
  CASE
    WHEN jeff_num_prospects = 0 THEN jeff_search_priority || ':0'
    WHEN jeff_search_priority IN ('SP5', 'SP6', 'SP7', 'SP8', 'SP9') THEN jeff_search_priority || ':4'
    WHEN jeff_num_prospects BETWEEN 1 AND 3 THEN jeff_search_priority || ':4'
    WHEN jeff_num_prospects >= 4 THEN jeff_search_priority || ':4+'
    ELSE jeff_search_priority
  END AS jeff_send_priority
FROM
  company_data;