-- CreateTable
CREATE TABLE "SellerCountryMatching" (
    "id" SERIAL NOT NULL,
    "amazon_seller_id" TEXT NOT NULL DEFAULT '',
    "smartscout_country" TEXT NOT NULL DEFAULT '',
    "seller_url" TEXT NOT NULL DEFAULT '',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SellerCountryMatching_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "SellerCountryMatching_amazon_seller_id_idx" ON "SellerCountryMatching"("amazon_seller_id");

-- CreateIndex
CREATE INDEX "SellerCountryMatching_smartscout_country_idx" ON "SellerCountryMatching"("smartscout_country");
