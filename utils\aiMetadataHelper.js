/**
 * AI Metadata Helper
 * 
 * This utility helps generate consistent metadata for AI requests
 * to enable proper tracking in LiteLLM/Lunary and other observability tools.
 */

const os = require('os');
const path = require('path');

class AIMetadataHelper {
    constructor() {
        this.sessionId = this.generateSessionId();
        this.startTime = Date.now();
    }

    /**
     * Generate a unique session ID for this application instance
     */
    generateSessionId() {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 8);
        return `sellerbot_${timestamp}_${random}`;
    }

    /**
     * Generate a unique request ID
     */
    generateRequestId() {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 8);
        return `req_${timestamp}_${random}`;
    }

    /**
     * Get the calling function name from the stack trace
     */
    getCallingFunction() {
        const stack = new Error().stack;
        const stackLines = stack.split('\n');
        
        // Skip the first few lines (Error, this function, and immediate caller)
        for (let i = 3; i < stackLines.length; i++) {
            const line = stackLines[i];
            if (line.includes('at ') && !line.includes('node_modules')) {
                // Extract function name and file
                const match = line.match(/at\s+([^\s]+)\s+\(([^)]+)\)/);
                if (match) {
                    const functionName = match[1];
                    const filePath = match[2];
                    const fileName = path.basename(filePath);
                    return {
                        function: functionName,
                        file: fileName,
                        path: filePath
                    };
                }
                
                // Handle anonymous functions
                const anonymousMatch = line.match(/at\s+([^(]+)/);
                if (anonymousMatch) {
                    const location = anonymousMatch[1].trim();
                    return {
                        function: 'anonymous',
                        file: path.basename(location),
                        path: location
                    };
                }
            }
        }
        
        return {
            function: 'unknown',
            file: 'unknown',
            path: 'unknown'
        };
    }

    /**
     * Generate comprehensive metadata for AI requests
     */
    generateMetadata(options = {}) {
        const caller = this.getCallingFunction();
        const requestId = this.generateRequestId();
        
        const baseMetadata = {
            // Request identification
            request_id: requestId,
            session_id: this.sessionId,
            timestamp: new Date().toISOString(),
            
            // Application context
            service: 'SellerBot',
            version: '1.0.0',
            environment: process.env.NODE_ENV || 'development',
            
            // Function context
            calling_function: caller.function,
            calling_file: caller.file,
            calling_path: caller.path,
            
            // System context
            hostname: os.hostname(),
            platform: os.platform(),
            node_version: process.version,
            
            // User context (if available)
            user_id: options.userId || 'system',
            user_type: options.userType || 'internal',
            
            // Business context
            use_case: options.useCase || 'general',
            operation_type: options.operationType || 'chat_completion',
            
            // Custom tags
            tags: {
                component: options.component || 'ai_service',
                feature: options.feature || 'unknown',
                priority: options.priority || 'normal',
                ...options.customTags
            }
        };

        // Add specific metadata based on operation type
        if (options.operationType === 'scrape_analysis') {
            baseMetadata.tags.domain = options.domain;
            baseMetadata.tags.scrape_type = options.scrapeType;
        } else if (options.operationType === 'lead_generation') {
            baseMetadata.tags.lead_source = options.leadSource;
            baseMetadata.tags.batch_id = options.batchId;
        } else if (options.operationType === 'email_analysis') {
            baseMetadata.tags.email_type = options.emailType;
            baseMetadata.tags.campaign_id = options.campaignId;
        }

        return baseMetadata;
    }

    /**
     * Generate metadata specifically for LiteLLM proxy requests
     */
    generateLiteLLMMetadata(options = {}) {
        const metadata = this.generateMetadata(options);
        
        // LiteLLM specific fields
        return {
            ...metadata,
            
            // LiteLLM proxy fields
            proxy_version: 'litellm',
            routing_strategy: options.routingStrategy || 'default',
            fallback_enabled: options.fallbackEnabled !== false,
            
            // Cost tracking
            cost_tracking: {
                enabled: true,
                budget_category: options.budgetCategory || 'general',
                cost_center: options.costCenter || 'ai_operations'
            },
            
            // Performance tracking
            performance: {
                expected_latency: options.expectedLatency || 'normal',
                priority_level: options.priorityLevel || 'standard'
            }
        };
    }

    /**
     * Generate headers for HTTP requests with metadata
     */
    generateHeaders(options = {}) {
        const metadata = this.generateLiteLLMMetadata(options);
        
        return {
            'X-LiteLLM-Metadata': JSON.stringify(metadata),
            'X-Request-ID': metadata.request_id,
            'X-Session-ID': metadata.session_id,
            'X-Service': metadata.service,
            'X-Function': metadata.calling_function,
            'X-Use-Case': metadata.use_case,
            'X-Operation-Type': metadata.operation_type,
            'X-User-ID': metadata.user_id,
            'X-Environment': metadata.environment
        };
    }

    /**
     * Generate metadata for OpenAI client requests in LiteLLM format
     */
    generateOpenAIMetadata(options = {}) {
        const caller = this.getCallingFunction();
        const requestId = this.generateRequestId();

        // Generate tags in LiteLLM format: ["key:value", "key:value"]
        const tags = [
            `requestId:${requestId}`,
            `sessionId:${this.sessionId}`,
            `service:SellerBot`,
            `function:${caller.function}`,
            `file:${caller.file}`,
            `useCase:${options.useCase || 'general'}`,
            `operationType:${options.operationType || 'chat_completion'}`,
            `feature:${options.feature || 'unknown'}`,
            `environment:${process.env.NODE_ENV || 'development'}`
        ];

        // Add business context tags
        if (options.scrapeType) tags.push(`scrapeType:${options.scrapeType}`);
        if (options.domain) tags.push(`domain:${options.domain}`);
        if (options.batchId) tags.push(`batchId:${options.batchId}`);
        if (options.campaignId) tags.push(`campaignId:${options.campaignId}`);
        if (options.leadSource) tags.push(`leadSource:${options.leadSource}`);
        if (options.emailType) tags.push(`emailType:${options.emailType}`);
        if (options.assistantId) tags.push(`assistantId:${options.assistantId}`);
        if (options.model) tags.push(`modelRequested:${options.model}`);
        if (options.temperature) tags.push(`temperature:${options.temperature}`);
        if (options.priority) tags.push(`priority:${options.priority}`);

        // Add custom tags
        if (options.customTags) {
            Object.entries(options.customTags).forEach(([key, value]) => {
                tags.push(`${key}:${value}`);
            });
        }

        return {
            user: options.userId || options.user || 'system',
            metadata: {
                tags: tags,
                request_id: requestId,
                session_id: this.sessionId,
                timestamp: new Date().toISOString(),
                calling_function: caller.function,
                calling_file: caller.file
            }
        };
    }

    /**
     * Generate LiteLLM compatible request body with metadata
     */
    generateLiteLLMRequestBody(messages, options = {}) {
        const openaiMetadata = this.generateOpenAIMetadata(options);

        // Only include valid OpenAI API parameters
        const requestBody = {
            model: options.model || 'gpt-4o',
            messages: messages,
            user: openaiMetadata.user,
            metadata: openaiMetadata.metadata
        };

        // Add optional OpenAI parameters only if they exist
        if (options.temperature !== undefined) requestBody.temperature = options.temperature;
        if (options.max_tokens !== undefined) requestBody.max_tokens = options.max_tokens;
        if (options.presence_penalty !== undefined) requestBody.presence_penalty = options.presence_penalty;
        if (options.frequency_penalty !== undefined) requestBody.frequency_penalty = options.frequency_penalty;
        if (options.top_p !== undefined) requestBody.top_p = options.top_p;
        if (options.stream !== undefined) requestBody.stream = options.stream;
        if (options.stop !== undefined) requestBody.stop = options.stop;
        if (options.logit_bias !== undefined) requestBody.logit_bias = options.logit_bias;
        if (options.seed !== undefined) requestBody.seed = options.seed;
        if (options.tools !== undefined) requestBody.tools = options.tools;
        if (options.tool_choice !== undefined) requestBody.tool_choice = options.tool_choice;
        if (options.response_format !== undefined) requestBody.response_format = options.response_format;

        return requestBody;
    }

    /**
     * Log metadata for debugging purposes
     */
    logMetadata(metadata, level = 'info') {
        if (process.env.AI_METADATA_LOGGING === 'true') {
            console.log(`[AI_METADATA_${level.toUpperCase()}]`, JSON.stringify(metadata, null, 2));
        }
    }

    /**
     * Create a scoped metadata generator for a specific component
     */
    createScopedGenerator(componentOptions = {}) {
        return (requestOptions = {}) => {
            return this.generateLiteLLMMetadata({
                ...componentOptions,
                ...requestOptions
            });
        };
    }
}

// Create singleton instance
const aiMetadataHelper = new AIMetadataHelper();

// Pre-configured generators for common use cases
const metadataGenerators = {
    scrapeGPT: aiMetadataHelper.createScopedGenerator({
        component: 'scrapeGPT',
        operationType: 'scrape_analysis',
        feature: 'content_analysis'
    }),
    
    leadGeneration: aiMetadataHelper.createScopedGenerator({
        component: 'lead_generation',
        operationType: 'lead_generation',
        feature: 'prospect_analysis'
    }),
    
    emailAnalysis: aiMetadataHelper.createScopedGenerator({
        component: 'email_analysis',
        operationType: 'email_analysis',
        feature: 'email_classification'
    }),
    
    centralizedAI: aiMetadataHelper.createScopedGenerator({
        component: 'centralized_ai',
        operationType: 'chat_completion',
        feature: 'general_ai'
    }),
    
    assistant: aiMetadataHelper.createScopedGenerator({
        component: 'assistant',
        operationType: 'assistant_run',
        feature: 'structured_analysis'
    })
};

module.exports = {
    AIMetadataHelper,
    aiMetadataHelper,
    metadataGenerators
};
