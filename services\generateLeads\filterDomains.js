const { extractDomain } = require("../../utils/domainHelper");
const popularEcommerceDomains = require("./data/popularDomains");
const fileExtensions = require("./data/fileExtensions");

function filterDomains(organicResults) {
  const filteredDomains = [];
  const checkedDomains = [];
  for (const { link: url, ...rest } of organicResults) {
    if (url.length < 5) continue;
    // Check for any file extension
    if (fileExtensions.some((ext) => url?.toLowerCase()?.endsWith(ext)))
      continue;

    const domainValue = extractDomain(url);
    const domain = url.split(domainValue)[0] + domainValue;
    if (checkedDomains.includes(domainValue)) {
      continue;
    }
    checkedDomains.push(domainValue);

    if (!domain) continue;

    // Check if domain exists and doesn't contain any popular ecommerce names
    const isPopularEcommerce = popularEcommerceDomains.some((ecommDomain) =>
      domain.toLowerCase().includes(ecommDomain),
    );

    if (domain && !isPopularEcommerce) {
      filteredDomains.push({
        ...rest,
        originalUrl: url,
        domain: domain,
        organicData: rest,
      });
    }
  }

  return filteredDomains;
}

module.exports = { filterDomains };
