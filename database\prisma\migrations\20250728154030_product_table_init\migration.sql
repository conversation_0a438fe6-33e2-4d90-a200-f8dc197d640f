-- CreateTable
CREATE TABLE "products" (
    "id" SERIAL NOT NULL,
    "url" TEXT NOT NULL,
    "brand_name" VARCHAR(255),
    "product_title" TEXT,
    "description" TEXT,
    "price" DECIMAL(10,2),
    "rating" DECIMAL(3,2),
    "total_reviews" INTEGER,
    "review_category" VARCHAR(100),
    "star_5_count" INTEGER,
    "star_4_count" INTEGER,
    "star_3_count" INTEGER,
    "star_2_count" INTEGER,
    "star_1_count" INTEGER,
    "sales_count" INTEGER,
    "main_image_url" TEXT,
    "image_count" INTEGER,
    "video_count" INTEGER,
    "title_char_count" INTEGER,
    "title_under_150_chars" BOOLEAN,
    "out_of_stock" BOOLEAN NOT NULL DEFAULT false,
    "aplus_content_present" BOOLEAN NOT NULL DEFAULT false,
    "premium_aplus_present" BOOLEAN NOT NULL DEFAULT false,
    "brand_story_present" BOOLEAN NOT NULL DEFAULT false,
    "storefront_present" BOOLEAN NOT NULL DEFAULT false,
    "storefront_url" TEXT,
    "bullet_points" JSONB DEFAULT '{}',
    "categories_and_ranks" JSONB DEFAULT '{}',
    "secondary_images" JSONB DEFAULT '{}',
    "brand_story_images" JSONB DEFAULT '{}',
    "full_json_data" JSONB NOT NULL DEFAULT '{}',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "products_pkey" PRIMARY KEY ("id")
);
