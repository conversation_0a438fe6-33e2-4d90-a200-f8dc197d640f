#!/usr/bin/env node

/**
 * Test script for Portkey + <PERSON><PERSON>mith integration
 * 
 * This script tests the integration and provides setup validation
 */

const { validateConfig } = require('../config/portkey');
const PortkeyLangSmithWrapper = require('../services/ai/portkeyLangsmithWrapper');
require('dotenv').config();

async function testConfiguration() {
  console.log('🔧 Testing Portkey + LangSmith Configuration...\n');
  
  const validation = validateConfig();
  
  console.log('Configuration Status:', validation.isValid ? '✅ Valid' : '❌ Invalid');
  console.log('Message:', validation.message);
  
  if (validation.missing.length > 0) {
    console.log('\n❌ Missing Required Variables:');
    validation.missing.forEach(key => console.log(`  - ${key}`));
  }
  
  if (validation.warnings.length > 0) {
    console.log('\n⚠️  Optional Variables Not Set:');
    validation.warnings.forEach(key => console.log(`  - ${key}`));
  }
  
  return validation.isValid;
}

async function testBasicChatCompletion() {
  console.log('\n🤖 Testing Basic Chat Completion...');
  
  try {
    const wrapper = new PortkeyLangSmithWrapper('chat');
    
    const messages = [
      {
        role: 'system',
        content: 'You are a helpful assistant. Respond with exactly "Hello from Portkey + LangSmith!"'
      },
      {
        role: 'user',
        content: 'Say hello'
      }
    ];
    
    console.log('Sending request...');
    const startTime = Date.now();
    
    const response = await wrapper.createChatCompletion(messages, {
      temperature: 0,
      max_tokens: 50,
    });
    
    const duration = Date.now() - startTime;
    
    console.log('✅ Chat completion successful!');
    console.log('Response:', response.choices[0]?.message?.content);
    console.log('Model:', response.model);
    console.log('Duration:', `${duration}ms`);
    console.log('Usage:', response.usage);
    
    return true;
    
  } catch (error) {
    console.log('❌ Chat completion failed:', error.message);
    return false;
  }
}

async function testFallbackMechanism() {
  console.log('\n🔄 Testing Fallback Mechanism...');
  
  try {
    // Test with invalid Portkey config to trigger fallback
    const { getChatGPTResponse } = require('../services/scrapeGPT/request');
    
    console.log('Testing fallback to direct OpenAI...');
    const response = await getChatGPTResponse(
      'You are a test assistant.',
      'Respond with "Fallback working!"',
      false // Force fallback
    );
    
    console.log('✅ Fallback mechanism working!');
    console.log('Response:', response.message);
    
    return true;
    
  } catch (error) {
    console.log('❌ Fallback test failed:', error.message);
    return false;
  }
}

async function testUsageStats() {
  console.log('\n📊 Testing Usage Statistics...');
  
  try {
    const wrapper = new PortkeyLangSmithWrapper('chat');
    const stats = await wrapper.getUsageStats('24h');
    
    console.log('✅ Usage stats retrieved!');
    console.log('Stats:', stats);
    
    return true;
    
  } catch (error) {
    console.log('❌ Usage stats test failed:', error.message);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 Portkey + LangSmith Integration Test Suite');
  console.log('==============================================\n');
  
  const results = {
    configuration: false,
    chatCompletion: false,
    fallback: false,
    usageStats: false,
  };
  
  // Test 1: Configuration
  results.configuration = await testConfiguration();
  
  // Test 2: Basic Chat Completion (only if config is valid)
  if (results.configuration) {
    results.chatCompletion = await testBasicChatCompletion();
  } else {
    console.log('\n⏭️  Skipping chat completion test due to invalid configuration');
  }
  
  // Test 3: Fallback Mechanism
  results.fallback = await testFallbackMechanism();
  
  // Test 4: Usage Stats
  if (results.configuration) {
    results.usageStats = await testUsageStats();
  } else {
    console.log('\n⏭️  Skipping usage stats test due to invalid configuration');
  }
  
  // Summary
  console.log('\n📋 Test Results Summary');
  console.log('========================');
  console.log(`Configuration: ${results.configuration ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Chat Completion: ${results.chatCompletion ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Fallback Mechanism: ${results.fallback ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Usage Statistics: ${results.usageStats ? '✅ PASS' : '❌ FAIL'}`);
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! Integration is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Check the configuration and try again.');
  }
  
  // Setup instructions if needed
  if (!results.configuration) {
    console.log('\n📝 Setup Instructions:');
    console.log('1. Get your Portkey API key from: https://app.portkey.ai/');
    console.log('2. Get your LangSmith API key from: https://smith.langchain.com/');
    console.log('3. Create virtual keys in Portkey for your AI providers');
    console.log('4. Update your .env file with the required keys');
    console.log('5. Run this test script again');
  }
  
  return passedTests === totalTests;
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test suite failed:', error);
      process.exit(1);
    });
}

module.exports = {
  testConfiguration,
  testBasicChatCompletion,
  testFallbackMechanism,
  testUsageStats,
  runAllTests,
};
