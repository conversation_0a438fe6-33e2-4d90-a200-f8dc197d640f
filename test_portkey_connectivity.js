#!/usr/bin/env node

/**
 * Simple Portkey Connectivity Test
 * 
 * This script tests basic connectivity to the Portkey gateway
 * and helps diagnose configuration issues.
 */

require('dotenv').config();
const axios = require('axios');

async function testPortkeyConnectivity() {
    console.log('🔍 Testing Portkey Connectivity\n');
    
    const portkeyBaseUrl = process.env.PORTKEY_BASE_URL;
    const openaiApiKey = process.env.OPENAI_API_KEY;
    const azureEndpoint = process.env.AZURE_OPENAI_ENDPOINT;
    const azureApiVersion = process.env.AZURE_OPENAI_API_VERSION;
    
    console.log(`Portkey Base URL: ${portkeyBaseUrl}`);
    console.log(`Azure Endpoint: ${azureEndpoint}`);
    console.log(`Azure API Version: ${azureApiVersion}`);
    console.log(`OpenAI API Key: ${openaiApiKey ? openaiApiKey.substring(0, 20) + '...' : 'NOT SET'}`);
    console.log('');
    
    if (!portkeyBaseUrl || !openaiApiKey || !azureEndpoint) {
        console.log('❌ Missing required environment variables!');
        return false;
    }
    
    // Test 1: Basic connectivity to Portkey
    console.log('🔄 Test 1: Basic Portkey connectivity...');
    try {
        const response = await axios.get(`${portkeyBaseUrl}/health`, {
            timeout: 5000,
            validateStatus: () => true
        });
        
        console.log(`Status: ${response.status}`);
        if (response.status === 200) {
            console.log('✅ Portkey gateway is accessible');
        } else {
            console.log(`⚠️  Portkey returned status ${response.status}`);
        }
    } catch (error) {
        console.log(`❌ Cannot reach Portkey gateway: ${error.message}`);
        console.log('   This might be a network issue or the gateway might be down');
    }
    
    // Test 2: Test Azure OpenAI through Portkey with minimal headers
    console.log('\n🔄 Test 2: Azure OpenAI through Portkey (minimal config)...');
    
    const resourceName = azureEndpoint.split('//')[1]?.split('.')[0];
    console.log(`Extracted resource name: ${resourceName}`);
    
    const url = `${portkeyBaseUrl}/v1/chat/completions`;
    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${openaiApiKey}`,
        'x-portkey-provider': 'azure-openai',
        'x-portkey-azure-resource-name': resourceName,
        'x-portkey-azure-deployment-id': process.env.AZURE_OPENAI_DEPLOYMENT || process.env.OPENAI_MODEL_ID || 'gpt-4o',
        'x-portkey-azure-api-version': azureApiVersion,
    };
    
    const data = {
        messages: [
            { role: 'user', content: 'Say "Portkey test successful!" and nothing else.' }
        ],
        max_tokens: 20,
        temperature: 0
    };
    
    try {
        console.log(`URL: ${url}`);
        console.log('Headers:', Object.keys(headers).map(key => 
            key.includes('Authorization') ? `${key}: Bearer ${openaiApiKey.substring(0, 10)}...` : `${key}: ${headers[key]}`
        ).join('\n         '));
        
        const response = await axios.post(url, data, { 
            headers,
            timeout: 30000,
            validateStatus: () => true
        });
        
        console.log(`Status Code: ${response.status}`);
        
        if (response.status === 200) {
            console.log('✅ Portkey Azure OpenAI integration working!');
            if (response.data.choices && response.data.choices.length > 0) {
                const message = response.data.choices[0].message.content;
                console.log(`🤖 AI Response: "${message}"`);
            }
            console.log('📊 Usage:', response.data.usage);
            return true;
        } else {
            console.log(`❌ Portkey request failed with status ${response.status}`);
            console.log('Response:', JSON.stringify(response.data, null, 2));
            
            // Provide specific error guidance
            if (response.status === 404) {
                console.log('\n💡 404 Error - Possible causes:');
                console.log('   - Incorrect Portkey base URL');
                console.log('   - Wrong API endpoint path');
                console.log('   - Portkey gateway not configured for Azure OpenAI');
            } else if (response.status === 401) {
                console.log('\n💡 401 Error - Authentication issue:');
                console.log('   - Check your Azure OpenAI API key');
                console.log('   - Verify the key has access to the specified deployment');
            } else if (response.status === 400) {
                console.log('\n💡 400 Error - Request issue:');
                console.log('   - Check deployment name matches your Azure resource');
                console.log('   - Verify API version is supported');
            }
            
            return false;
        }
        
    } catch (error) {
        console.log(`❌ Request failed: ${error.message}`);
        if (error.code === 'ECONNREFUSED') {
            console.log('   Connection refused - Portkey gateway might be down');
        } else if (error.code === 'ETIMEDOUT') {
            console.log('   Request timed out - Network or gateway issue');
        }
        return false;
    }
}

async function testDirectAzureOpenAI() {
    console.log('\n🔄 Test 3: Direct Azure OpenAI (for comparison)...');
    
    const openaiApiKey = process.env.OPENAI_API_KEY;
    const azureEndpoint = process.env.AZURE_OPENAI_ENDPOINT;
    const azureApiVersion = process.env.AZURE_OPENAI_API_VERSION;
    const deployment = process.env.AZURE_OPENAI_DEPLOYMENT || process.env.OPENAI_MODEL_ID || 'gpt-4o';
    
    const url = `${azureEndpoint}/openai/deployments/${deployment}/chat/completions?api-version=${azureApiVersion}`;
    
    const headers = {
        'Content-Type': 'application/json',
        'api-key': openaiApiKey
    };
    
    const data = {
        messages: [
            { role: 'user', content: 'Say "Direct Azure test successful!" and nothing else.' }
        ],
        max_tokens: 20,
        temperature: 0
    };
    
    try {
        const response = await axios.post(url, data, { 
            headers,
            timeout: 30000,
            validateStatus: () => true
        });
        
        console.log(`Status Code: ${response.status}`);
        
        if (response.status === 200) {
            console.log('✅ Direct Azure OpenAI working!');
            if (response.data.choices && response.data.choices.length > 0) {
                const message = response.data.choices[0].message.content;
                console.log(`🤖 AI Response: "${message}"`);
            }
            return true;
        } else {
            console.log(`❌ Direct Azure OpenAI failed with status ${response.status}`);
            console.log('Response:', JSON.stringify(response.data, null, 2));
            return false;
        }
        
    } catch (error) {
        console.log(`❌ Direct Azure OpenAI request failed: ${error.message}`);
        return false;
    }
}

async function main() {
    console.log('🚀 Portkey Connectivity Diagnostic Tool\n');
    
    const portkeySuccess = await testPortkeyConnectivity();
    const directSuccess = await testDirectAzureOpenAI();
    
    console.log('\n' + '='.repeat(60));
    console.log('📋 DIAGNOSTIC SUMMARY');
    console.log('='.repeat(60));
    console.log(`Direct Azure OpenAI: ${directSuccess ? '✅ Working' : '❌ Failed'}`);
    console.log(`Portkey Integration: ${portkeySuccess ? '✅ Working' : '❌ Failed'}`);
    
    if (directSuccess && !portkeySuccess) {
        console.log('\n💡 RECOMMENDATION:');
        console.log('   Direct Azure OpenAI is working, but Portkey integration has issues.');
        console.log('   Your application will use the fallback mode (direct Azure OpenAI).');
        console.log('   To fix Portkey integration:');
        console.log('   1. Check if the Portkey gateway URL is accessible');
        console.log('   2. Verify Portkey is configured for Azure OpenAI');
        console.log('   3. Check deployment names match your Azure resource');
    } else if (!directSuccess) {
        console.log('\n💡 RECOMMENDATION:');
        console.log('   Direct Azure OpenAI is not working. Check:');
        console.log('   1. Azure OpenAI API key is correct');
        console.log('   2. Azure OpenAI endpoint URL is correct');
        console.log('   3. Deployment name exists in your Azure resource');
        console.log('   4. API version is supported');
    } else if (portkeySuccess) {
        console.log('\n🎉 EXCELLENT:');
        console.log('   Both direct Azure OpenAI and Portkey integration are working!');
        console.log('   Your application will use Portkey with fallback capability.');
    }
    
    process.exit(portkeySuccess && directSuccess ? 0 : 1);
}

if (require.main === module) {
    main().catch(error => {
        console.error('❌ Diagnostic failed:', error);
        process.exit(1);
    });
}

module.exports = { testPortkeyConnectivity, testDirectAzureOpenAI };
