const prisma = require("../../../database/prisma/getPrismaClient");
const { filterDomains } = require("../filterDomains");

async function processUrlFiltering(lead) {
  try {
    console.log(`Processing lead ${lead.id}`);
    const { apiResponse: serpResponse = {} } = lead;
    let filteredDomains = [];
    if (lead.mode === "serp" || !lead.mode) {
      const organicResults = serpResponse?.results?.results?.organic;
      if (!organicResults || !Array.isArray(organicResults) || organicResults.length === 0) {
        console.log(`No valid organic results found for lead ${lead.id}. SERP Response:`, JSON.stringify(serpResponse));
        return await prisma.lead.update({
          where: { id: lead.id },
          data: {
            status: "srp_failed",
          },
        });
      }
      filteredDomains = filterDomains(organicResults);

      console.log(`Filtered domains: ${filteredDomains.length}`);
      let keywords = [];
      if (lead.businessName && lead.businessName.length > 0) {
        keywords.push(lead.businessName);
      }
      if (lead.sellerName && lead.sellerName.length > 0) {
        keywords.push(lead.sellerName);
      }
      if (lead.address && lead.address.length > 0) {
        keywords.push(lead.address);
      }
      await Promise.all(
        filteredDomains.map((domain, index) => {
          console.log(`Processing domain ${domain.originalUrl}`);
          // console.log({ domain });

          return prisma.leadUrl
            .create({
              data: {
                leadId: lead.id,
                url: domain.originalUrl,
                domain: domain.domain,
                googlePosition: domain.position,
                filterPosition: index + 1,
                organicData: domain.organicData,
                keywords,
                useDomain: lead.useDomain,
              },
            })
            .catch((error) => {
              console.error(`Error creating leadUrl: ${error}`);
            });
        })
      );
    }

    await prisma.lead.update({
      where: { id: lead.id },
      data: {
        status: "scoring",
      },
    });
  } catch (error) {
    console.error(`Error processing lead ${lead.id}: ${error}`);
  }
}
module.exports = { processUrlFiltering };
