const { processSerpRequests } = require("./serpWorker/serpWorker");
const { processSyncRequests } = require("./serpWorker/serpSyncWorker");

async function runSerpWorkers() {
  console.log("Starting SERP workers...");

  // Run both workers every 10 seconds
  setInterval(async () => {
    try {
      // Run async worker
      const syncResults = await processSyncRequests();
      const asyncResults = await processSerpRequests();
      if (asyncResults) {
        console.log("Async SERP batch processed:", asyncResults);
      }

      // Run sync worker
      if (syncResults) {
        console.log("Sync SERP batch processed:", syncResults);
      }
    } catch (error) {
      console.error("SERP processing error:", error);
    }
  }, 10000); // 10 seconds
}

// Export for use in main application
module.exports = {
  runSerpWorkers,
};
