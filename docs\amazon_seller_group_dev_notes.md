# Amazon Seller Group Integration - Developer Notes

## Overview

This document provides a concise summary of the Amazon Seller Group integration in SellerBot (Issue #163), focusing on the schema changes to handle sellers across multiple marketplaces and the enhancement to update all Amazon sellers in the same group when updating a prospect.

## Background

The integration addresses two key problems:

1. **Schema Redesign**: Updating the schema to better align with SmartScout data and support improved filtering
2. **Cross-Marketplace Seller Management**: Solving the problem of a single seller existing across multiple countries/marketplaces

A seller may have:

- The same seller ID across different marketplaces (e.g., US, CA)
- Different seller IDs but represent the same company
- Different URLs across regions due to regulatory differences

## Key Changes

1. **New Database Schema**:
   - Created dedicated `AmazonSeller` table with proper data types (Float, Int, Boolean)
   - Added `SellerGroup` table to manage seller relationships across marketplaces
   - Modified `Prospect` table to link to seller groups instead of individual companies

2. **Cross-Marketplace Seller Management**:
   - Sellers with the same ID across different marketplaces are now grouped
   - Composite key validation (`amazon_seller_id` + `marketplace`) ensures proper identification

3. **Group-based Data Propagation**:
   - When a prospect is updated, all Amazon sellers in the same group receive the updates
   - Lookup sources are automatically propagated and deduplicated across all entities in a group
   - Changes to one seller can affect related sellers in different marketplaces


## Data Model

- **AmazonSeller**: Uniquely identified by `amazon_seller_id` + `marketplace`

  - Replaces the functionality of the `Company` table
  - Uses appropriate data types (Float, Int, Boolean) for numeric fields
  - Each seller in a specific marketplace can have only one "Final Correct" domain

- **SellerGroup**: Groups related sellers together

  - Sellers with the same domain and other identifiers
  - Sellers with the same ID across different marketplaces belong to the same group
  - Different seller IDs representing the same company are grouped together

- **Prospect**: Now associated with a seller group instead of directly with a company
  - Allows prospects to be linked to multiple sellers across different marketplaces
  - When updated, changes propagate to all sellers in the group

## Key Constraints

### Application-Level Constraints (from Issue #163)

1. **Marketplace Domain Uniqueness**: For a specific marketplace, only one seller can have the same "Final Correct" domain

   - Example: Two different seller IDs cannot both have nike.com as their domain in the US marketplace
   - However, the same domain can exist across different marketplaces (US, CA, UK, etc.)

2. **Domain Status**: Non "Final Correct" domains will always be empty

   - Only domains with "Final Correct" status are considered for grouping

3. **Prospect Relationship**: Prospects are now linked to seller groups instead of individual sellers
   - This enables prospects to be associated with multiple sellers across marketplaces

### Amazon Seller Insert/Update

1. **Unique Identifier**: Each seller must have a unique `amazon_seller_id` + `marketplace` combination
2. **Domain Rules**: Sellers with the same domain and "Final Correct" website status belong to the same group
3. **Lookup Sources**: When a seller's lookup source is updated, all sellers in the same group should be updated

### Prospect Update

1. **Identifier Requirements**: At least one of `person_linkedin`, `phone`, or `email` must be provided
2. **Group Association**: Prospects are associated with seller groups based on their company's domain
3. **Source Propagation**: When a prospect is updated, its source is propagated to all sellers in its group

### Company Update (Legacy)

1. **Backward Compatibility**: Company operations are redirected to Amazon Seller operations
2. **Required Fields**: `amazon_seller_id` and `smartscout_country` (mapped to `marketplace`) are required

## API Endpoints

### Amazon Seller Endpoints

- **Insert Amazon Seller**

  ```
  POST /api/lead/insert/amazon_seller
  ```

  - Creates a new Amazon Seller record
  - Requires `amazon_seller_id` and `marketplace`
  <!-- - Automatically associates with a seller group if domain has "Final Correct" status -->
  <!-- - Returns the created seller with its group association -->

- **Update Amazon Seller**

  ```
  POST /api/lead/update/amazon_seller
  ```

  - Updates an existing Amazon Seller record
  - Requires `amazon_seller_id` and `marketplace` to identify the seller
  - Updates group association if domain or website status changes
  - Propagates lookup sources to other sellers in the same group

<!-- - **Get Amazon Seller**

  ```
  GET /api/lead/amazon_seller/:id
  ```

  - Retrieves an Amazon Seller by its ID
  - Includes seller group information

- **Search Amazon Sellers**
  ```
  GET /api/lead/amazon_seller/search?domain=example.com&marketplace=US
  ```
  - Searches for Amazon Sellers based on various criteria
  - Supports filtering by domain, marketplace, amazon_seller_id, etc.

### Seller Group Endpoints

- **Get Seller Group**

  ```
  GET /api/lead/seller_group/:id
  ```

  - Retrieves a Seller Group by its ID
  - Includes all sellers and prospects in the group

- **Get Amazon Sellers by Group**
  ```
  GET /api/lead/amazon_seller/group/:groupId
  ```
  - Retrieves all Amazon Sellers in a specific group
  - Useful for finding related sellers across marketplaces

### Prospect Endpoints

- **Update Prospect**
  ```
  POST /api/lead/update/prospect
  ```
  - Updates a prospect and propagates changes to related sellers
  - Requires at least one identifier (person_linkedin, phone, or email)
  - Updates all Amazon sellers in the same group with the prospect's lookup source

### CSV Upload Endpoint

- **Upload CSV**
  ```
  POST /api/lead/upload/csv
  ```
  - Uploads and processes a CSV file for bulk operations
  - Supports various data types (amazon_seller, prospect, company, matching)
  - Handles group associations and lookup source propagation -->

### Key Differences from Previous Implementation

1. **Group Propagation**:

   - Previously: Updates were isolated to the specific entity being updated
   - Now: Updates to a prospect propagate to all Amazon sellers in the same group

2. **Automatic Group Association**:

   - Previously: Seller groups had to be manually assigned
   - Now: Sellers with the same domain and "Final Correct" status are automatically grouped

3. **Lookup Source Handling**:

   - Previously: Lookup sources were entity-specific
   - Now: Lookup sources are propagated across related entities in the same group

4. **Seller Group ID**:
   - New parameter that can be used to manually assign an entity to a specific group
   - Typically handled automatically based on domain relationships

## Detailed API Parameters

### Amazon Seller Insert/Update Parameters

```json
{
  // Required Parameters
  "amazon_seller_id": "ABCD1234", // Required - Seller ID on Amazon
  "marketplace": "US", // Required - Country/marketplace code (e.g., US, UK, CA)

  // Seller Information
  "name": "Example Seller", // Optional - Seller name
  "business_name": "Example Business", // Optional - Business name

  // Website Information
  "website": "https://example.com", // Optional - Seller website
  "domain": "example.com", // Optional - Extracted automatically from website
  "website_status": "Final Correct", // Optional - Status of website verification

  // Sales and Performance Metrics
  "estimate_sales": 100000.5, // Optional - Estimated sales (Float)
  "avg_price": 25.99, // Optional - Average price (Float)
  "percent_fba": 75.5, // Optional - Percentage of FBA sales (Float)
  "number_reviews_lifetime": 5000, // Optional - Lifetime review count (Integer)
  "number_reviews_30days": 150, // Optional - 30-day review count (Integer)
  "number_winning_brands": 3, // Optional - Number of winning brands (Integer)
  "number_asins": 50, // Optional - Number of ASINs (Integer)
  "number_top_asins": 10, // Optional - Number of top ASINs (Integer)
  "number_brands_1000": 2, // Optional - Number of brands in top 1000 (Integer)
  "mom_growth": 12.5, // Optional - Month-over-month growth percentage (Float)
  "mom_growth_count": 5, // Optional - Month-over-month growth count (Integer)

  // Category Information
  "primary_category": "Electronics", // Optional - Primary category
  "primary_sub_category": "Accessories", // Optional - Primary sub-category

  // Address Information
  "street": "123 Main St", // Optional - Street address
  "city": "Seattle", // Optional - City
  "adr_state": "WA", // Optional - State
  "adr_country": "USA", // Optional - Country
  "adr_zip_code": "98101", // Optional - ZIP/Postal code

  // Status Information
  "is_suspended": false, // Optional - Whether seller is suspended (Boolean)
  "last_suspended_date": "2023-01-01", // Optional - Date of last suspension (DateTime)
  "started_selling_date": "2020-05-15", // Optional - Date started selling (DateTime)

  // Source and Group Information
  "lookup_source": "Apollo", // Optional - Source of the data
  "seller_group_id": 123, // Optional - Manual group assignment
  "seller_url": "https://amazon.com/shops/ABCD1234" // Optional - URL to seller's page on Amazon
}
```

## Example Scenarios (Based on Issue #163)

### Scenario 1: Same Seller ID Across Marketplaces

```
Seller ID, Marketplace, Domain
ABC1, US, nike.com
ABC1, CA, nike.com
```

- Both sellers have the same ID but operate in different marketplaces
- They are automatically grouped together in the same seller group
- When a prospect linked to this group is updated, both sellers receive the update

### Scenario 2: Different Seller IDs with Same Domain

```
Seller ID, Marketplace, Domain
ABC1, US, nike.com
XYZ1, UK, nike.com
```

- Different seller IDs but same domain across different marketplaces
- They are grouped together based on the domain
- Updates to prospects propagate to all sellers in the group

### Scenario 3: Invalid Configuration (Not Allowed)

```
Seller ID, Marketplace, Domain
ABC1, US, nike.com
ABC2, US, nike.com  (Not allowed - violates constraint #1)
```

- This is not allowed because two different seller IDs cannot have the same "Final Correct" domain in the same marketplace

## Data Type Handling

The AmazonSeller table uses appropriate data types for numeric and boolean fields, with automatic conversion during data import:

### Numeric Field Conversions
- **Float Fields**: `estimate_sales`, `avg_price`, `percent_fba`, `mom_growth`
  - String values are converted using `parseFloat()`
  - Invalid values are set to `null`

### Integer Field Conversions
- **Integer Fields**: `number_reviews_lifetime`, `number_reviews_30days`, `number_winning_brands`, `number_asins`, `number_top_asins`, `number_brands_1000`, `mom_growth_count`
  - String values are converted using `parseInt()`
  - Invalid values are set to `null`

### Boolean Field Conversions
- **Boolean Fields**: `is_suspended`
  - String values "true", "1" are converted to `true`
  - String values "false", "0" are converted to `false`
  - Other values are set to `null`

### Date Field Handling
- **Date Fields**: `last_suspended_date`, `started_selling_date`
  - Stored as DateTime in the database
  - String date formats are automatically parsed


## Validation Logic

### Domain Uniqueness Validation
- Domain uniqueness is enforced within the context of a marketplace
- Validation uses composite keys in the format `${domain}-${marketplace}`
- This allows the same domain to exist across different marketplaces
- Example: nike.com can exist in both US and CA marketplaces, but not twice in the US marketplace

### Required Field Validation
- For Amazon Sellers: `amazon_seller_id` and `marketplace` are required
- Website Status is required when a website is provided
- Whenever the Website status is anything other than Final Correct + Website Given, raise an error

### Cross-Record Validation
- The system checks for domain conflicts both within the current batch and against existing database records
- When a domain with "Final Correct" status is found in multiple marketplaces, this information is logged to assist with seller grouping
