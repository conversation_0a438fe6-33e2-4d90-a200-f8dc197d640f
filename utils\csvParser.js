const prisma = require("../database/prisma/getPrismaClient");
const csvtojson = require("csvtojson");

async function processCSV(csvFilePath, userId, job) {
  try {
    // Async / await usage
    const jsonArray = await csvtojson().fromFile(csvFilePath);

    for (let i = 0; i < jsonArray.length; i++) {
      console.log("Processing row:", i + 1);
      await processJsonData(jsonArray[i], userId, job);
    }

    console.log(
      "++++++++++++CSV Parsing and Insertion complete++++++++++++++++++",
    );
  } catch (e) {
    console.log("Error in processCSV", e);
  }
}

async function processJsonData(row, userId, job) {
  console.log("Row data:", row);

  const sellerUrl = row?.["Seller URL"] || row?.["seller_url"];
  const sellerName = row?.["Name"] || row?.["name"];
  const sellerId = row?.["Amazon Seller ID"] || row?.["amazon_seller_id"];
  // console.log({ sellerUrl, sellerName, sellerId });

  const inputData = row;
  try {
    let seller = null;
    if (sellerName && sellerUrl && sellerId) {
      seller = await prisma.seller.findFirst({
        where: {
          name: sellerName,
        },
      });
    }

    if (!seller) {
      seller = await prisma.seller.create({
        data: {
          name: sellerName,
          status: "pending",
          userId,
          jobId: job.id,
        },
      });
    }

    await prisma.outputData.create({
      data: {
        sellerName,
        sellerUrl,
        sellerId: seller.id,
        inputStatus: "pending",
        jobId: job.id,
        scraped_html: "",
        inputData,
      },
    });
  } catch (e) {
    console.log("Error in inserting seller data", e);
  }
}

module.exports = { processCSV, processJsonData };
