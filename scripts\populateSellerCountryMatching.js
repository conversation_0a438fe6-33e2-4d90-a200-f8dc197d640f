const newrelic = require("newrelic");
const { PrismaClient } = require("@prisma/client");
const { func } = require("joi");
const prisma = new PrismaClient();

async function populateSellerCountryMatching() {
  try {
    const existingSellerIds = await prisma.sellerCountryMatching.findMany({
      select: {
        amazon_seller_id: true,
      },
      distinct: ["amazon_seller_id"],
    });

    const existingSellerIdSet = new Set(
      existingSellerIds.map((s) => s.amazon_seller_id),
    );

    const allSellers = await prisma.company.findMany({
      select: {
        amazon_seller_id: true,
      },
      distinct: ["amazon_seller_id"],
    });

    const missingSellerIds = allSellers
      .map((s) => s.amazon_seller_id)
      .filter((id) => !existingSellerIdSet.has(id) && id !== "");

    console.log(
      `Found ${missingSellerIds.length} sellers missing from SellerCountryMatching`,
    );

    const totalSellers = missingSellerIds.length;
    let successCount = 0;

    // Using for...of loop instead of Promise.all
    for (const amazon_seller_id of missingSellerIds) {
      try {
        successCount++;
        console.log(
          `Creating entry for ${amazon_seller_id} (${successCount}/${totalSellers})`,
        );

        const result = await prisma.sellerCountryMatching.create({
          data: {
            amazon_seller_id: amazon_seller_id,
            smartscout_country: "US",
            seller_url: `https://www.amazon.com/sp?seller=${amazon_seller_id}`,
          },
        });

        console.log(result);
      } catch (error) {
        console.error(`Error creating entry for ${amazon_seller_id}:`, error);
        continue;
      }
    }

    console.log(
      `Successfully created ${successCount} SellerCountryMatching entries`,
    );
  } catch (error) {
    console.error("Error:", error);
  } finally {
    await prisma.$disconnect();
  }
}
//scp jeff@**************:'/home/<USER>/hourly_data_backups/*' /Users/<USER>/personal/SellerBot/backups
// psql -h localhost -p 5432 -U new_user -d sellers-db < /Users/<USER>/personal/SellerBot/backups/sellers-db_202502050644.sql

// Uncomment to run

populateSellerCountryMatching();
