const fs = require("fs");
const { getChatGPTResponse } = require("./request");

function generatePrompt(json) {
  const template = fs.readFileSync("scrapeGPT/gptPrompt.md", "utf8");
  const markdown = template.replace("{{json}}", JSON.stringify(json));
  return markdown;
}

async function main(json) {
  try {
    const prompt = generatePrompt(json);
    // console.log(prompt);
    const response = await getChatGPTResponse(prompt);
    console.log(response);
    // TODO adding it to DB
    return response;
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error in main:", error);
  }
}

// main(fs.readFileSync('scrapeGPT/data.json', 'utf8'))
exports.main = main;
