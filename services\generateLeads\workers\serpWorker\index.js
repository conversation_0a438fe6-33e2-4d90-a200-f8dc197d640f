const newrelic = require("newrelic");
const { processSerpRequests } = require("./serpWorker");
const processSyncRequests = require("./serpSyncWorker");

async function runWorker() {
  return newrelic.startBackgroundTransaction('processSellerBotTasks', 'SellerBotSerpWorker', async function() {
    const transaction = newrelic.getTransaction();
    
  try {
    // Process async SERP requests
    processSerpRequests();

    // Process sync SERP requests
    processSyncRequests();
  } catch (error) {
    console.error("Error in SERP worker:", error);
  } finally {
    transaction.end();
  }
}
  )}
  // Start the worker loop
function startWorker() {
  console.log("Starting SERP worker...");

  // Run immediately on startup
  runWorker();

  // Then run every 30 seconds
  setInterval(runWorker, 30000);
}

// Start the worker if this file is run directly
if (require.main === module) {
  startWorker();
}

module.exports = {
  startWorker,
};
