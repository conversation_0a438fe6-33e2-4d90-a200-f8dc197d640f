#!/bin/bash

# Load environment variables from .env file
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
fi

echo "=== Azure OpenAI Portkey Curl Test ==="
echo "Portkey Base URL: $PORTKEY_BASE_URL"
echo "Azure Endpoint: $AZURE_OPENAI_ENDPOINT"
echo "Azure API Version: $AZURE_OPENAI_API_VERSION"
echo "OpenAI API Key: ${OPENAI_API_KEY:0:20}..."
echo ""

# Extract resource name from Azure endpoint
RESOURCE_NAME=$(echo $AZURE_OPENAI_ENDPOINT | sed 's|https://||' | sed 's|\..*||')
echo "Extracted Resource Name: $RESOURCE_NAME"
echo ""

# Test with curl
echo "🔄 Testing Azure OpenAI through Portkey..."
curl -X POST \
    "$PORTKEY_BASE_URL/v1/chat/completions" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $OPENAI_API_KEY" \
    -H "x-portkey-provider: azure-openai" \
    -H "x-portkey-azure-resource-name: $RESOURCE_NAME" \
    -H "x-portkey-azure-deployment-id: gpt-4o-mini" \
    -H "x-portkey-azure-api-version: $AZURE_OPENAI_API_VERSION" \
    -H "x-portkey-azure-model-name: gpt-4o-mini" \
    -d '{
        "messages": [
            {"role": "user", "content": "Hello, how are you? This is a test from curl."}
        ],
        "model": "gpt-4o-mini",
        "max_tokens": 100,
        "temperature": 0.7
    }' \
    -w "\n\nHTTP Status: %{http_code}\n" \
    -s

echo ""
echo "=== Direct Azure OpenAI Test ==="
echo "🔄 Testing direct Azure OpenAI connection..."

curl -X POST \
    "$AZURE_OPENAI_ENDPOINT/openai/deployments/gpt-4o-mini/chat/completions?api-version=$AZURE_OPENAI_API_VERSION" \
    -H "Content-Type: application/json" \
    -H "api-key: $OPENAI_API_KEY" \
    -d '{
        "messages": [
            {"role": "user", "content": "Hello, this is a direct Azure OpenAI test."}
        ],
        "max_tokens": 100,
        "temperature": 0.7
    }' \
    -w "\n\nHTTP Status: %{http_code}\n" \
    -s

echo ""
echo "Test completed!"
