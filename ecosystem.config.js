module.exports = {
  apps: [
    {
      name: "serpSyncWorker",
      script: "./services/generateLeads/workers/serpWorker/index.js",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "2G",
      env: {
        NODE_ENV: "production",
      },
      dependency: ["prisma-generate"],
    },
    {
      name: "mainWorker1",
      script: "./services/generateLeads/workers/mainWorker.js",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "3G",
      env: {
        NODE_ENV: "production",
      },
      dependency: ["prisma-generate"],
    },
    {
      name: "mainWorker2",
      script: "./services/generateLeads/workers/mainWorker.js",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "3G",
      env: {
        NODE_ENV: "production",
      },
      dependency: ["prisma-generate"],
    },
  ],
};
