const { api, sheets } = require('../config/index');
const axios = require('axios');
const fs = require('fs');
const FormData = require('form-data');
const createCsvWriter = require('csv-writer').createObjectCsvWriter;
const { sanitizeObject } = require('../utils/sanitizer');

class APIService {
  constructor() {
    this.baseURL = api.BASE_URL;
    this.client = axios.create({ 
      baseURL: this.baseURL,
      headers: {
        'Authorization': `Bearer ${api.BEARER_TOKEN}`
      }
    });
  }

  async uploadData(endpoint, data, skipErrorRows = false, useNewEndpoints = false) {
    try {
      // Determine which endpoint collection to use based on the useNewEndpoints flag
      const endpointCollection = useNewEndpoints ? api.UPDATED_ENDPOINTS : api.ENDPOINTS;
      const fullEndpoint = endpointCollection[endpoint];
      if (!fullEndpoint) {
        throw new Error(`Unknown API endpoint: ${endpoint}${useNewEndpoints ? ' in UPDATED_ENDPOINTS' : ''}`);
      }

      // Sanitize all string values in each row
      const sanitizedData = Array.isArray(data) ? data.map(row => sanitizeObject(row)) : [sanitizeObject(data)];

      // Write to CSV
      const csvWriter = createCsvWriter({
        path: sheets.OUTPUT_CSV_FILE,
        header: Object.keys(sanitizedData[0] || {}).map((key) => ({
          id: key,
          title: key,
        })),
        alwaysQuote: true,
      });

      await csvWriter.writeRecords(sanitizedData);
      console.log(`✅ Transformed CSV file saved: ${sheets.OUTPUT_CSV_FILE}`);

      // Create FormData and append CSV
      const formData = new FormData();
      formData.append("csvFile", fs.createReadStream(sheets.OUTPUT_CSV_FILE));

      const config = {
        method: "post",
        maxBodyLength: Infinity,
        url: `${this.baseURL}${fullEndpoint}${skipErrorRows ? '?skiperrorrows=true' : ''}`,
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${api.BEARER_TOKEN}`
        },
        data: formData,
      };

      console.log("📤 Uploading transformed CSV to API...");
      console.log(config.url)
      console.log(config);
      const response = await axios.request(config);

      if (response.status === 200) {
        console.log("✅ Transformed CSV uploaded successfully.");
        return { success: true, data: response.data };
      }

      console.log("❌ Error uploading transformed CSV. Saving response to errors CSV.");
      fs.writeFileSync(sheets.ERRORS_CSV_FILE, response.data);
      return {
        success: false,
        validationFile: sheets.ERRORS_CSV_FILE,
        data: response.data
      };

    } catch (error) {
      if (error.response) {
        if (error.response.status === 403) {
          console.log("ℹ️ Validation response received:");
          const errorCsvContent = error.response.data;
          
          fs.writeFileSync(sheets.ERRORS_CSV_FILE, errorCsvContent);
          console.log(`✅ Validation results saved to ${sheets.ERRORS_CSV_FILE}`);
          
          return {
            success: false,
            validationFile: sheets.ERRORS_CSV_FILE,
            data: error.response.data
          };
        }
        
        console.error(`❌ Upload failed with status ${error.response.status}`);
        console.error("Response:", error.response.data);
        throw error;
      }
      
      console.error(`Error uploading data to ${endpoint}:`, error.message);
      throw error;
    }
  }

  async uploadTransformedData(apiEndpoint, data, skipErrorRows = false, endpointVersion = 'old') {
    if (!data || data.length === 0) {
      console.error("❌ No data to transform");
      return { success: false, error: "No data to transform" };
    }

    try {
      if (endpointVersion === 'both') {
        console.log(`Using BOTH endpoint versions for ${apiEndpoint}`);
        return await this.uploadToBothEndpoints(apiEndpoint, data, skipErrorRows);
      } else {
        const useNewEndpoints = endpointVersion === 'new';
        console.log(`Using ${useNewEndpoints ? 'NEW' : 'OLD'} endpoint version for ${apiEndpoint}`);
        const response = await this.uploadData(apiEndpoint, data, skipErrorRows, useNewEndpoints);
        return response;
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async uploadToBothEndpoints(apiEndpoint, data, skipErrorRows = false) {
    console.log(`\n🔄 Starting dual API upload for ${apiEndpoint}...`);
    
    const results = {
      success: false,
      oldEndpoint: { success: false, error: null, data: null, validationFile: null },
      newEndpoint: { success: false, error: null, data: null, validationFile: null },
      summary: '',
      validationFile: null // This will be set if either endpoint has validation errors
    };

    // First, upload to OLD endpoint
    console.log(`\n📤 Uploading to OLD endpoint...`);
    try {
      const oldResponse = await this.uploadData(apiEndpoint, data, skipErrorRows, false);
      results.oldEndpoint = {
        success: oldResponse.success,
        error: oldResponse.error || null,
        data: oldResponse.data || null,
        validationFile: oldResponse.validationFile || null
      };
      console.log(`✅ OLD endpoint result: ${oldResponse.success ? 'SUCCESS' : 'FAILED'}`);
      if (!oldResponse.success && oldResponse.error) {
        console.log(`❌ OLD endpoint error: ${oldResponse.error}`);
      }
      // If OLD endpoint has validation errors, use its validation file
      if (oldResponse.validationFile) {
        results.validationFile = oldResponse.validationFile;
        console.log(`📋 OLD endpoint validation file: ${oldResponse.validationFile}`);
      }
    } catch (error) {
      results.oldEndpoint = {
        success: false,
        error: error.message,
        data: null,
        validationFile: null
      };
      console.log(`❌ OLD endpoint failed: ${error.message}`);
    }

    // Add 10-second pause between old and new API calls to prevent connection issues
    console.log(`\n⏳ Waiting 10 seconds before uploading to NEW endpoint...`);
    await new Promise(resolve => setTimeout(resolve, 10000));
    console.log(`✅ Wait complete. Proceeding with NEW endpoint upload.`);

    // Then, upload to NEW endpoint
    console.log(`\n📤 Uploading to NEW endpoint...`);
    try {
      const newResponse = await this.uploadData(apiEndpoint, data, skipErrorRows, true);
      results.newEndpoint = {
        success: newResponse.success,
        error: newResponse.error || null,
        data: newResponse.data || null,
        validationFile: newResponse.validationFile || null
      };
      console.log(`✅ NEW endpoint result: ${newResponse.success ? 'SUCCESS' : 'FAILED'}`);
      if (!newResponse.success && newResponse.error) {
        console.log(`❌ NEW endpoint error: ${newResponse.error}`);
      }
      // If NEW endpoint has validation errors and we don't already have a validation file, use the NEW one
      // Priority: Use OLD endpoint validation file if available, otherwise use NEW endpoint validation file
      if (newResponse.validationFile && !results.validationFile) {
        results.validationFile = newResponse.validationFile;
        console.log(`📋 NEW endpoint validation file: ${newResponse.validationFile}`);
      }
    } catch (error) {
      results.newEndpoint = {
        success: false,
        error: error.message,
        data: null,
        validationFile: null
      };
      console.log(`❌ NEW endpoint failed: ${error.message}`);
    }

    // Generate summary
    const oldStatus = results.oldEndpoint.success ? '✅ SUCCESS' : '❌ FAILED';
    const newStatus = results.newEndpoint.success ? '✅ SUCCESS' : '❌ FAILED';
    
    results.summary = `\n========== DUAL API UPLOAD SUMMARY ==========\n` +
                     `OLD Endpoint: ${oldStatus}\n` +
                     `NEW Endpoint: ${newStatus}\n` +
                     `Overall: ${results.oldEndpoint.success || results.newEndpoint.success ? 'At least one succeeded' : 'Both failed'}\n`;
    
    // Add validation file info to summary if present
    if (results.validationFile) {
      results.summary += `Validation Errors: Available in ${results.validationFile}\n`;
    }
    
    results.summary += `=============================================`;
    
    console.log(results.summary);
    
    // Consider it successful if at least one endpoint succeeded
    results.success = results.oldEndpoint.success || results.newEndpoint.success;
    
    return results;
  }
}

module.exports = new APIService();
