require('dotenv').config();
const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();

/**
 * Function to print all emails without campaignId
 */
async function printEmailsWithoutCampaignId() {
  try {
    console.log("🔍 Finding emails without campaignId...");
    
    const emailsWithoutCampaignId = await prisma.email.findMany({
      where: {
        OR: [
          { campaingId: null },
          { campaingId: undefined }
        ]
      },
      select: {
        id: true,
        matchId: true,
        leadId: true,
        messageId: true,
        subject: true,
        type: true,
        toEmailID: true,
        fromEmailID: true,
        time: true,
        campaingId: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`📧 Found ${emailsWithoutCampaignId.length} emails without campaignId`);
    
    if (emailsWithoutCampaignId.length > 0) {
      console.log("\n📋 Sample emails without campaignId:");
      emailsWithoutCampaignId.slice(0, 10).forEach((email, index) => {
        console.log(`${index + 1}. ID: ${email.id}, LeadID: ${email.leadId}, Subject: ${email.subject}, Time: ${email.time}`);
      });
      
      if (emailsWithoutCampaignId.length > 10) {
        console.log(`   ... and ${emailsWithoutCampaignId.length - 10} more emails`);
      }
    }

    return emailsWithoutCampaignId;
  } catch (error) {
    console.error("❌ Error finding emails without campaignId:", error);
    throw error;
  }
}

/**
 * Function to get campaignId from SmartLead_Lead table for a given leadId
 */
async function getCampaignIdFromLead(leadId) {
  try {
    const lead = await prisma.smartLead_Lead.findUnique({
      where: { id: leadId },
      select: {
        id: true,
        campaignId: true,
        campaign: {
          select: {
            campaignId: true
          }
        }
      }
    });

    if (!lead) {
      console.warn(`⚠️  Lead with ID ${leadId} not found`);
      return null;
    }
    // console.log(lead.campaign.campaignId);
    // Return the campaignId from the Campaign table (not the foreign key)
    return lead.campaign?.campaignId || null;
  } catch (error) {
    console.error(`❌ Error getting campaignId for lead ${leadId}:`, error);
    return null;
  }
}

// getCampaignIdFromLead(132670);

/**
 * Function to update emails with campaignId from SmartLead_Lead table
 */
async function updateEmailsWithCampaignId() {
  try {
    console.log("🔄 Starting campaignId update process...");
    
    // First, get all emails without campaignId
    const emailsWithoutCampaignId = await printEmailsWithoutCampaignId();
    
    if (emailsWithoutCampaignId.length === 0) {
      console.log("✅ All emails already have campaignId");
      return;
    }

    let updatedCount = 0;
    let failedCount = 0;
    let skippedCount = 0;

    console.log(`\n🔄 Updating ${emailsWithoutCampaignId.length} emails...`);

    for (const email of emailsWithoutCampaignId) {
      try {
        // Get campaignId from the lead
        const campaignId = await getCampaignIdFromLead(email.leadId);
        
        if (!campaignId) {
          console.warn(`⚠️  Skipping email ${email.id} - no campaignId found for lead ${email.leadId}`);
          skippedCount++;
          continue;
        }

        // Update the email with the campaignId
        await prisma.email.update({
          where: { id: email.id },
          data: { 
            campaingId: campaignId 
          }
        });

        updatedCount++;
        
        if (updatedCount % 100 === 0) {
          console.log(`✅ Updated ${updatedCount} emails so far...`);
        }

      } catch (error) {
        console.error(`❌ Error updating email ${email.id}:`, error);
        failedCount++;
      }
    }

    console.log(`\n📊 Update Summary:`);
    console.log(`✅ Successfully updated: ${updatedCount} emails`);
    console.log(`❌ Failed to update: ${failedCount} emails`);
    console.log(`⚠️  Skipped (no campaignId): ${skippedCount} emails`);

    // Verify the update
    const remainingEmailsWithoutCampaignId = await prisma.email.count({
      where: {
        OR: [
          { campaingId: null },
          { campaingId: undefined }
        ]
      }
    });

    console.log(`\n🔍 Verification: ${remainingEmailsWithoutCampaignId} emails still without campaignId`);

  } catch (error) {
    console.error("❌ Error in updateEmailsWithCampaignId:", error);
    throw error;
  }
}

/**
 * Function to get statistics about emails and their campaignId status
 */
async function getEmailCampaignIdStats() {
  try {
    console.log("📊 Getting email campaignId statistics...");
    
    const totalEmails = await prisma.email.count();
    const emailsWithCampaignId = await prisma.email.count({
      where: {
        NOT: {
          OR: [
            { campaingId: null },
            { campaingId: undefined }
          ]
        }
      }
    });
    const emailsWithoutCampaignId = await prisma.email.count({
      where: {
        OR: [
          { campaingId: null },
          { campaingId: undefined }
        ]
      }
    });

    console.log(`📊 Email CampaignId Statistics:`);
    console.log(`📧 Total emails: ${totalEmails}`);
    console.log(`✅ Emails with campaignId: ${emailsWithCampaignId}`);
    console.log(`❌ Emails without campaignId: ${emailsWithoutCampaignId}`);
    console.log(`📈 Completion rate: ${((emailsWithCampaignId / totalEmails) * 100).toFixed(2)}%`);

    return {
      total: totalEmails,
      withCampaignId: emailsWithCampaignId,
      withoutCampaignId: emailsWithoutCampaignId,
      completionRate: (emailsWithCampaignId / totalEmails) * 100
    };
  } catch (error) {
    console.error("❌ Error getting email statistics:", error);
    throw error;
  }
}

/**
 * Main function to run the seeder
 */
async function run() {
  try {
    console.log("🚀 Starting Email CampaignId Seeder...");
    
    // Get initial statistics
    await getEmailCampaignIdStats();
    
    // Print emails without campaignId
    await printEmailsWithoutCampaignId();
    
    // Update emails with campaignId
    await updateEmailsWithCampaignId();
    
    // Get final statistics
    await getEmailCampaignIdStats();
    
    console.log("🎉 Email CampaignId Seeder completed successfully!");
    
  } catch (error) {
    console.error("❌ Error in main seeder:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// Export functions for individual use
module.exports = {
  printEmailsWithoutCampaignId,
  updateEmailsWithCampaignId,
  getEmailCampaignIdStats,
  run
};

// Run the seeder if this file is executed directly
if (require.main === module) {
  run();
}
