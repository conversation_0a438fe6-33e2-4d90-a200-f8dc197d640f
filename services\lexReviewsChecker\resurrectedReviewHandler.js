const prisma = require("../../database/prisma/getPrismaClient");
const { ReviewStatus } = require("@prisma/client");
const { sendLexSlackNotification } = require("./utils");

/**
 * Handle review status transitions for resurrected/returned reviews
 */
class ResurrectedReviewHandler {
    /**
 * Handle when a review is removed
 * @param {Object} review - The review object
 * @param {Date} removalDate - Date when review was removed
 */
    static async handleReviewRemoved(review, removalDate = new Date()) {
        try {
            // Parse existing history arrays
            const removedHistory = Array.isArray(review.removedHistory)
                ? review.removedHistory
                : [];

            // Add current removal date to history
            removedHistory.push(removalDate.toISOString());

            // Create updated review object (don't update database here)
            const updatedReview = {
                ...review,
                status: ReviewStatus.REMOVED,
                removedAt: removalDate,
                removedHistory: removedHistory,
                comments: "", // Clear comments when removed
            };

            // Send Slack notification
            await this.sendRemovalNotification(updatedReview, removalDate);

            console.log(`Review ${review.reviewId} marked as removed on ${removalDate.toISOString()}`);
            return updatedReview;
        } catch (error) {
            console.error(`Error handling review removal for ${review.reviewId}:`, error);
            throw error;
        }
    }

    /**
     * Handle when a review has returned
     * @param {Object} review - The review object
     * @param {Date} returnDate - Date when review returned
     */
    static async handleReviewReturned(review, returnDate = new Date()) {
        try {
            // Parse existing history arrays
            const removedHistory = Array.isArray(review.removedHistory)
                ? review.removedHistory
                : [];
            const returnedHistory = Array.isArray(review.returnedHistory)
                ? review.returnedHistory
                : [];

            // Add current return date to history
            returnedHistory.push(returnDate.toISOString());

            // Get the latest removal date for comment
            console.log("Debug - removedHistory:", removedHistory);
            console.log("Debug - removedHistory length:", removedHistory.length);
            const latestRemovalDate = removedHistory.length > 0
                ? new Date(removedHistory[removedHistory.length - 1]).toLocaleDateString()
                : "Unknown";
            console.log("Debug - latestRemovalDate:", latestRemovalDate);

            // Create comment
            const comment = `Last removed: ${latestRemovalDate}. Returned on ${returnDate.toLocaleDateString()}.`;

            // Create updated review object (don't update database here)
            const updatedReview = {
                ...review,
                status: ReviewStatus.RESURRECTED,
                removedAt: null, // Clear removedAt but keep history
                returnedHistory: returnedHistory,
                comments: comment,
            };

            // Send Slack notification
            await this.sendReturnNotification(updatedReview, returnDate, latestRemovalDate);

            console.log(`Review ${review.reviewId} marked as returned on ${returnDate.toISOString()}`);
            return updatedReview;
        } catch (error) {
            console.error(`Error handling review return for ${review.reviewId}:`, error);
            throw error;
        }
    }

    /**
     * Handle when a returned review gets removed again
     * @param {Object} review - The review object
     * @param {Date} removalDate - Date when review was removed again
     */
    static async handleReviewRemovedAgain(review, removalDate = new Date()) {
        try {
            // Parse existing history arrays
            const removedHistory = Array.isArray(review.removedHistory)
                ? review.removedHistory
                : [];
            const returnedHistory = Array.isArray(review.returnedHistory)
                ? review.returnedHistory
                : [];

            // Add current removal date to history
            removedHistory.push(removalDate.toISOString());

            // Get the latest return date for comment
            const latestReturnDate = returnedHistory.length > 0
                ? new Date(returnedHistory[returnedHistory.length - 1]).toLocaleDateString()
                : "Unknown";

            // Create comment
            const comment = `Returned on ${latestReturnDate}, removed again on ${removalDate.toLocaleDateString()}.`;

            // Create updated review object (don't update database here)
            const updatedReview = {
                ...review,
                status: ReviewStatus.REMOVED,
                removedAt: removalDate,
                removedHistory: removedHistory,
                comments: comment,
            };

            // Send Slack notification
            await this.sendRemovalAgainNotification(updatedReview, removalDate, latestReturnDate);

            console.log(`Review ${review.reviewId} marked as removed again on ${removalDate.toISOString()}`);
            return updatedReview;
        } catch (error) {
            console.error(`Error handling review removal again for ${review.reviewId}:`, error);
            throw error;
        }
    }

    /**
     * Send Slack notification for review removal
     */
    static async sendRemovalNotification(review, removalDate) {
        const message = {
            text: `:warning: *Review Removed* :warning:\n\n` +
                `*Review Details:*\n` +
                `• *Review ID:* ${review.reviewId}\n` +
                `• *ASIN:* \`${review.asin}\`\n` +
                `• *Brand:* ${review.brandName}\n` +
                `• *Status:* REMOVED\n` +
                `• *Review Link:* <${review.reviewUrl}|View Review>\n` +
                `• *Removed At:* ${removalDate.toLocaleString()}\n` +
                `• *Comments:* ${review.comments || "N/A"}\n` +
                `• *Removed History:* ${JSON.stringify(review.removedHistory)}\n` +
                `• *Returned History:* ${JSON.stringify(review.returnedHistory)}`
        };

        await sendLexSlackNotification([review], "RESURRECTED", null, 1, 0, message);
    }

    /**
     * Send Slack notification for review return
     */
    static async sendReturnNotification(review, returnDate, lastRemovalDate) {
        const message = {
            text: `:green_circle: *Review Returned* :green_circle:\n\n` +
                `*Review Details:*\n` +
                `• *Review ID:* ${review.reviewId}\n` +
                `• *ASIN:* \`${review.asin}\`\n` +
                `• *Brand:* ${review.brandName}\n` +
                `• *Status:* RESURRECTED\n` +
                `• *Review Link:* <${review.reviewUrl}|View Review>\n` +
                `• *Returned At:* ${returnDate.toLocaleString()}\n` +
                `• *Last Removed:* ${lastRemovalDate}\n` +
                `• *Comments:* ${review.comments}\n` +
                `• *Removed History:* ${JSON.stringify(review.removedHistory)}\n` +
                `• *Returned History:* ${JSON.stringify(review.returnedHistory)}`
        };

        await sendLexSlackNotification([review], "RESURRECTED", null, 1, 0, message);
    }

    /**
     * Send Slack notification for review removed again
     */
    static async sendRemovalAgainNotification(review, removalDate, lastReturnDate) {
        const message = {
            text: `:x: *Review Removed Again* :x:\n\n` +
                `*Review Details:*\n` +
                `• *Review ID:* ${review.reviewId}\n` +
                `• *ASIN:* \`${review.asin}\`\n` +
                `• *Brand:* ${review.brandName}\n` +
                `• *Status:* REMOVED\n` +
                `• *Review Link:* <${review.reviewUrl}|View Review>\n` +
                `• *Removed Again At:* ${removalDate.toLocaleString()}\n` +
                `• *Last Returned:* ${lastReturnDate}\n` +
                `• *Comments:* ${review.comments}\n` +
                `• *Removed History:* ${JSON.stringify(review.removedHistory)}\n` +
                `• *Returned History:* ${JSON.stringify(review.returnedHistory)}`
        };

        await sendLexSlackNotification([review], "RESURRECTED", null, 1, 0, message);
    }

    /**
     * Check if a review status transition is needed based on current status
     * @param {Object} review - The review object
     * @param {ReviewStatus} newStatus - The new status detected
     */
    static async checkStatusTransition(review, newStatus) {
        const currentStatus = review.status;

        // If review was REMOVED and now PRESENT, it has returned
        if (currentStatus === ReviewStatus.REMOVED && newStatus === ReviewStatus.PRESENT) {
            return await this.handleReviewReturned(review);
        }

        // If review was RESURRECTED and now REMOVED, it was removed again
        if (currentStatus === ReviewStatus.RESURRECTED && newStatus === ReviewStatus.REMOVED) {
            return await this.handleReviewRemovedAgain(review);
        }

        // If review was PRESENT and now REMOVED, it was removed for the first time
        if (currentStatus === ReviewStatus.PRESENT && newStatus === ReviewStatus.REMOVED) {
            return await this.handleReviewRemoved(review);
        }

        // No transition needed, return the review with new status but no other changes
        return {
            ...review,
            status: newStatus,
            removedAt: newStatus === ReviewStatus.REMOVED && !review.removedAt ? new Date() : review.removedAt,
        };
    }
}

module.exports = ResurrectedReviewHandler; 