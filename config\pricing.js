/**
 * Configuration file for AI service pricing
 * This file contains the pricing multipliers for different AI services
 * These values can be easily updated when pricing changes
 * Last updated: April 2024 based on Azure OpenAI Service pricing
 */

module.exports = {
  // Azure OpenAI pricing (per million tokens)
  azure: {
    // GPT-4 Turbo pricing (Global deployment)
    gpt4: {
      input: 10.0,    // $10 per million input tokens
      output: 30.0,   // $30 per million output tokens
    },
    // GPT-3.5 Turbo pricing
    gpt35: {
      input: 0.5,     // $0.5 per million input tokens
      output: 1.5,    // $1.5 per million output tokens
    },
    // GPT-4o pricing (Global deployment)
    gpt4o: {
      input: 5.0,     // $5 per million input tokens
      output: 15.0,   // $15 per million output tokens
    },
    // GPT-4o mini pricing (Global deployment)
    gpt4o_mini: {
      input: 0.25,    // $0.25 per million input tokens
      output: 0.75,   // $0.75 per million output tokens
    }
  },

  // Default model to use (can be 'gpt4', 'gpt35', 'gpt4o', or 'gpt4o_mini')
  defaultModel: 'gpt4o',

  // Helper function to get the correct pricing based on model
  getPricing: function(model = null) {
    const modelToUse = model || this.defaultModel;
    return this.azure[modelToUse] || this.azure.gpt4;
  },

  // Helper function to calculate input token cost
  getInputTokenCost: function(tokens, model = null) {
    const pricing = this.getPricing(model);
    return (tokens * pricing.input) / 1000000;
  },

  // Helper function to calculate output token cost
  getOutputTokenCost: function(tokens, model = null) {
    const pricing = this.getPricing(model);
    return (tokens * pricing.output) / 1000000;
  }
};
