/*
  Domain extraction using the Public Suffix List when available.
  - Prefer using `tldts` (kept up-to-date with the PSL) for accurate eTLD+1.
  - Falls back to a heuristic that handles common multi-level TLD patterns
    (e.g., co.uk, com.au, us.com) without maintaining an exhaustive list here.

  To enable the accurate path, install:
    npm i tldts --save
*/

let tldts = null;
try {
  // Optional dependency. If not installed, gracefully degrade to the fallback logic.
  // eslint-disable-next-line import/no-extraneous-dependencies, global-require
  tldts = require('tldts');
} catch (_) {
  tldts = null;
}

function isIpAddress(hostname) {
  // IPv4: 4 groups of 1-3 digits separated by dots
  const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
  
  // IPv6: contains colons (simplified check)
  const ipv6Regex = /:/;
  
  // Check if it's an IPv4 address
  if (ipv4Regex.test(hostname)) {
    // Additional validation: each octet should be 0-255
    const parts = hostname.split('.');
    for (const part of parts) {
      const num = parseInt(part, 10);
      if (num < 0 || num > 255) {
        return false;
      }
    }
    return true;
  }
  
  // Check if it's an IPv6 address
  if (ipv6Regex.test(hostname)) {
    return true;
  }
  
  return false;
}

function isValidHostname(hostname) {
  // Check if hostname contains only valid characters
  // Valid: letters, numbers, dots, hyphens, and common business symbols
  // Invalid: parentheses, spaces, control characters, etc.
  const validHostnameRegex = /^[a-zA-Z0-9.\-®™©]+$/;
  
  if (!validHostnameRegex.test(hostname)) {
    return false;
  }
  
  // Additional checks:
  // - Should not start or end with dot or hyphen
  // - Should not have consecutive dots
  // - Each label should not start or end with hyphen
  if (hostname.startsWith('.') || hostname.endsWith('.') || 
      hostname.startsWith('-') || hostname.endsWith('-') ||
      hostname.includes('..')) {
    return false;
  }
  
  // Check each label (part between dots)
  const labels = hostname.split('.');
  for (const label of labels) {
    if (label.length === 0 || label.startsWith('-') || label.endsWith('-')) {
      return false;
    }
  }
  
  return true;
}

function isEmailSafeDomain(hostname) {
  // FIRST: Check for Unicode characters (non-ASCII) - this catches ®, ™, ©, etc.
  for (let i = 0; i < hostname.length; i++) {
    if (hostname.charCodeAt(i) > 127) {
      return false; // Non-ASCII character detected
    }
  }
  
  // SECOND: Check if domain is safe for email addresses
  // RFC 5322 compliant: only allows a-z, A-Z, 0-9, ., -, _
  const emailSafeRegex = /^[a-zA-Z0-9.\-_]+$/;
  
  if (!emailSafeRegex.test(hostname)) {
    return false;
  }
  
  // THIRD: Additional email-safe checks:
  // - Should not start or end with dot or hyphen
  // - Should not have consecutive dots
  // - Each label should not start or end with hyphen
  if (hostname.startsWith('.') || hostname.endsWith('.') || 
      hostname.startsWith('-') || hostname.endsWith('-') ||
      hostname.includes('..')) {
    return false;
  }
  
  // FOURTH: Check each label (part between dots)
  const labels = hostname.split('.');
  for (const label of labels) {
    if (label.length === 0 || label.startsWith('-') || label.endsWith('-')) {
      return false;
    }
  }
  
  return true;
}

function extractDomain(url) {
  try {
    if (!url) return;
    if (!url.startsWith("http")) {
      url = `https://${url}`;
    }else{
      // replace http with https
      url = url.replace(/^http:\/\//i, 'https://');
    }
    
    // Try to parse the URL - if it fails, it's malformed
    let parsedUrl;
    try {
      parsedUrl = new URL(url);
    } catch (urlError) {
      console.error('Malformed URL detected:', url, urlError.message);
      return null;
    }
    
    const hostname = parsedUrl.hostname;

    // Check if domain is email-safe FIRST (before any processing)
    // This catches Unicode characters like ®, ™, © before they get converted
    if (!isEmailSafeDomain(hostname)) {
      console.error('Domain contains Unicode/special characters - not email-safe, rejecting:', hostname);
      return null; // Reject Unicode domains for email purposes
    }

    // Reject IP addresses - they are not valid domains
    if (isIpAddress(hostname)) {
      console.error('IP address detected and rejected:', hostname);
      return null;
    }

    // Validate hostname - reject if it contains invalid characters
    if (!isValidHostname(hostname)) {
      console.error('Invalid hostname detected:', hostname);
      return null;
    }

    // Preferred: use tldts when available for PSL-accurate extraction
    if (tldts && typeof tldts.getDomain === 'function') {
      const registrable = tldts.getDomain(hostname, { allowPrivateDomains: true });
      // console.log('Registrable:- ', registrable);
      if (registrable) {
        return registrable;
      }
    }

    // Fallback heuristic: handle common multi-level TLD tokens
    // console.log('Fallback Hostname:- ', hostname);
    const parts = hostname.split('.');
    if (parts.length < 2) return hostname;

    // A broad set of tokens used as second-level categories across ccTLDs and
    // CentralNic-style namespaces under .com/.net (co, com, net, org, gov, edu, etc.).
    // This is NOT exhaustive like PSL, but covers common cases until `tldts` is installed.
    const secondLevelTokens = new Set([
      'ac','co','com','net','org','gov','edu','mil','int','nom','ne','or','id','me','sch','plc','ltd',
      // Country codes used as pseudo second-level under .com/.net (e.g., us.com, uk.net)
      'us','uk','de','in','br','au','ca','jp','fr','it','es','ru','mx','nl','se','ch','no','fi','dk',
      'ie','nz','za','pl','tr','ar','at','be','hk','kr','tw','pt','gr','cz','hu','sg','il','my','ro',
      'ua','th','sk','bg','si','lt','lv','ee','vn'
    ]);

    if (secondLevelTokens.has(parts[parts.length - 2])) {
      return parts.slice(-3).join('.');
    }
    return parts.slice(-2).join('.');
  } catch (error) {
    console.error('Invalid URL:', error);
    return null;
  }
}

module.exports = { extractDomain, isEmailSafeDomain };
// Example usage
// console.log(extractDomain('sub.example.com'));         // Output: example.com
// console.log(extractDomain('https://www.example.co.uk'));       // Output: example.co.uk
// console.log(extractDomain('https://example.com'));             // Output: example.com
// console.log(extractDomain('https://***************'));         // Output: 123.123
// console.log(extractDomain('https://subdomain.example.com.au')); // Output: example.com.au
// console.log(extractDomain('https://localhost:3000'));          // Output: localhost
// console.log(extractDomain('https://invalid-url'));             // Output: null
// console.log(extractDomain(''));                                // Output: null
// console.log(extractDomain('http://www.mzoosleepmask.us.com'));             // Output: example.com
// console.log(extractDomain('https://shopkhim.myshopify.com'));             // Output: shopkhim.myshopify.com
// console.log(extractDomain('http://www.eqlightningdelivery(werecordsn).com'));             // Output: eqlightningdelivery(werecordsn).com