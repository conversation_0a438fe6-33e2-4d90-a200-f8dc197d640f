const { PrismaClient } = require("@prisma/client");
const { ReviewStatus } = require("@prisma/client");
const fs = require("fs");
const path = require("path");
const csvtojson = require("csvtojson");

const prisma = new PrismaClient();

/**
 * Seeder to fix reviews that were incorrectly marked as REMOVED yesterday
 * based on the CSV file data
 */
async function fixRemovedReviewsFromCSV() {
    try {
        console.log("🔧 Starting to fix incorrectly marked REMOVED reviews from CSV...");

        // Path to the CSV file
        const csvPath = path.join(__dirname, '../sample/removed/lex_reviews_output_2025-08-08T13-56-37-245Z.csv');

        if (!fs.existsSync(csvPath)) {
            console.error(`❌ CSV file not found: ${csvPath}`);
            return;
        }

        console.log(`📄 Reading CSV file: ${csvPath}`);

        // Read and parse the CSV file
        const csvData = await csvtojson().fromFile(csvPath);
        console.log(`📊 Found ${csvData.length} records in CSV`);

        // Create a map of reviewID to status from CSV
        const csvStatusMap = new Map();
        csvData.forEach(row => {
            const reviewId = row['Review ID'];
            const status = row['Status'];
            if (reviewId && status) {
                csvStatusMap.set(reviewId, status);
            }
        });

        console.log(`🗺️  Created status map for ${csvStatusMap.size} reviews from CSV`);

        // Get all reviews from the database that were updated yesterday
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        yesterday.setHours(0, 0, 0, 0);

        const today = new Date();
        today.setHours(0, 0, 0, 0);

        console.log(`📅 Looking for reviews updated between ${yesterday.toISOString()} and ${today.toISOString()}`);

        const dbReviews = await prisma.review.findMany({
            where: {
                updatedAt: {
                    gte: yesterday,
                    lt: today
                }
            },
            select: {
                id: true,
                reviewId: true,
                reviewUrl: true,
                status: true,
                removedAt: true,
                updatedAt: true,
                reviewJobId: true
            }
        });

        console.log(`📊 Found ${dbReviews.length} reviews in database from yesterday`);

        let fixedCount = 0;
        let skippedCount = 0;
        let notFoundInCSVCount = 0;

        for (const dbReview of dbReviews) {
            const csvStatus = csvStatusMap.get(dbReview.reviewId);

            if (!csvStatus) {
                console.log(`❓ Review ${dbReview.reviewId} not found in CSV`);
                notFoundInCSVCount++;
                continue;
            }

            // Check if the status needs to be updated
            if (dbReview.status !== csvStatus) {
                console.log(`🔄 Fixing review ${dbReview.reviewId}: ${dbReview.status} → ${csvStatus}`);

                try {
                    const updateData = {
                        status: csvStatus,
                        comments: `Fixed: Status updated from ${dbReview.status} to ${csvStatus} based on CSV data from 2025-08-08`,
                        updatedAt: new Date()
                    };

                    // Clear removedAt if status is being changed from REMOVED to PRESENT
                    if (dbReview.status === ReviewStatus.REMOVED && csvStatus === ReviewStatus.PRESENT) {
                        updateData.removedAt = null;
                    }

                    await prisma.review.update({
                        where: { id: dbReview.id },
                        data: updateData
                    });

                    fixedCount++;
                    console.log(`✅ Fixed review ${dbReview.reviewId}`);
                } catch (error) {
                    console.error(`❌ Error fixing review ${dbReview.reviewId}:`, error.message);
                }
            } else {
                console.log(`⏭️  Skipping review ${dbReview.reviewId} (status already correct: ${csvStatus})`);
                skippedCount++;
            }
        }

        console.log("\n📈 Summary:");
        console.log(`✅ Fixed: ${fixedCount} reviews`);
        console.log(`⏭️  Skipped (already correct): ${skippedCount} reviews`);
        console.log(`❓ Not found in CSV: ${notFoundInCSVCount} reviews`);
        console.log(`📊 Total processed: ${dbReviews.length} reviews`);

        // Create a detailed summary report
        const summary = {
            timestamp: new Date().toISOString(),
            csvFilePath: csvPath,
            csvRecordCount: csvData.length,
            csvStatusMapSize: csvStatusMap.size,
            dbReviewCount: dbReviews.length,
            fixedCount,
            skippedCount,
            notFoundInCSVCount,
            fixedReviews: dbReviews
                .filter(r => csvStatusMap.get(r.reviewId) && r.status !== csvStatusMap.get(r.reviewId))
                .map(r => ({
                    reviewId: r.reviewId,
                    reviewUrl: r.reviewUrl,
                    reviewJobId: r.reviewJobId,
                    oldStatus: r.status,
                    newStatus: csvStatusMap.get(r.reviewId)
                })),
            skippedReviews: dbReviews
                .filter(r => csvStatusMap.get(r.reviewId) && r.status === csvStatusMap.get(r.reviewId))
                .map(r => ({
                    reviewId: r.reviewId,
                    reviewUrl: r.reviewUrl,
                    reviewJobId: r.reviewJobId,
                    status: r.status
                })),
            notFoundReviews: dbReviews
                .filter(r => !csvStatusMap.get(r.reviewId))
                .map(r => ({
                    reviewId: r.reviewId,
                    reviewUrl: r.reviewUrl,
                    reviewJobId: r.reviewJobId,
                    status: r.status
                }))
        };

        // Save summary to file
        const summaryPath = path.join(__dirname, '../sample/removed/fixRemovedReviewsFromCSV_summary.json');
        fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));
        console.log(`📄 Summary saved to: ${summaryPath}`);

        console.log("\n🎉 Fix completed successfully!");

    } catch (error) {
        console.error("❌ Error in fixRemovedReviewsFromCSV:", error);
        throw error;
    } finally {
        await prisma.$disconnect();
    }
}



// Run the seeder if this file is executed directly
if (require.main === module) {
    console.log("Running fix based on CSV data...");
    fixRemovedReviewsFromCSV().catch(console.error);
}

module.exports = { fixRemovedReviewsFromCSV };
