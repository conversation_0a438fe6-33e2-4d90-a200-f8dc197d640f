const prisma = require("../../database/prisma/getPrismaClient");

async function resetIncompleteLeads() {
  try {
    const updatedLeads = await prisma.lead.updateMany({
      where: {
        AND: [
          {
            processId: {
              not: null,
            },
          },
          {
            status: {
              in: ["scoring", "validating"],
            },
          },
        ],
      },
      data: {
        status: "srp_scraped",
        processId: null,
      },
    });

    console.log(
      `Successfully reset ${updatedLeads.count} leads from scoring/validating to srp_scraped status`,
    );
    return updatedLeads;
  } catch (error) {
    console.error("Error resetting lead statuses:", error);
    // throw error;
    return null;
  }
}

async function resetStaleLeads() {
  // Calculate timestamp for 1 hour ago
  const oneHourAgo = new Date(Date.now() - 30 * 60 * 1000);

  try {
    const updatedLeads = await prisma.lead.updateMany({
      where: {
        AND: [
          {
            processId: {
              not: null,
            },
          },
          {
            status: {
              in: ["scoring", "validating", "processing"],
            },
          },
          {
            updatedAt: {
              lt: oneHourAgo,
            },
          },
        ],
      },
      data: {
        status: "srp_scraped",
        processId: null,
      },
    });

    console.log(
      `Successfully reset ${updatedLeads.count} stale leads older than 1 hour from scoring/validating to srp_scraped status`,
    );
    return updatedLeads;
  } catch (error) {
    console.error("Error resetting stale lead statuses:", error);
    // throw error;
    return null;
  }
}

async function executeReset() {
  try {
    const result = await resetIncompleteLeads();
    await prisma.$disconnect();
    return result;
  } catch (error) {
    await prisma.$disconnect();
    process.exit(1);
  } finally {
    try {
      await prisma.$disconnect();
    } catch (disconnectError) {
      logger.error("Error disconnecting from database:", disconnectError);
    }
  }
}

module.exports = {
  resetIncompleteLeads,
  resetStaleLeads,
  executeReset,
};

if (require.main === module) {
  executeReset();
}
