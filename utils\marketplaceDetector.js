/**
 * Utility for detecting marketplace from seller URLs
 */

/**
 * Mapping of top-level domains to marketplace codes
 */
const TLD_TO_MARKETPLACE = {
  'com': 'US',
  'co.uk': 'UK',
  'de': 'DE',
  'fr': 'FR',
  'it': 'IT',
  'es': 'ES',
  'ca': 'CA',
  'com.au': 'AU',
  'co.jp': 'JP',
  'in': 'IN',
  'com.mx': 'MX',
  'com.br': 'BR',
  'nl': 'NL',
  'se': 'SE',
  'pl': 'PL',
  'com.tr': 'TR',
  'ae': 'AE',
  'sa': 'SA',
  'sg': 'SG'
};

/**
 * Extract marketplace from seller URL based on top-level domain
 * @param {string} sellerUrl - The seller URL (e.g., "amazon.com/xyz", "amazon.de/abc")
 * @returns {string|null} - Marketplace code (e.g., "US", "UK") or null if not detectable
 */
function extractMarketplaceFromUrl(sellerUrl) {
  if (!sellerUrl || typeof sellerUrl !== 'string') {
    return null;
  }

  // Clean the URL - remove protocol if present
  let cleanUrl = sellerUrl.trim().toLowerCase();
  cleanUrl = cleanUrl.replace(/^https?:\/\//, '');
  cleanUrl = cleanUrl.replace(/^www\./, '');

  // Check if it's an Amazon URL
  if (!cleanUrl.startsWith('amazon.')) {
    return null;
  }

  // Extract the domain part (everything before the first slash)
  const domainPart = cleanUrl.split('/')[0];
  
  // Extract TLD from amazon.{tld}
  const tldMatch = domainPart.match(/^amazon\.(.+)$/);
  if (!tldMatch) {
    return null;
  }

  const tld = tldMatch[1];
  
  // Look up marketplace code
  return TLD_TO_MARKETPLACE[tld] || null;
}

/**
 * Validate seller URL format
 * @param {string} sellerUrl - The seller URL to validate
 * @returns {boolean} - True if URL format is valid
 */
function isValidSellerUrlFormat(sellerUrl) {
  if (!sellerUrl || typeof sellerUrl !== 'string') {
    return false;
  }

  const cleanUrl = sellerUrl.trim().toLowerCase()
    .replace(/^https?:\/\//, '')
    .replace(/^www\./, '');

  // Should start with amazon. and have some path
  return /^amazon\.[a-z.]+\/.+/.test(cleanUrl);
}

module.exports = {
  extractMarketplaceFromUrl,
  isValidSellerUrlFormat,
  TLD_TO_MARKETPLACE
};
