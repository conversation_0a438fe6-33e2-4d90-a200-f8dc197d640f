#!/usr/bin/env node

/**
 * LiteLLM Tags Format Test Script
 * 
 * This script demonstrates the proper LiteLLM metadata/tags format
 * as shown in your curl example, using environment variables for auth.
 */

require('dotenv').config();
const axios = require('axios');
const { litellmService } = require('./services/ai/litellmService');
const { aiMetadataHelper } = require('./utils/aiMetadataHelper');

// Configuration from environment
const LITELLM_PROXY_URL = process.env.LITELLM_PROXY_URL || 'http://**************:4000';
const API_KEY = process.env.LITELLM_API_KEY || process.env.OPENAI_API_KEY || 'sk-1234';

// Enable metadata logging
process.env.AI_METADATA_LOGGING = 'true';

function printTestHeader(title) {
    console.log('\n' + '='.repeat(60));
    console.log(`🏷️  ${title}`);
    console.log('='.repeat(60));
}

function printTestResult(testName, success, details = '') {
    const icon = success ? '✅' : '❌';
    console.log(`${icon} ${testName}: ${success ? 'PASSED' : 'FAILED'}`);
    if (details) {
        console.log(`   ${details}`);
    }
}

async function testDirectCurlFormat() {
    printTestHeader('Direct cURL Format Test (like your example)');
    
    try {
        console.log('🔄 Testing direct HTTP request with LiteLLM tags format...');
        
        const requestData = {
            model: 'gpt-4o',
            messages: [
                {
                    role: 'user',
                    content: 'what llm are you'
                }
            ],
            user: 'sellerbot_user', // Track spend by user
            metadata: {
                tags: [
                    'jobID:test_job_12345',
                    'taskName:llm_identification',
                    'service:SellerBot',
                    'function:testDirectCurlFormat',
                    'useCase:testing',
                    'environment:' + (process.env.NODE_ENV || 'development'),
                    'requestType:direct_curl'
                ]
            }
        };
        
        console.log('Request Data:');
        console.log(JSON.stringify(requestData, null, 2));
        
        const response = await axios.post(`${LITELLM_PROXY_URL}/v1/chat/completions`, requestData, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${API_KEY}`
            },
            timeout: 30000
        });
        
        const result = response.data;
        const message = result.choices?.[0]?.message?.content;
        const success = response.status === 200 && !!message;
        
        printTestResult('Direct cURL Format', success, `Status: ${response.status}`);
        printTestResult('Response Content', !!message, `Response: "${message}"`);
        
        if (success) {
            console.log('\nFull Response:');
            console.log(JSON.stringify(result, null, 2));
        }
        
        return success;
    } catch (error) {
        printTestResult('Direct cURL Format', false, error.message);
        if (error.response?.data) {
            console.log('Error Response:', JSON.stringify(error.response.data, null, 2));
        }
        return false;
    }
}

async function testLiteLLMServiceWithTags() {
    printTestHeader('LiteLLM Service with Tags Test');
    
    try {
        console.log('🔄 Testing LiteLLM service with proper tags format...');
        
        const result = await litellmService.createChatCompletion([
            {
                role: 'system',
                content: 'You are a helpful assistant for SellerBot, an e-commerce automation platform.'
            },
            {
                role: 'user',
                content: 'Explain what you do in one sentence.'
            }
        ], {
            useCase: 'service_description',
            feature: 'assistant_intro',
            userId: 'sellerbot_demo',
            userType: 'demo',
            model: 'gpt-4o',
            temperature: 0.7,
            max_tokens: 100,
            customTags: {
                jobID: 'demo_job_67890',
                taskName: 'service_explanation',
                component: 'litellm_service',
                testType: 'integration'
            }
        });
        
        const success = result.success && result.message;
        printTestResult('LiteLLM Service', success, success ? `Response: "${result.message.substring(0, 50)}..."` : result.error);
        printTestResult('Metadata Included', !!result.metadata, 'Response contains metadata');
        
        if (result.metadata) {
            console.log('\nGenerated Tags:');
            result.metadata.tags?.forEach(tag => console.log(`  - ${tag}`));
            console.log(`\nUser: ${result.metadata.user || 'Not set'}`);
            console.log(`Request ID: ${result.metadata.request_id}`);
        }
        
        return success;
    } catch (error) {
        printTestResult('LiteLLM Service', false, error.message);
        return false;
    }
}

async function testScrapeGPTWithTags() {
    printTestHeader('ScrapeGPT with Tags Test');
    
    try {
        console.log('🔄 Testing ScrapeGPT with LiteLLM tags format...');
        
        const result = await litellmService.getChatGPTResponse(
            'You are an AI that analyzes website content for business information.',
            'Analyze this sample text: "Acme Corp is a leading provider of widgets and gadgets based in New York."',
            {
                useCase: 'scrape_analysis',
                feature: 'business_extraction',
                userId: 'scraper_bot_001',
                userType: 'system',
                scrapeType: 'business_info',
                domain: 'acme-corp.com',
                customTags: {
                    jobID: 'scrape_job_11111',
                    taskName: 'business_info_extraction',
                    batchID: 'batch_001',
                    sourceType: 'website_content'
                }
            }
        );
        
        const success = result.success && result.message;
        printTestResult('ScrapeGPT with Tags', success, success ? `Response: "${result.message.substring(0, 50)}..."` : result.error);
        
        if (result.metadata?.tags) {
            console.log('\nScrapeGPT Tags:');
            result.metadata.tags.forEach(tag => console.log(`  - ${tag}`));
        }
        
        return success;
    } catch (error) {
        printTestResult('ScrapeGPT with Tags', false, error.message);
        return false;
    }
}

async function testFactoryWithTags() {
    printTestHeader('Factory Completion with Tags Test');
    
    try {
        console.log('🔄 Testing factory completion with LiteLLM tags...');
        
        const testData = {
            textContent: 'Example Corp is a software company that provides CRM solutions for small businesses.',
            businessKeywords: ['Example Corp', 'CRM', 'software'],
            url: 'https://example-corp.com'
        };
        
        const result = await litellmService.completionFactory(
            'match_text',
            testData,
            {
                useCase: 'lead_generation',
                feature: 'business_matching',
                userId: 'lead_gen_system',
                userType: 'system',
                customTags: {
                    jobID: 'lead_job_22222',
                    taskName: 'business_text_matching',
                    batchID: 'lead_batch_002',
                    sourceType: 'manual_input',
                    matchingType: 'text_analysis'
                }
            }
        );
        
        const success = result.success && result.message;
        printTestResult('Factory with Tags', success, success ? `Response: "${result.message.substring(0, 50)}..."` : result.error);
        
        if (result.metadata?.tags) {
            console.log('\nFactory Tags:');
            result.metadata.tags.forEach(tag => console.log(`  - ${tag}`));
        }
        
        return success;
    } catch (error) {
        printTestResult('Factory with Tags', false, error.message);
        return false;
    }
}

async function testStreamingWithTags() {
    printTestHeader('Streaming with Tags Test');
    
    try {
        console.log('🔄 Testing streaming with LiteLLM tags...');
        
        const stream = await litellmService.createStreamingCompletion([
            {
                role: 'user',
                content: 'List 5 benefits of using AI for e-commerce, one per line.'
            }
        ], {
            useCase: 'content_generation',
            feature: 'benefit_listing',
            userId: 'content_generator',
            userType: 'system',
            customTags: {
                jobID: 'content_job_33333',
                taskName: 'ai_benefits_listing',
                outputFormat: 'list',
                streamingEnabled: 'true'
            }
        });
        
        let fullResponse = '';
        let chunkCount = 0;
        
        console.log('Streaming response:');
        for await (const chunk of stream) {
            const content = chunk.choices[0]?.delta?.content || '';
            if (content) {
                fullResponse += content;
                chunkCount++;
                process.stdout.write(content);
            }
        }
        console.log(); // New line
        
        const success = chunkCount > 0 && fullResponse.length > 0;
        printTestResult('Streaming with Tags', success, `Received ${chunkCount} chunks`);
        printTestResult('Content Generated', fullResponse.length > 0, `Content length: ${fullResponse.length}`);
        
        return success;
    } catch (error) {
        printTestResult('Streaming with Tags', false, error.message);
        return false;
    }
}

async function demonstrateTagsFormat() {
    printTestHeader('Tags Format Demonstration');
    
    console.log('🔄 Demonstrating LiteLLM tags format generation...');
    
    const metadata = aiMetadataHelper.generateOpenAIMetadata({
        useCase: 'demo',
        feature: 'tags_demo',
        userId: 'demo_user',
        userType: 'demo',
        operationType: 'demonstration',
        customTags: {
            jobID: 'demo_job_44444',
            taskName: 'tags_format_demo',
            priority: 'high',
            testMode: 'true'
        }
    });
    
    console.log('Generated LiteLLM Format:');
    console.log(JSON.stringify({
        user: metadata.user,
        metadata: metadata.metadata
    }, null, 2));
    
    console.log('\nTags Array:');
    metadata.metadata.tags.forEach(tag => console.log(`  - ${tag}`));
    
    return true;
}

async function main() {
    console.log('🚀 Starting LiteLLM Tags Format Tests');
    console.log(`📍 Proxy URL: ${LITELLM_PROXY_URL}`);
    console.log(`🔑 API Key: ${API_KEY.substring(0, 10)}...\n`);
    
    const results = {
        directCurl: await testDirectCurlFormat(),
        litellmService: await testLiteLLMServiceWithTags(),
        scrapeGPT: await testScrapeGPTWithTags(),
        factory: await testFactoryWithTags(),
        streaming: await testStreamingWithTags(),
        tagsDemo: await demonstrateTagsFormat(),
    };
    
    // Final summary
    printTestHeader('Test Summary');
    
    const totalTests = Object.keys(results).length;
    const passedTests = Object.values(results).filter(Boolean).length;
    const failedTests = totalTests - passedTests;
    
    console.log(`📊 Test Results: ${passedTests}/${totalTests} passed`);
    console.log(`✅ Passed: ${passedTests}`);
    console.log(`❌ Failed: ${failedTests}`);
    
    for (const [testName, passed] of Object.entries(results)) {
        printTestResult(testName.charAt(0).toUpperCase() + testName.slice(1), passed);
    }
    
    if (passedTests === totalTests) {
        console.log('\n🎉 All LiteLLM tags tests passed!');
        console.log('\n💡 Usage Tips:');
        console.log('1. Tags format: ["key:value", "key:value"] - exactly like your curl example');
        console.log('2. User field tracks spend by user in LiteLLM');
        console.log('3. jobID and taskName tags help track specific operations');
        console.log('4. All requests automatically include function/file context');
        console.log('5. Check LiteLLM logs to see the tags in action');
    } else {
        console.log('\n⚠️  Some tests failed. Check the configuration and try again.');
    }
    
    process.exit(failedTests > 0 ? 1 : 0);
}

if (require.main === module) {
    main().catch(error => {
        console.error('❌ Test suite failed:', error);
        process.exit(1);
    });
}

module.exports = {
    testDirectCurlFormat,
    testLiteLLMServiceWithTags,
    testScrapeGPTWithTags,
    testFactoryWithTags,
    testStreamingWithTags,
    demonstrateTagsFormat
};
