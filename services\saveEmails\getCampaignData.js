const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();
const axios = require("axios");

require("dotenv").config();
const SMART_LEADS_API_KEY = process.env.SMART_LEADS_API_KEY;
const API_DELAY = 1000;
async function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

async function fetchClients() {
  try {
    const client = await prisma.client.findMany();
    console.log(`Client Data: ${JSON.stringify(client)}`);
  } catch (error) {
    console.error(error);
  }
}

async function getCampaignData(params) {
  try {
    const url =
      `https://server.smartlead.ai/api/v1/campaigns?api_key=${SMART_LEADS_API_KEY}`;
    
      let campaigns = [];

      try {
        const response = await axios.get(url);
        if (response && response.data) {
          campaigns = response.data;
        } else {
          console.warn("No data received from SmartLeads API.");
          return;
        }
      } catch (axiosError) {
        console.error(
          "Error while calling SmartLeads API:",
          axiosError.message
        );
        return;
      }

    for (const campaign of campaigns) {
      console.log(
        `Processing Campaign ID: ${campaign.id}, Client ID: ${campaign.client_id}, Campaign Name: ${campaign.name}`
      );

      const { client_id, id, name, parent_campaign_id, status } = campaign;
      // console.log({ client_id });
      if (client_id) {

        // Find the corresponding client using the client_id
        const client = await prisma.client.findUnique({
          where: { clientId: parseInt(client_id) },
        });

        if (client) {
          // Upsert the campaign data into the database, linking it to the found client
          await prisma.campaign.upsert({
            where: { campaignId: id, clientId: client.clientId },
            update: {
              name: name,
              clientId: client.clientId,
              parentCampaignId: parent_campaign_id
                    ? parent_campaign_id.toString()
                    : "null",
            },
            create: {
              campaignId: id,
              name: name,
              clientId: client.clientId,
              parentCampaignId: parent_campaign_id
                    ? parent_campaign_id.toString()
                    : "null",
            }
          });

          console.log(`Campaign ${name} saved for client ${client.name}`);
          // await sleep(API_DELAY);
        } else {
          console.log(
            `Client with clientId ${client_id} not found. Skipping campaign ${name}`
          );
        }
      }
    }
  } catch (error) {
    console.error(`Error in getCampaignData function: ${error}`);
  }
}

// fetchClients();
// getCampaignData();

module.exports = { getCampaignData };
