const path = require("path");
const { processCSV, extractEmails } = require("./lib");
const { SKIP_URL_PATTERNS, DEFAULT_OPTIONS } = require("./lib/constants");

/**
 * Main function for processing a CSV file\ducts,/items,/shop"
 */
async function main() {
  try {
    const inputFile = process.argv[2] || path.join(__dirname, "input.csv");
    const outputFile = process.argv[3] || path.join(__dirname, "output.csv");

    // Default to processing first 10 rows for testing
    const startRow = parseInt(process.argv[4] || "0", 10);
    const endRow = parseInt(process.argv[5] || "49", 10);
    const batchSize = parseInt(process.argv[6] || "10", 10);

    // Parse skip patterns from command line (comma-separated) or use defaults from constants
    const skipPatterns = process.argv[7]
      ? process.argv[7].split(",")
      : SKIP_URL_PATTERNS;

    console.log("Email Extraction Service");
    console.log("=======================");
    console.log(`Input file: ${inputFile}`);
    console.log(`Output file: ${outputFile}`);
    console.log(`Start row: ${startRow}`);
    console.log(`End row: ${isFinite(endRow) ? endRow : "All"}`);
    console.log(`Batch size: ${batchSize}`);
    console.log(`Skip URL patterns: ${skipPatterns.join(", ")}`);
    console.log(`Max pages per website: ${DEFAULT_OPTIONS.maxPages}`);
    console.log(
      `Rate limiting: ${DEFAULT_OPTIONS.maxConcurrent} concurrent, ${DEFAULT_OPTIONS.minTime}ms delay`
    );
    console.log("=======================");

    const options = {
      skipUrlPatterns: skipPatterns,
    };

    await processCSV(
      inputFile,
      outputFile,
      startRow,
      endRow,
      batchSize,
      options
    );

    console.log("Process completed successfully!");
  } catch (error) {
    console.error("Error in main process:", error);
    process.exit(1);
  }
}

/**
 * Extract emails from a single website (for direct API usage)
 * @param {string} websiteUrl - Website URL or domain
 * @param {Object} options - Options for email extraction
 * @returns {Promise<{emails: string[], status: string}>}
 */
async function extractEmailsFromWebsite(websiteUrl, options = {}) {
  return extractEmails(websiteUrl, options);
}

// Run main function if this script is executed directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  processCSV,
  extractEmailsFromWebsite,
};
