/**
 * LiteLLM Service with Comprehensive Metadata Tagging
 * 
 * This service provides a wrapper around LiteLLM proxy with rich metadata
 * for tracking and observability in Lunary and other monitoring tools.
 */

require('dotenv').config();
const OpenAI = require('openai');
const axios = require('axios');
const { aiMetadataHelper, metadataGenerators } = require('../../utils/aiMetadataHelper');

class LiteLLMService {
    constructor(options = {}) {
        this.proxyUrl = options.proxyUrl || process.env.LITELLM_PROXY_URL || 'http://74.225.248.239:4000';
        this.apiKey = options.apiKey || process.env.LITELLM_API_KEY || process.env.OPENAI_API_KEY || 'sk-litellm-default';
        
        // Initialize OpenAI client with LiteLLM proxy
        this.client = new OpenAI({
            apiKey: this.apiKey,
            baseURL: `${this.proxyUrl}/v1`
        });
        
        this.defaultModel = options.defaultModel || 'gpt-4o';
        this.availableModels = [];
        
        // Initialize available models
        this.initializeModels();
    }

    /**
     * Initialize available models from the proxy
     */
    async initializeModels() {
        try {
            const response = await axios.get(`${this.proxyUrl}/v1/models`, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`
                },
                timeout: 10000
            });
            
            this.availableModels = response.data.data || [];
            console.log(`LiteLLM Service: Loaded ${this.availableModels.length} available models`);
        } catch (error) {
            console.warn('LiteLLM Service: Could not load available models:', error.message);
            this.availableModels = [];
        }
    }

    /**
     * Get the best available model for a given preference
     */
    getBestModel(preferredModel = null) {
        if (preferredModel && this.availableModels.some(m => m.id === preferredModel)) {
            return preferredModel;
        }
        
        if (this.availableModels.length === 0) {
            return this.defaultModel;
        }
        
        const modelIds = this.availableModels.map(m => m.id);
        
        // Preference order
        const preferences = ['gpt-4o', 'gpt-4', 'gpt-3.5-turbo', 'claude-3-sonnet-20240229'];
        
        for (const pref of preferences) {
            const found = modelIds.find(id => id.includes(pref));
            if (found) return found;
        }
        
        return modelIds[0] || this.defaultModel;
    }

    /**
     * Create chat completion with comprehensive metadata
     */
    async createChatCompletion(messages, options = {}) {
        const startTime = Date.now();
        
        // Generate metadata
        const metadata = metadataGenerators.centralizedAI({
            useCase: options.useCase || 'general',
            operationType: 'chat_completion',
            feature: options.feature || 'chat',
            userId: options.userId,
            userType: options.userType,
            customTags: {
                model_requested: options.model,
                temperature: options.temperature,
                max_tokens: options.max_tokens,
                ...options.customTags
            }
        });

        // Select best model
        const modelToUse = this.getBestModel(options.model);
        metadata.tags.model_used = modelToUse;

        try {
            // Prepare request with metadata
            const requestOptions = {
                model: modelToUse,
                messages,
                temperature: options.temperature || 0.7,
                max_tokens: options.max_tokens || 1000,
                presence_penalty: options.presence_penalty || 0,
                top_p: options.top_p || 1,
                stream: options.stream || false,
                ...options
            };

            // Add metadata to extra headers
            const extraHeaders = aiMetadataHelper.generateHeaders({
                ...options,
                operationType: 'chat_completion'
            });

            // Log metadata if enabled
            aiMetadataHelper.logMetadata(metadata, 'info');

            // Make the request
            const completion = await this.client.chat.completions.create(requestOptions, {
                headers: extraHeaders
            });

            const duration = Date.now() - startTime;

            // Prepare result with metadata
            const result = {
                success: true,
                message: completion.choices[0]?.message?.content,
                usage: completion.usage,
                duration,
                model: modelToUse,
                provider: 'litellm-proxy',
                metadata: {
                    ...metadata,
                    response: {
                        id: completion.id,
                        created: completion.created,
                        finish_reason: completion.choices[0]?.finish_reason,
                        duration_ms: duration
                    }
                }
            };

            // Log successful completion
            aiMetadataHelper.logMetadata({
                ...metadata,
                status: 'success',
                duration_ms: duration,
                tokens_used: completion.usage?.total_tokens
            }, 'info');

            return result;

        } catch (error) {
            const duration = Date.now() - startTime;
            
            // Log error with metadata
            aiMetadataHelper.logMetadata({
                ...metadata,
                status: 'error',
                error_message: error.message,
                error_type: error.constructor.name,
                duration_ms: duration
            }, 'error');

            return {
                success: false,
                error: error.message,
                provider: 'litellm-proxy',
                duration,
                metadata: {
                    ...metadata,
                    error: {
                        message: error.message,
                        type: error.constructor.name,
                        duration_ms: duration
                    }
                }
            };
        }
    }

    /**
     * Create streaming chat completion with metadata
     */
    async createStreamingCompletion(messages, options = {}) {
        const metadata = metadataGenerators.centralizedAI({
            useCase: options.useCase || 'general',
            operationType: 'streaming_completion',
            feature: options.feature || 'streaming_chat',
            userId: options.userId,
            userType: options.userType,
            customTags: {
                model_requested: options.model,
                streaming: true,
                ...options.customTags
            }
        });

        const modelToUse = this.getBestModel(options.model);
        metadata.tags.model_used = modelToUse;

        const extraHeaders = aiMetadataHelper.generateHeaders({
            ...options,
            operationType: 'streaming_completion'
        });

        aiMetadataHelper.logMetadata(metadata, 'info');

        return await this.client.chat.completions.create({
            model: modelToUse,
            messages,
            temperature: options.temperature || 0.7,
            max_tokens: options.max_tokens || 1000,
            stream: true,
            ...options
        }, {
            headers: extraHeaders
        });
    }

    /**
     * ScrapeGPT compatible method with metadata
     */
    async getChatGPTResponse(systemPrompt, userPrompt, options = {}) {
        const messages = [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
        ];

        return await this.createChatCompletion(messages, {
            useCase: 'scrape_analysis',
            feature: 'content_analysis',
            operationType: 'scrape_gpt',
            temperature: 1,
            presence_penalty: 0,
            top_p: 1,
            max_tokens: 256,
            customTags: {
                prompt_type: 'scrape_analysis',
                system_prompt_length: systemPrompt.length,
                user_prompt_length: userPrompt.length
            },
            ...options
        });
    }

    /**
     * Factory method compatible with existing patterns
     */
    async completionFactory(type, data, options = {}) {
        const metadata = metadataGenerators.centralizedAI({
            useCase: 'factory_completion',
            feature: 'template_completion',
            operationType: 'factory_completion',
            customTags: {
                factory_type: type,
                data_size: JSON.stringify(data).length,
                template_type: type
            },
            ...options
        });

        // This would integrate with your existing template system
        // For now, we'll create a simple completion
        const messages = [
            {
                role: 'system',
                content: `You are processing a ${type} request. Analyze the provided data and respond appropriately.`
            },
            {
                role: 'user',
                content: typeof data === 'string' ? data : JSON.stringify(data)
            }
        ];

        return await this.createChatCompletion(messages, {
            ...options,
            useCase: 'factory_completion',
            feature: type,
            customTags: metadata.tags
        });
    }

    /**
     * Health check with metadata
     */
    async getHealthStatus() {
        const metadata = metadataGenerators.centralizedAI({
            operationType: 'health_check',
            feature: 'system_health'
        });

        try {
            const response = await axios.get(`${this.proxyUrl}/health`, {
                headers: aiMetadataHelper.generateHeaders({
                    operationType: 'health_check'
                }),
                timeout: 10000
            });

            aiMetadataHelper.logMetadata({
                ...metadata,
                status: 'healthy',
                proxy_status: response.status
            }, 'info');

            return {
                status: 'healthy',
                proxy: response.data,
                models_available: this.availableModels.length,
                metadata
            };
        } catch (error) {
            aiMetadataHelper.logMetadata({
                ...metadata,
                status: 'unhealthy',
                error: error.message
            }, 'error');

            return {
                status: 'unhealthy',
                error: error.message,
                metadata
            };
        }
    }

    /**
     * Get available models with metadata
     */
    async getAvailableModels() {
        await this.initializeModels();
        
        const metadata = metadataGenerators.centralizedAI({
            operationType: 'models_list',
            feature: 'model_discovery'
        });

        aiMetadataHelper.logMetadata({
            ...metadata,
            models_count: this.availableModels.length
        }, 'info');

        return this.availableModels;
    }
}

// Create singleton instance
const litellmService = new LiteLLMService();

module.exports = {
    LiteLLMService,
    litellmService
};
