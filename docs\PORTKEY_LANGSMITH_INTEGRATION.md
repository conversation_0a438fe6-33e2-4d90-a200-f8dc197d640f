# Portkey + LangSmith Integration Guide

This document provides a comprehensive guide for the Portkey AI Gateway and LangSmith integration in the SellerBot application.

## Overview

The integration combines:
- **Portkey AI Gateway**: Provides AI infrastructure with features like caching, rate limiting, fallbacks, and cost tracking
- **LangSmith**: Offers comprehensive tracing, monitoring, and debugging for AI applications

## Features

### Portkey AI Gateway
- ✅ **Multi-provider support**: OpenAI, Azure OpenAI, Anthropic, and more
- ✅ **Intelligent caching**: Semantic caching to reduce costs and latency
- ✅ **Rate limiting**: Automatic rate limiting and quota management
- ✅ **Fallback strategies**: Automatic failover between providers
- ✅ **Cost tracking**: Real-time cost monitoring and budget alerts
- ✅ **Request/Response logging**: Comprehensive logging for debugging

### LangSmith Integration
- ✅ **Distributed tracing**: End-to-end tracing of AI requests
- ✅ **Performance monitoring**: Latency, token usage, and error tracking
- ✅ **Request debugging**: Detailed request/response inspection
- ✅ **Custom metadata**: Rich context for each AI operation

## Setup Instructions

### 1. Environment Variables

Add the following environment variables to your `.env` file:

```bash
# Portkey AI Gateway Configuration
PORTKEY_API_KEY=your_portkey_api_key_here
PORTKEY_BASE_URL=https://api.portkey.ai/v1
PORTKEY_VIRTUAL_KEY_OPENAI=your_openai_virtual_key_here
PORTKEY_VIRTUAL_KEY_AZURE=your_azure_virtual_key_here

# LangSmith Configuration
LANGSMITH_API_KEY=your_langsmith_api_key_here
LANGSMITH_PROJECT=SellerBot
LANGSMITH_TRACING=true

# Optional Configuration
PORTKEY_LOGGING=true
PORTKEY_RATE_LIMITING=true
PORTKEY_COST_TRACKING=true
```

### 2. Getting API Keys

#### Portkey Setup
1. Visit [Portkey Dashboard](https://app.portkey.ai/)
2. Create an account and get your API key
3. Create virtual keys for your AI providers (OpenAI, Azure, etc.)
4. Configure your virtual keys in the environment variables

#### LangSmith Setup
1. Visit [LangSmith](https://smith.langchain.com/)
2. Create an account and get your API key
3. Create a project (or use the default "SellerBot")
4. Add the API key to your environment variables

### 3. Installation

The required packages are already installed:
```bash
npm install portkey-ai langsmith
```

## Usage Examples

### Basic Chat Completion

```javascript
const PortkeyLangSmithWrapper = require('./services/ai/portkeyLangsmithWrapper');

const wrapper = new PortkeyLangSmithWrapper('chat');

const messages = [
  {
    role: 'system',
    content: 'You are a helpful assistant.'
  },
  {
    role: 'user',
    content: 'What are the best practices for Amazon SEO?'
  }
];

const response = await wrapper.createChatCompletion(messages, {
  temperature: 0.7,
  max_tokens: 200,
});

console.log(response.choices[0].message.content);
```

### Assistant API

```javascript
const { getAssistantResponse } = require('./services/scrapeGPT/assistant');

const response = await getAssistantResponse(
  'asst_your_assistant_id',
  JSON.stringify({ company: 'Example Corp' }),
  true // Use Portkey
);

console.log(response.message);
```

### Legacy Function Integration

```javascript
const { getChatGPTResponse } = require('./services/scrapeGPT/request');

const response = await getChatGPTResponse(
  'You are an expert analyst.',
  'Analyze this data...',
  true // Use Portkey
);

console.log(response.message);
```

## API Endpoints

The integration includes several demo API endpoints:

### Chat Completion
```
POST /api/portkey/chat
```

### Assistant API
```
POST /api/portkey/assistant
```

### Text Analysis
```
POST /api/portkey/analyze
```

### Batch Processing
```
POST /api/portkey/batch
```

### Usage Statistics
```
GET /api/portkey/stats
```

## Configuration Options

### Use Case Configurations

The wrapper supports different configurations for different use cases:

- **chat**: General chat completions
- **assistant**: OpenAI Assistant API calls
- **azure**: Azure OpenAI specific configuration
- **analysis**: Text analysis with low temperature

### Model Configurations

```javascript
// Example model configurations
const modelConfigs = {
  chat: {
    provider: 'openai',
    model: 'gpt-4o',
    temperature: 0.7,
    maxTokens: 1000,
  },
  analysis: {
    provider: 'openai',
    model: 'gpt-4o-mini',
    temperature: 0.1,
    maxTokens: 500,
  }
};
```

## Monitoring and Observability

### LangSmith Tracing

Every AI request is automatically traced in LangSmith with:
- Request inputs and outputs
- Token usage and costs
- Latency measurements
- Error tracking
- Custom metadata

### Portkey Analytics

Access comprehensive analytics through Portkey:
- Request volume and patterns
- Cost breakdown by model/provider
- Error rates and types
- Performance metrics

## Error Handling and Fallbacks

The integration includes robust error handling:

1. **Automatic Fallbacks**: If Portkey fails, requests automatically fall back to direct OpenAI
2. **Retry Logic**: Configurable retry attempts for transient failures
3. **Error Logging**: Comprehensive error logging for debugging

## Testing

### Run Example Scripts

```bash
# Run all examples
node examples/portkeyLangsmithExamples.js

# Run specific examples
node -e "require('./examples/portkeyLangsmithExamples').exampleChatCompletion()"
```

### Test API Endpoints

Use the Swagger UI at `/api-docs` to test the Portkey demo endpoints.

## Best Practices

1. **Use Appropriate Use Cases**: Choose the right configuration for your specific use case
2. **Monitor Costs**: Regularly check Portkey analytics for cost optimization
3. **Set Rate Limits**: Configure appropriate rate limits to avoid quota exhaustion
4. **Enable Caching**: Use semantic caching for repeated similar requests
5. **Monitor Traces**: Regularly review LangSmith traces for performance optimization

## Troubleshooting

### Common Issues

1. **Missing API Keys**: Ensure all required environment variables are set
2. **Virtual Key Configuration**: Verify virtual keys are properly configured in Portkey
3. **Network Issues**: Check firewall settings for Portkey and LangSmith endpoints
4. **Rate Limiting**: Monitor rate limits and adjust configuration as needed

### Debug Mode

Enable debug logging:
```bash
PORTKEY_LOG_LEVEL=debug
PORTKEY_LOG_REQUESTS=true
PORTKEY_LOG_RESPONSES=true
```

## Support

For issues and questions:
- Portkey Documentation: [docs.portkey.ai](https://docs.portkey.ai)
- LangSmith Documentation: [docs.smith.langchain.com](https://docs.smith.langchain.com)
- Internal Support: Check the `#ai-integration` Slack channel
