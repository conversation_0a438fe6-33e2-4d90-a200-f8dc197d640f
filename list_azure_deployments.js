#!/usr/bin/env node

/**
 * Script to list Azure OpenAI deployments
 */

require('dotenv').config();
const axios = require('axios');
const { centralizedAI } = require('./services/ai/centralizedAIService');

function printEnvVariables() {
    console.log('=== Environment Variables ===');
    console.log(`OPENAI_API_KEY: ${process.env.OPENAI_API_KEY ? process.env.OPENAI_API_KEY.substring(0, 20) + '...' : 'NOT SET'}`);
    console.log(`AZURE_OPENAI_ENDPOINT: ${process.env.AZURE_OPENAI_ENDPOINT || 'NOT SET'}`);
    console.log(`AZURE_OPENAI_API_VERSION: ${process.env.AZURE_OPENAI_API_VERSION || 'NOT SET'}`);
    console.log(`PORTKEY_BASE_URL: ${process.env.PORTKEY_BASE_URL || 'NOT SET'}`);
    console.log(`OPENAI_MODEL_ID: ${process.env.OPENAI_MODEL_ID || 'NOT SET'}`);
    console.log('');
}

async function listAzureDeployments() {
    console.log('=== Azure OpenAI Deployments ===');

    const openaiApiKey = process.env.OPENAI_API_KEY;
    const azureEndpoint = process.env.AZURE_OPENAI_ENDPOINT;
    const azureApiVersion = process.env.AZURE_OPENAI_API_VERSION;
    
    console.log(`Azure Endpoint: ${azureEndpoint}`);
    console.log(`Azure API Version: ${azureApiVersion}`);
    console.log(`API Key: ${openaiApiKey ? '*'.repeat(20) : 'NOT SET'}`);
    console.log('');
    
    if (!openaiApiKey || !azureEndpoint || !azureApiVersion) {
        console.log('❌ Missing required Azure OpenAI environment variables!');
        return;
    }
    
    // List deployments endpoint - ensure proper URL construction
    const baseUrl = azureEndpoint.endsWith('/') ? azureEndpoint : azureEndpoint + '/';
    const url = `${baseUrl}openai/deployments?api-version=${azureApiVersion}`;
    
    const headers = {
        'api-key': openaiApiKey
    };
    
    try {
        console.log('🔄 Fetching deployments...');
        console.log(`URL: ${url}`);
        
        const response = await axios.get(url, { 
            headers,
            timeout: 30000,
            validateStatus: () => true
        });
        
        console.log(`Status Code: ${response.status}`);
        
        if (response.status === 200) {
            console.log('✅ Successfully retrieved deployments!');
            console.log('\nDeployments:');
            console.log(JSON.stringify(response.data, null, 2));
            
            if (response.data.data && Array.isArray(response.data.data)) {
                console.log('\n📋 Available Deployment Names:');
                response.data.data.forEach((deployment, index) => {
                    console.log(`${index + 1}. ${deployment.id} (Model: ${deployment.model}, Status: ${deployment.status})`);
                });
            }
        } else {
            console.log(`❌ Failed to retrieve deployments with status ${response.status}:`);
            console.log(JSON.stringify(response.data, null, 2));
        }
        
    } catch (error) {
        if (error.response) {
            console.log(`❌ Request failed with status ${error.response.status}:`);
            console.log(JSON.stringify(error.response.data, null, 2));
        } else {
            console.log(`❌ Request failed: ${error.message}`);
        }
    }
}

async function testCommonDeploymentNames() {
    console.log('\n=== Testing Common Deployment Names ===');
    
    const commonNames = [
        'gpt-4o',
        'gpt-4o-mini',
        'gpt-4',
        'gpt-35-turbo',
        'gpt-3.5-turbo',
        'text-davinci-003'
    ];
    
    const openaiApiKey = process.env.OPENAI_API_KEY;
    const azureEndpoint = process.env.AZURE_OPENAI_ENDPOINT;
    const azureApiVersion = process.env.AZURE_OPENAI_API_VERSION;
    
    for (const deploymentName of commonNames) {
        const url = `${azureEndpoint}openai/deployments/${deploymentName}/chat/completions?api-version=${azureApiVersion}`;
        
        const headers = {
            'Content-Type': 'application/json',
            'api-key': openaiApiKey
        };
        
        const data = {
            messages: [
                { role: 'user', content: 'Hello' }
            ],
            max_tokens: 5
        };
        
        try {
            console.log(`🔄 Testing deployment: ${deploymentName}`);
            const response = await axios.post(url, data, { 
                headers,
                timeout: 10000,
                validateStatus: () => true
            });
            
            if (response.status === 200) {
                console.log(`✅ ${deploymentName} - WORKS!`);
            } else if (response.status === 404) {
                console.log(`❌ ${deploymentName} - Not found`);
            } else {
                console.log(`⚠️  ${deploymentName} - Status ${response.status}: ${response.data?.error?.message || 'Unknown error'}`);
            }
            
        } catch (error) {
            if (error.response?.status === 404) {
                console.log(`❌ ${deploymentName} - Not found`);
            } else {
                console.log(`⚠️  ${deploymentName} - Error: ${error.message}`);
            }
        }
    }
}

async function testCentralizedAIService() {
    console.log('\n=== Testing Centralized AI Service ===');

    try {
        // Test health status
        const healthStatus = centralizedAI.getHealthStatus();
        console.log('🔍 Health Status:');
        console.log(JSON.stringify(healthStatus, null, 2));

        // Test chat completion with Portkey
        console.log('\n🔄 Testing chat completion with Portkey...');
        const messages = [
            { role: 'system', content: 'You are a helpful assistant.' },
            { role: 'user', content: 'Say "Hello from Centralized AI Service!" and nothing else.' }
        ];

        const result = await centralizedAI.createChatCompletion(messages, {
            temperature: 0,
            max_tokens: 50,
        }, 'azure', true);

        console.log('✅ Centralized AI Service test successful!');
        console.log(`🤖 Response: ${result.message}`);
        console.log(`⚡ Duration: ${result.duration}ms`);
        console.log(`🔧 Provider: ${result.provider}`);
        console.log(`📊 Usage:`, result.usage || 'No usage data');

        return true;
    } catch (error) {
        console.log('❌ Centralized AI Service test failed:', error.message);
        return false;
    }
}

async function main() {
    printEnvVariables();
    await listAzureDeployments();
    await testCommonDeploymentNames();
    await testCentralizedAIService();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { listAzureDeployments, testCommonDeploymentNames, testCentralizedAIService };
