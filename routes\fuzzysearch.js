const fs = require("fs");
const csv = require("csv-parser");
const Fuse = require("fuse.js");
const { parse } = require("json2csv");
const express = require("express");
const multer = require("multer");
const { adminAuth } = require("../middlewares/jwt");
const upload = multer({ dest: "uploads/" });

const router = express.Router();

const inputSchemaMap = {
  business_name: [
    "Business Name",
    "business_name",
    "Company Name",
    "Name",
    "name",
  ],
  seller_name: ["Name", "Seller Name", "seller_name"],
  company_address: ["Company Address", "Address", "company_address"],
  seller_url: [
    "SellerCountryMatching - Amazon Seller → Seller URL",
    "Seller URL",
    "seller_url",
  ],
};

const outputSchemaMap = {
  matched_company_name: ["Company Name", "company_name"],
  business_name: ["Business Name", "business_name"],
  email: ["Email", "eMail", "email"],
  linkedin_link: ["LinkedIn Link", "LinkedIn URL", "linkedin_url"],
  company_website_full: ["Company Website Full", "Website", "website"],
  company_phone_number: ["Company Phone Number", "Phone #", "phone"],
};
function mapRow(row, schemaMap) {
  const mappedRow = {};
  for (const key in schemaMap) {
    for (const possibleHeader of schemaMap[key]) {
      if (row[possibleHeader]) {
        mappedRow[key] = row[possibleHeader];
        break;
      }
    }
  }
  return mappedRow;
}

async function readCSV(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on("data", (data) => results.push(data))
      .on("end", () => resolve(results))
      .on("error", (error) => reject(error));
  });
}

async function fuzzyMatchAndMap(inputCSV, outputCSV) {
  const rawInputData = await readCSV(inputCSV);
  const rawOutputData = await readCSV(outputCSV);

  const inputData = rawInputData.map((row) => mapRow(row, inputSchemaMap));
  const outputData = rawOutputData.map((row) => mapRow(row, outputSchemaMap));

  const fuse = new Fuse(outputData, {
    keys: ["matched_company_name", "business_name"], // Search on both fields
    includeScore: true,
    threshold: 0.3,
  });

  // Store best match for each company
  const bestMatches = {};

  inputData.forEach((inputRow) => {
    const matches = fuse.search(inputRow.business_name);
    if (matches.length > 0) {
      const bestMatch = matches.reduce((prev, current) =>
        prev.score < current.score ? prev : current
      );
      bestMatches[inputRow.business_name] = bestMatch;
    }
  });

  const mappedResults = inputData.map((inputRow) => {
    const match = bestMatches[inputRow.business_name];

    if (match) {
      return {
        OriginalInputBusinessName: inputRow.business_name,
        SellerName: inputRow.seller_name,
        CompanyAddress: inputRow.company_address,
        SellerURL: inputRow.seller_url,
        FuzzyScore: (1 - match.score).toFixed(4), // Higher score means better match
        ...match.item,
      };
    } else {
      return {
        OriginalInputBusinessName: inputRow.business_name,
        SellerName: inputRow.seller_name,
        CompanyAddress: inputRow.company_address,
        SellerURL: inputRow.seller_url,
        FuzzyScore: "No Match",
      };
    }
  });

  return parse(mappedResults);
}
/**

/**
 * @swagger
 * /api/fuzzy-search:
 *   post:
 *     summary: Perform fuzzy matching between two CSV files
 *     description: |
 *       Takes two CSV files (input and output) and performs fuzzy matching based on business names, returning a mapped CSV file.
 *
 *       The process:
 *       1. Reads and parses both input and output CSV files
 *       2. Maps columns from both files to standardized field names
 *       3. Performs fuzzy matching on business names with a threshold of 0.3
 *       4. For each input row, finds the best match from the output data
 *       5. Returns a CSV with original input data plus matched fields from output data
 *     tags:
 *       - Fuzzy Search
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - inputCSV
 *               - outputCSV
 *             properties:
 *               inputCSV:
 *                 type: string
 *                 format: binary
 *                 description: |
 *                   Input CSV([see sample](/examples/input_fuzzy.csv)) file containing business names to match. The file should include one or more of these columns:
 *                   - "Business Name" or "business_name" or "Company Name" or "Name" or "name": Name of the business to match (required)
 *                   - "Name" or "Seller Name" or "seller_name": Name of the seller
 *                   - "Company Address" or "Address" or "company_address": Address of the company
 *                   - "Seller URL" or "seller_url": URL of the seller's website
 *
 *                   Example input CSV:
 *                   Business Name,Seller Name,Company Address,Seller URL
 *                   Acme Corporation,John Smith,123 Main St,https://acme.com
 *                   XYZ Industries,Jane Doe,456 Oak Ave,https://xyz-ind.com
 *               outputCSV:
 *                 type: string
 *                 format: binary
 *                 description: |
 *                   Output CSV([see sample](/examples/output_fuzzy.csv)) file containing the database to match against. The file should include one or more of these columns:
 *                   - "Company Name" or "company_name": Name of the company to match against
 *                   - "Business Name" or "business_name": Alternative business name
 *                   - "Email" or "eMail" or "email": Company email address
 *                   - "LinkedIn Link" or "LinkedIn URL" or "linkedin_url": LinkedIn profile URL
 *                   - "Company Website Full" or "Website" or "website": Company website
 *                   - "Company Phone Number" or "Phone #" or "phone": Company phone number
 *
 *                   Example output CSV:
 *                   Company Name,Business Name,Email,LinkedIn Link,Company Website Full,Company Phone Number
 *                   Acme Corp,Acme Corporation,<EMAIL>,https://linkedin.com/company/acme,https://acme.com,************
 *                   XYZ Inc,XYZ Industries,<EMAIL>,https://linkedin.com/company/xyz,https://xyz.com,************
 *     responses:
 *       200:
 *         description: Successfully matched and mapped CSV file
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 *             example: |
 *               OriginalInputBusinessName,SellerName,CompanyAddress,SellerURL,FuzzyScore,matched_company_name,business_name,email,linkedin_link,company_website_full,company_phone_number
 *               Acme Corporation,John Smith,123 Main St,https://acme.com,0.9200,Acme Corp,Acme Corporation,<EMAIL>,https://linkedin.com/company/acme,https://acme.com,************
 *               XYZ Industries,Jane Doe,456 Oak Ave,https://xyz-ind.com,0.8500,XYZ Inc,XYZ Industries,<EMAIL>,https://linkedin.com/company/xyz,https://xyz.com,************
 *               ABC Company,Bob Johnson,789 Pine Rd,https://abc.co,No Match,,,,,,
 *         headers:
 *           Content-Disposition:
 *             schema:
 *               type: string
 *             description: Attachment header with filename
 *             example: attachment; filename="mapped_output.csv"
 *           Content-Type:
 *             schema:
 *               type: string
 *             example: text/csv
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 */

router.post(
  "/api/fuzzy-search",
  adminAuth,
  upload.fields([{ name: "inputCSV" }, { name: "outputCSV" }]),
  async (req, res) => {
    try {
      const inputCSVPath = req.files["inputCSV"][0].path;
      const outputCSVPath = req.files["outputCSV"][0].path;

      const csvOutput = await fuzzyMatchAndMap(inputCSVPath, outputCSVPath);

      res.header("Content-Type", "text/csv");
      res.attachment("mapped_output.csv");
      return res.send(csvOutput);
    } catch (error) {
      console.error("Error processing files:", error);
      res.status(500).send("Error processing files");
    }
  }
);

module.exports = router;
