/**
 * maintainAnalytics.js
 * 
 * Utility script for maintaining analytics tables and views in the database.
 * - Allows refreshing the materialized view mv_email_with_thread_start
 */

require("dotenv").config({ path: "../.env" });
const prisma = require("../database/prisma/getPrismaClient");

// Command-line argument processing
const args = process.argv.slice(2);
const VALID_MODES = ['refreshEmailsMV'];
const mode = args[0];

/**
 * Refreshes the mv_email_with_thread_start materialized view
 */
async function refreshEmailsWithThreadStartMV() {
  console.log("🔄 Refreshing the mv_email_with_thread_start materialized view...");

  try {
    // Execute the SQL to refresh the materialized view
    await prisma.$executeRawUnsafe(
      `REFRESH MATERIALIZED VIEW mv_email_with_thread_start`
    );
    console.log("✅ Successfully refreshed the mv_email_with_thread_start materialized view!");
  } catch (error) {
    console.error("❌ Error refreshing the materialized view:", error);
    throw error;
  }
}

/**
 * Main function to run the script based on the chosen mode
 */
async function main() {
  // Validate the mode argument
  if (!mode || !VALID_MODES.includes(mode)) {
    console.error(`Error: Please provide a valid mode. Supported modes: ${VALID_MODES.join(', ')}`);
    process.exit(1);
  }

  try {
    // Execute the selected mode
    switch (mode) {
      case 'refreshEmailsMV':
        await refreshEmailsWithThreadStartMV();
        break;
      default:
        console.error(`Invalid mode: ${mode}`);
        process.exit(1);
    }

    console.log("✅ Operation completed successfully");
    process.exit(0);
  } catch (error) {
    console.error("❌ Error running the script:", error);
    process.exit(1);
  } finally {
    // Clean up the database connection
    await prisma.$disconnect();
  }
}

// Execute the main function
main();