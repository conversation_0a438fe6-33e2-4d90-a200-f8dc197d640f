const { cleanEmail, isValidEmail, extractEmailsFromText } = require('./lib/utils/emailUtils');
const cheerio = require('cheerio');

console.log('Email Regex Test');
console.log('================');

// Test data - various email formats including problematic ones
const testEmails = [
  '<EMAIL>',
  '<EMAIL>',
  '-<EMAIL>',
  '<EMAIL>', // Test the broken email with capitals after domain
  '<EMAIL>', // Test the broken email from CSV
  '<EMAIL>',
  'contact (at) domain (dot) com',
  '<NAME_EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  'invalid@domain',
  'not@<EMAIL>',
  '<EMAIL>',
  'aaaaaaaa@domaincom',
  ' <EMAIL> ',
  'email@123.123.123.123',
  '<EMAIL>',
  '<EMAIL>' // Test handling of uppercase domain
];

console.log('\nCleaning and validating individual emails:');
console.log('----------------------------------------');

testEmails.forEach(email => {
  const cleaned = cleanEmail(email);
  const isValid = isValidEmail(cleaned);
  console.log(`Original: ${email}`);
  console.log(`Cleaned: ${cleaned}`);
  console.log(`Valid: ${isValid ? 'YES' : 'NO'}`);
  console.log('---');
});

// Test the HTML extraction
const testHtml = `
<html>
<body>
  <p>Contact <NAME_EMAIL> or <EMAIL>.</p>
  <p>Email with artifact: -<EMAIL></p>
  <p>Malformed email: not@<EMAIL></p>
  <p>Contact: contact (at) domain (dot) com</p>
  <p>Email with spaces: <NAME_EMAIL></p>
  <a href="mailto:<EMAIL>">Email Us</a>
</body>
</html>
`;

console.log('\nExtracting emails from HTML:');
console.log('---------------------------');
const $ = cheerio.load(testHtml);
const extractedEmails = extractEmailsFromText(testHtml, 'https://test.com');
console.log('Extracted emails:', extractedEmails);

// Run the script
if (require.main === module) {
  // Additional test - check specific email that was problematic
  const problematicEmail = '-<EMAIL>';
  const cleanedProblematic = cleanEmail(problematicEmail);
  console.log('\nSpecific test for problematic email:');
  console.log(`Original: ${problematicEmail}`);
  console.log(`Cleaned: ${cleanedProblematic}`);
  console.log(`Valid: ${isValidEmail(cleanedProblematic) ? 'YES' : 'NO'}`);
} 