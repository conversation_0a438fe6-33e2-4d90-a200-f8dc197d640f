I will give you two sets of data as input:

1. Company data: You will get a single text string that will have 3 things -
   [ "Brand name of the company", "Legal business name of the company", "Address" ]
2. Website data: You will get two text strings here, which are -
   a) Website URL
   b) Text content from the website

Your job is to tell me if the company (for which the company data is given) owns the website or not.
If it owns the website, then say 'True'
If not, then say 'False'

Note before you start: Whenever I say match, I don't mean an exact match. It just needs to be from the same company. Sometimes it will be a partial match or broad match. For example: In address matching one address might not have the pin code and street number but on the basis of the state, country and area you know this is the same for sure. Then you would consider that as a mytch here.
Example in term of company name - Sometimes the business name would have additional things like LLC, Pvt., etc. but from the name in the URL you know for sure that this is the same company then it is a match.

Steps to figure out the answer -
Step 1: read and understand the company data
Step 2: See the URL and see if it matches the brand name/business name of the company
Step 3: Go through the entire text content word by word and understand it very well.
Step 4: Extract all the company names & addresses from the text content
Step 5: See if the data extracted in Step 4 matches the company data in Step 1 or not. The match doesn't have to be an exact match, it can be a partial match as well.
Step 6: Make sure that the company owns the website. Sometimes the name & address are present in the text content but that does not mean the company owns the website. Some examples for this -

1. Directory of companies where the company is listed
2. Database of companies
3. A marketplace where the company's products are is listed
4. A case study for another company that this company might be a client for
5. A trading platform this company is listed on
6. A Social media platform & it's the company's profile
7. A booking platform where the company is selling something
8. Much more
   Hence you need to make sure the company owns the website.

The final answer is 'True' only and only if -

1. Any one thing matches from step 2 & step 5
2. From step 6 you are 100% the company actually owns the website.

Rules to remember:

- Only respond with 'True' or 'False'
- Only respond with True if you're 100% sure about both the questions
- No explanations needed
- No apologies or uncertainty statements

Example:
Input -
Company data: ["True North Publishing", "The Starboard Group LLC"]
Website data:
URL - "https://truenorthpublishing.com"
Text Content - "Welcome to the leading publisher for independent authors, True North Publishing. We specialize in providing quality publishing services."

Output - 'True'
