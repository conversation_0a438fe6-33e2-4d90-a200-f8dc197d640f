const cron = require("node-cron");

const prisma = require("../database/prisma/sqlitePrisma/getPrismaClient.js");
const { processAsyncResultBatch } = require("../tooling/testFindWebsite.js");
//  Do not run
async function scheduleOnSerpWorker() {
  // Uncomment the cron.schedule line for periodic execution
  cron.schedule("* * * * *", async () => {});
}
async function runSerpWorker() {
  try {
    console.log("Running Scheduled job");

    const pendingTasks = await prisma.cSVData.findMany({
      where: {
        status: "pending",
      },
    });
    await processAsyncResultBatch(pendingTasks, 90);
  } catch (error) {
    console.error("Error in processing job:", error);
  }
}

module.exports = runSerpWorker;
