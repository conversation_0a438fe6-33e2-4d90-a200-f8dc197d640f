/**
 * Centralized configuration constants for email extraction service
 */

/**
 * URL patterns to skip when crawling websites
 * These are paths that typically don't contain contact information
 */
const SKIP_URL_PATTERNS = [
  '/products',
  '/items',
  '/product',
  '/shop',
  '/cart',
  '/order',
  '/checkout',
  '/login',
  '/register',
  '/dashboard',
  '/admin',
  '/wp-admin',
  '/wp-content',
  '/wp-includes',
  '/api',
  '/feed',
  '/rss',
  '/media',
  '/uploads',
  '/images',
  '/img',
  '/js',
  '/css',
  '/assets',
  '/static',
  '/blog',
  '/blogs',
  '/blog-posts',
  '/blog-post',
  '/blog-posts',
  '/collections',
  '/collection',
  '/collection-page',
  '/collection-pages',
  '/collection-page',
  '.zip',
  '.pdf',
  '.doc',
  '.docx',
  '.xls',
  '.xlsx',
  '.ppt',
  '.pptx',
  '.txt',
  '.csv',
  '.json',
  '.xml',
  '.yaml',
  '.dmg',
];

/**
 * Keywords that indicate relevant pages for email extraction
 */
const RELEVANT_KEYWORDS = [
  'about',
  'contact',
  'privacy',
  'terms',
  'policy',
  'team',
  'staff',
  'support',
  'help',
  'faq',
  'inquiry',
  'enquiry'
];

/**
 * Default rate limiting options
 */
const RATE_LIMITING = {
  maxConcurrent: 25,              // Maximum concurrent requests
  minTime: 0,                     // Minimum time between requests (ms)
  reservoirRefreshAmount: 100,    // Number of requests allowed per interval
  reservoirRefreshInterval: 10000 // Refresh interval (ms)
};

/**
 * Proxy-specific configuration
 */
const PROXY_CONFIG = {
  maxPages: 10,  // Maximum pages to crawl when using proxy (to limit API usage costs)
  timeout: 45000 // Longer timeout for proxy requests (ms)
};

/**
 * Blacklisted email domains that should be ignored
 * These are common placeholder or example domains
 */
const EMAIL_BLACKLIST_DOMAINS = [
  'example.com',
  'email.com',
  'domain.com',
  'yourdomain.com',
  'yoursite.com',
  'youremail.com',
  'companyemail.com',
  'site.com',
  'test.com',
  'sample.com',
  'placeholder.com',
  'mysite.com',
  'company.com',
  'mailexample.com',
  'emailaddress.com',
  'somecompany.com',
  'somedomain.com'
];

/**
 * Default extraction options
 */
const DEFAULT_OPTIONS = {
  maxEmails: 20,                     // Maximum emails to extract per website
  maxPages: 10,                     // Maximum pages to crawl per website
  maxProxyPages: PROXY_CONFIG.maxPages, // Maximum pages when using proxy
  useProxy: true,                    // Whether to use proxy as fallback
  saveHtml: false,                   // Save HTML files for debugging
  skipUrlPatterns: SKIP_URL_PATTERNS,
  relevantKeywords: RELEVANT_KEYWORDS,
  blacklistedDomains: EMAIL_BLACKLIST_DOMAINS, // Blacklisted email domains
  ...RATE_LIMITING                   // Include rate limiting options
};

module.exports = {
  SKIP_URL_PATTERNS,
  RELEVANT_KEYWORDS,
  RATE_LIMITING,
  PROXY_CONFIG,
  EMAIL_BLACKLIST_DOMAINS,
  DEFAULT_OPTIONS
}; 