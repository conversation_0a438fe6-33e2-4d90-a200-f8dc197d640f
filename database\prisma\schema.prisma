// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        Int      @id @default(autoincrement())
  name      String
  email     String   @unique
  password  String
  jobs      Job[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// input job
model Job {
  id         Int          @id @default(autoincrement())
  status     JobStatus
  name       String
  user       User         @relation(fields: [userId], references: [id])
  userId     Int
  Domain     Seller[]
  OutputData OutputData[]
  createdAt  DateTime     @default(now())
  updatedAt  DateTime     @updatedAt
}

model Seller {
  id         Int          @id @default(autoincrement())
  name       String       @unique()
  userId     Int
  jobId      Int
  htmlData   String       @default("")
  status     JobStatus
  OutputData OutputData[]
  Job        Job          @relation(fields: [jobId], references: [id])
}

model OutputData {
  id            Int         @id @default(autoincrement())
  sellerName    String
  sellerId      Int
  sellerUrl     String      @default("")
  inputStatus   InputStatus
  contactNumber String      @default("")
  sellerEmail   String      @default("")
  sellerAddress String      @default("")
  sellerPincode String      @default("")
  jobId         Int
  scraped_html  String?     @db.Text
  inputData     Json        @default("{}")
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  seller        Seller      @relation(fields: [sellerId], references: [id])
  job           Job         @relation(fields: [jobId], references: [id])
}

// Enums for representing status
enum InputStatus {
  error
  success
  pending
}

enum SubmitStatus {
  error
  success
  pending
}

enum JobStatus {
  pending
  in_progress
  completed
  failed
}

model Company {
  id                     Int        @id @default(autoincrement())
  name                   String     @default("")
  amazon_seller_id       String     @default("")
  primary_category_id    String     @default("")
  primary_category       String     @default("")
  primary_sub_category   String     @default("")
  estimate_sales         String     @default("")
  derived_estimate_sales Float      @default(0.0)
  percent_fba            String     @default("")
  number_winning_brands  String     @default("")
  number_asins           String     @default("")
  number_top_asins       String     @default("")
  state                  String     @default("")
  country                String     @default("")
  business_name          String     @default("")
  number_brands_1000     String     @default("")
  mom_growth             String     @default("")
  mom_growth_count       String     @default("")
  started_selling_date   String     @default("")
  smartscout_country     String     @default("")
  website                String     @default("")
  domain                 String     @default("")
  website_status         String     @default("")
  find_website_batch     String     @default("")
  lookup_source          String     @default("")
  lookup_sources         Json       @default("{}")
  employee_count         String     @default("")
  company_linkedin       String     @default("")
  company_twitter        String     @default("")
  company_fb             String     @default("")
  company_location       String     @default("")
  company_address        String     @default("")
  company_pincode        String     @default("")
  prospects              Prospect[]
  createdAt              DateTime   @default(now())
  updatedAt              DateTime   @updatedAt

  @@index([amazon_seller_id])
  @@index([domain])
  @@index([smartscout_country])
  @@index([website_status])
  @@index([lookup_sources], type: Gin)
}

model Prospect {
  prospect_id         Int                        @id @default(autoincrement())
  company_id          Int
  amazon_seller_id    String                     @default("")
  website             String                     @default("")
  domain              String                     @default("")
  person_name         String                     @default("")
  person_linkedin     String                     @default("")
  phone               String                     @default("")
  email               String                     @default("")
  job_title           String                     @default("")
  contact_location    String                     @default("")
  apollo_company_name String                     @default("")
  apollo_website      String                     @default("")
  smartscout_country  String                     @default("")
  source              String                     @default("")
  sources             Json                       @default("{}")
  address             String                     @default("")
  pincode             String                     @default("")
  email_status        String                     @default("")
  // MX Record tracking fields
  emailProvider       String                     @default("")
  mxRecords           Json                       @default("{}")
  lastProcessAt       DateTime                   @default(now())
  nextProcessAt       DateTime                   @default(now())
  company             Company                    @relation(fields: [company_id], references: [id])
  clientsProspects    ClientsProspectsMatching[]
  createdAt           DateTime                   @default(now())
  updatedAt           DateTime                   @updatedAt

  @@index([person_linkedin])
  @@index([email])
  @@index([amazon_seller_id])
  @@index([phone])
  @@index([nextProcessAt])
  @@index([sources], type: Gin)
}

model AmazonProspect {
  prospect_id      Int                        @id @default(autoincrement())
  person_name      String                     @default("")
  person_linkedin  String                     @default("")
  email            String                     @default("")
  job_title        String                     @default("")
  source           String                     @default("")
  sources          Json                       @default("{}")
  email_status     String                     @default("")
  // MX Record tracking fields
  emailProvider    String                     @default("")
  mxRecords        Json                       @default("{}")
  lastProcessAt    DateTime                   @default(now())
  nextProcessAt    DateTime                   @default(now())
  seller_id        Int?
  amazonSeller     AmazonSeller?              @relation(fields: [seller_id], references: [id])
  createdAt        DateTime                   @default(now())
  updatedAt        DateTime                   @updatedAt

  @@index([person_linkedin])
  @@index([email])
  @@index([nextProcessAt])
  @@index([sources], type: Gin)
}

model ClientsProspectsMatching {
  match_id           Int      @id @default(autoincrement())
  client             String
  prospect_id        Int
  amazon_seller_id   String   @default("")
  date_reached_out   DateTime @default(now())
  jeff_output_status String
  reply_sentiment    String?
  campaign_name      String?

  emails_sent        Json            @default("[]")
  replies            Json            @default("[]")
  prospect           Prospect        @relation(fields: [prospect_id], references: [prospect_id])
  amazon_prospect_id Int?
  createdAt          DateTime        @default(now())
  updatedAt          DateTime        @updatedAt

  @@index([client, prospect_id])
  @@index([amazon_seller_id])
  @@index([amazon_prospect_id])
}

model SellerCountryMatching {
  id                 Int      @id @default(autoincrement())
  amazon_seller_id   String   @default("")
  smartscout_country String   @default("")
  seller_url         String   @default("")
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  @@index([amazon_seller_id])
  @@index([smartscout_country])
}

model AmazonSeller {
  id                      Int          @id @default(autoincrement())
  name                    String?
  amazon_seller_id        String?
  marketplace             String?
  primary_category        String?
  primary_sub_category    String?
  estimate_sales          Float?
  avg_price               Float?
  percent_fba             Float?
  number_reviews_lifetime Int?
  number_reviews_30days   Int?
  number_winning_brands   Int?
  number_asins            Int?
  number_top_asins        Int?
  street                  String?
  city                    String?
  adr_state               String?
  adr_country             String?
  adr_zip_code            String?
  business_name           String?
  number_brands_1000      Int?
  mom_growth              Float?
  mom_growth_count        Int?
  is_suspended            Boolean?
  last_suspended_date     DateTime?
  started_selling_date    DateTime?
  domain                  String?
  website_status          String?
  lookup_source           String?
  lookup_sources          Json         @default("{}")
  seller_group_id         Int?
  seller_group            SellerGroup? @relation(fields: [seller_group_id], references: [id])
  amazonProspect          AmazonProspect[]
  createdAt               DateTime     @default(now())
  updatedAt               DateTime     @updatedAt

  @@unique([amazon_seller_id, marketplace])
  @@index([domain])
  @@index([website_status])
  @@index([seller_group_id])
  @@index([lookup_sources], type: Gin)
}

model SellerGroup {
  id             Int              @id @default(autoincrement())
  name           String?
  sellers        AmazonSeller[]
  seller_ids     Json             @default("[]")
  domains        Json             @default("[]")
  domain_count   Int?             @default(0)
  seller_count   Int?             @default(0)
  rank           Int              @default(0)
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
}

model SellerTree {
  id            Int          @id @default(autoincrement())
  entity_type   EntityType   // 'domain' or 'seller_id'
  entity_value  String       // actual domain or seller_id value
  parent_id     Int?         // reference to seller_group table
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt

  @@unique([entity_type, entity_value])
  @@index([entity_type])
  @@index([entity_value])
  @@index([parent_id])
  @@index([entity_type, entity_value, parent_id])
}

enum EntityType {
  domain
  seller_id
}

model LeadJob {
  id            Int         @id @default(autoincrement())
  name          String
  inputCsvName  String?
  inputCsvPath  String?
  useDomain     Boolean     @default(false)
  mode          LeadJobMode @default(serp)
  asyncSerp     Boolean     @default(false)
  status        JobStatus   @default(pending)
  resultJson    Json?       @default("{}")
  searchPattern String      @default("original")
  leads         Lead[]
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
}

model Lead {
  id             Int         @id @default(autoincrement())
  jobId          Int
  sellerName     String
  useDomain      Boolean     @default(false)
  mode           LeadJobMode @default(serp)
  businessName   String?
  address        String?
  numbers        Json?
  scraped_emails Json?
  sellerUrl      String?
  screenshotUrl  String?
  srp_task_id    Int?
  country        String?
  searchString   String      @default("")
  apiResponse    Json        @default("{}")
  status         LeadStatus  @default(pending)
  metadata       Json        @default("{}")
  processId      String?
  urls           LeadUrl[]
  job            LeadJob     @relation(fields: [jobId], references: [id])
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt

  @@index([status])
  @@index([jobId])
}

model LeadUrl {
  id                 Int         @id @default(autoincrement())
  leadId             Int
  url                String?
  data               String?
  keywords           Json?
  domain             String
  useDomain          Boolean     @default(false)
  googlePosition     Int?
  filterPosition     Int?
  confidence         Float       @default(0)
  htmlCheckResult    Json?
  snippetCheckResult Json?
  fuzzyDomainResult  Json?
  organicData        Json?
  textValidation     Json?       @default("{}")
  imageValidation    Json?       @default("{}")
  addressValidation  Json?       @default("{}")
  numberValidation   Json?       @default("{}")
  emailValidation    Json?       @default("{}")
  discoveredPages    Json?       @default("{}")
  matchedTextUrls    Json?       @default("[]")
  matchedImageUrls   Json?       @default("[]")
  matchedAddressUrls Json?       @default("[]")
  matchedNumberUrls  Json?       @default("[]")
  matchedEmailUrls   Json?       @default("[]")
  hasEmailMatch      Boolean     @default(false)
  hasAddressMatch    Boolean     @default(false)
  hasNumberMatch     Boolean     @default(false)
  hasTextMatch       Boolean     @default(false)
  hasImageMatch      Boolean     @default(false)
  mode               LeadJobMode @default(serp)
  lead               Lead        @relation(fields: [leadId], references: [id])
  createdAt          DateTime    @default(now())
  updatedAt          DateTime    @updatedAt

  @@unique([leadId, url])
  @@index([leadId])
}

enum LeadStatus {
  pending
  srp_requested
  srp_failed
  srp_scraped
  processing
  scoring
  validating
  completed
  failed
}

enum LeadJobMode {
  serp
  input_domain
}

model Serp_Cache {
  hash        String   @id @unique
  apiResponse Json
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model GitHubSlackThread {
  id          Int      @id @default(autoincrement())
  repo        String
  issueNumber Int
  slackTs     String
  channelId   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@unique([repo, issueNumber])
}

model ReviewJob {
  id               Int                @id @default(autoincrement())
  name             String
  status           ReviewJobStatus    @default(PENDING)
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @updatedAt
  Review           Review[]
  ReviewOutputData ReviewOutputData[]
}

model Review {
  id          Int       @id @default(autoincrement())
  reviewJobId Int
  reviewJob   ReviewJob @relation(fields: [reviewJobId], references: [id], onDelete: Cascade)

  asin             String
  brandName        String
  reviewId         String             @unique
  reviewUrl        String
  reviewer         String?
  reviewerLink     String?
  inputData        Json               @default("{}")
  status           ReviewStatus       @default(PENDING)
  next_run         DateTime?          @default(now())
  run_frequency    Int                @default(1)
  totalRuns        Int                @default(0)
  removedAt        DateTime? // Make it optional (nullable)
  removedHistory   Json               @default("[]") // Array of removal dates
  returnedHistory  Json               @default("[]") // Array of return dates
  comments         String             @default("") // Comments for status changes
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @updatedAt
  ReviewOutputData ReviewOutputData[]
}

model ReviewOutputData {
  id          Int          @id @default(autoincrement())
  revId       Int
  review      Review       @relation(fields: [revId], references: [id], onDelete: Cascade)
  reviewJobId Int
  status      ReviewStatus @default(PENDING)
  reviewJob   ReviewJob    @relation(fields: [reviewJobId], references: [id], onDelete: Cascade)
  reviewUrl   String
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
}

enum ReviewJobStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}

enum ReviewStatus {
  PENDING
  PRESENT
  REMOVED
  FAILED
  RESURRECTED
}

model DNSJob {
  id               Int       @id @default(autoincrement())
  originalFilename String?
  status           JobStatus @default(pending)
  progress         Int       @default(0)
  totalDomains     Int       @default(0)
  processedDomains Int       @default(0)
  inputFilePath    String?
  outputPath       String?
  error            Json?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
}

model Client {
  id        Int        @id @default(autoincrement())
  clientId  Int        @unique
  name      String
  campaigns Campaign[]
  createdAt DateTime   @default(now())
  updatedAt DateTime?  @updatedAt

  @@index([clientId])
}

model Campaign {
  id               Int              @id @default(autoincrement())
  campaignId       Int              @unique
  name             String
  clientId         Int
  parentCampaignId String
  client           Client           @relation(fields: [clientId], references: [clientId])
  leads            SmartLead_Lead[]
  createdAt        DateTime         @default(now())
  updatedAt        DateTime?        @updatedAt

  @@index([campaignId,parentCampaignId])
  @@index([clientId])
}

model SmartLead_Lead {
  id                Int             @id @default(autoincrement())
  campaignLeadMapId String          @unique
  leadId            String
  lead_category_id  Int?
  leadStatus        String?
  email             String
  website           String?
  campaignId        Int
  campaign          Campaign        @relation(fields: [campaignId], references: [id])
  emails            Email[]
  status            SmartLeadStatus @default(PENDING)
  createdAt         DateTime        @default(now())
  updatedAt         DateTime?       @updatedAt

  @@index([lead_category_id])
  @@index([campaignId])
  @@index([email])
}

model SmartLead_Lead_Category {
  id         Int       @unique
  created_at DateTime
  name       String
  createdAt  DateTime  @default(now())
  updatedAt  DateTime? @updatedAt
}

model Email {
  id               Int            @id @default(autoincrement())
  matchId          String
  campaingId       Int?
  leadId           Int
  messageId        String // Unique identifier for each email
  subject          String
  body             String
  type             String // Sent, Reply, Forward, etc.
  toEmailID        String
  fromEmailID      String
  email_seq_number String?
  open_count       Int?
  click_count      Int?
  click_details    Json?
  time             DateTime
  lead             SmartLead_Lead @relation(fields: [leadId], references: [id])
  createdAt        DateTime       @default(now())
  updatedAt        DateTime?      @updatedAt

  @@unique([messageId, time]) // Ensure uniqueness per email event
  @@index([campaingId])
  @@index([email_seq_number])
}

model RepoInstallationMapping {
  repo           String   @id         // e.g., "equalcollective/SellerBot"
  installationId Int
}

model JobRun {
  id        Int      @id @default(autoincrement())
  jobName   String   @unique
  lastRunAt DateTime
}

enum SmartLeadStatus {
  PENDING
  SUCCESS
  MISSING
  FAILED
}

model Product {
  id                    Int      @id @default(autoincrement())

  // seller information
  sellerId   String?
  marketplace String?
  sellerUrl String?
  
  // Basic product information
  url                   String  
  brand_name            String?  @db.VarChar(255)
  product_title         String?  @db.Text
  description           String?  @db.Text
  price                 Decimal? @db.Decimal(10, 2)
  
  // Ratings & reviews
  rating                Decimal? @db.Decimal(3, 2)
  total_reviews         Int?
  review_category       String?  @db.VarChar(100)
  star_5_count          Int?
  star_4_count          Int?
  star_3_count          Int?
  star_2_count          Int?
  star_1_count          Int?
  sales_count           Int?
  
  // Media
  main_image_url        String?  @db.Text
  image_count           Int?
  video_count           Int?
  
  // Metadata
  title_char_count      Int?
  title_under_150_chars Boolean?
  out_of_stock          Boolean  @default(false)
  
  // Amazon features
  aplus_content_present Boolean  @default(false)
  premium_aplus_present Boolean  @default(false)
  brand_story_present   Boolean  @default(false)
  storefront_present    Boolean  @default(false)
  storefront_url        String?  @db.Text
  
  // JSON fields for complex data
  bullet_points         Json?    @default("{}")
  categories_and_ranks  Json?    @default("{}")
  secondary_images      Json?    @default("{}")
  brand_story_images    Json?    @default("{}")
  
  // System fields
  full_json_data        Json     @default("{}")
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  @@map("products")
}

model CsvData {
  id                Int              @id @default(autoincrement())
  file_name         String           
  file_path         String           
  file_size         Int              
  file_type         String           
  process_type      String           // e.g., "prospect", "amazon_seller", "company", etc.
  original_headers  Json             @default("[]")
  total_rows        Int              @default(0)
  options           Json             @default("{}")
  header_mappings   Json             @default("{}")
  status            CsvProcessStatus @default(PENDING)
  processed_until   Int              @default(0)
  is_completed      Boolean          @default(false)
  error_status      Boolean          @default(false)
  error_message     String?          @db.Text
  retry_count       Int              @default(0)
  max_retries       Int              @default(3)
  last_retry_at     DateTime?
  source            String?          
  uploaded_by       String?          
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
  csvDataLogs       CsvDataLog[]

  @@index([status])
  @@index([process_type])
  @@index([is_completed])
}

model CsvDataLog {
  id             Int              @id @default(autoincrement())
  data_id        Int
  upload_log_id  String?          
  row_number     Int              
  headers_map    Json             @default("{}")
  processed      Boolean          @default(false)
  error_status   Boolean          @default(false)
  error_message  String?          @db.Text
  retry_count    Int              @default(0)
  
  // Flexible key-value columns (key1 to key50)
  key1           String?
  key2           String?
  key3           String?
  key4           String?
  key5           String?
  key6           String?
  key7           String?
  key8           String?
  key9           String?
  key10          String?
  key11          String?
  key12          String?
  key13          String?
  key14          String?
  key15          String?
  key16          String?
  key17          String?
  key18          String?
  key19          String?
  key20          String?
  key21          String?
  key22          String?
  key23          String?
  key24          String?
  key25          String?
  key26          String?
  key27          String?
  key28          String?
  key29          String?
  key30          String?
  key31          String?
  key32          String?
  key33          String?
  key34          String?
  key35          String?
  key36          String?
  key37          String?
  key38          String?
  key39          String?
  key40          String?
  key41          String?
  key42          String?
  key43          String?
  key44          String?
  key45          String?
  key46          String?
  key47          String?
  key48          String?
  key49          String?
  key50          String?
  
  csvData        CsvData          @relation(fields: [data_id], references: [id], onDelete: Cascade)
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt

  @@index([data_id])
  @@index([processed])
  @@index([error_status])
  @@index([row_number])
}

enum CsvProcessStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  PAUSED
}
