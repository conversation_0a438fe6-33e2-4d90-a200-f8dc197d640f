const {
  removeSpecialCharacters,
} = require("../../../utils/removeSpecialCharacters");
const Bottleneck = require("bottleneck");
const { getOrFetchHtml } = require("../../../utils/fileCache");
const stringSimilarity = require("string-similarity");

const limiter = new Bottleneck({
  maxConcurrent: 30,
  minTime: 30,
});

async function validateAddressRaw(
  organicDatum,
  addressToMatch,
  CONFIDENCE_THRESHOLD = 0.8,
) {
  const url = organicDatum.originalUrl;
  if (!addressToMatch) {
    return { error: "Insufficient Data" };
  }
  try {
    let html = await getOrFetchHtml(url);
    const normalizedHtml = removeSpecialCharacters(html);
    const normalizedAddress = removeSpecialCharacters(
      addressToMatch.toLowerCase(),
    );

    const addressWords = normalizedAddress.split(/\s+/);

    const totalWords = addressWords.length;
    const halfLength = Math.floor(totalWords / 2);

    let confidenceResults = [];

    // Check full address first
    const fullAddressConfidence = checkConfidence(
      addressWords,
      normalizedHtml,
      totalWords,
      "full",
    );

    if (fullAddressConfidence.confidence >= CONFIDENCE_THRESHOLD) {
      confidenceResults.push(fullAddressConfidence);
      return buildResponse(confidenceResults);
    }

    // Check from highest word count to lowest until we find a good match
    for (let wordCount = totalWords - 1; wordCount >= halfLength; wordCount--) {
      // Check last N words
      const lastNWords = addressWords.slice(-wordCount);

      const lastConfidence = checkConfidence(
        lastNWords,
        normalizedHtml,
        totalWords,
        `last_${wordCount}`,
      );

      if (lastConfidence.confidence >= CONFIDENCE_THRESHOLD) {
        confidenceResults.push(lastConfidence);
        return buildResponse(confidenceResults);
      }

      // Check first N words
      const firstNWords = addressWords.slice(0, wordCount);

      const firstConfidence = checkConfidence(
        firstNWords,
        normalizedHtml,
        totalWords,
        `first_${wordCount}`,
      );

      if (firstConfidence.confidence >= CONFIDENCE_THRESHOLD) {
        confidenceResults.push(firstConfidence);
        return buildResponse(confidenceResults);
      }
    }

    return buildResponse(confidenceResults);
  } catch (error) {
    console.error(error);
    return {
      matchedAddress: null,
      bestConfidence: 0,
      finalScore: 0,
      url,
      error: error.message,
    };
  }
}

function checkConfidence(words, normalizedHtml, totalWords, segment) {
  const searchString = words.join(" ");
  let confidence = 0;

  if (normalizedHtml.includes(searchString)) {
    confidence = words.length / totalWords;
  }

  return {
    segment,
    words: words.join(" "),
    wordCount: words.length,
    totalWords,
    confidence: confidence,
  };
}

function buildResponse(confidenceResults) {
  if (confidenceResults.length === 0) {
    return {
      matchedAddress: null,
      bestConfidence: 0,
      finalScore: 0,
    };
  }
  confidenceResults.sort((a, b) => b.confidence - a.confidence);
  const matchedSegments = confidenceResults[0].segment;
  const bestConfidence = confidenceResults[0].confidence;
  const matchedAddress = confidenceResults[0].words;
  const finalScore = validateAddress.config.score * bestConfidence;

  return {
    // matchedAddress: addressToMatch,
    matchedSegments,
    matchedAddress,
    bestConfidence: bestConfidence,
    finalScore: finalScore,
  };
}

const validateAddress = limiter.wrap(validateAddressRaw);

validateAddress.config = {
  score: 10,
  key: "validateAddress",
};

// Example function to test validateAddress
async function testValidateAddress() {
  // Simulated organicDatum object with a URL
  const organicDatum = {
    originalUrl:
      "https://products.ecomcrew.com/seller-profiles/All%20Home%20Goods%20USA",
  };

  // Address to match against the content of the URL
  const addressToMatch =
    "14145 Proctor Ave, Suite 12, LA PUENTE, CA, 91746, US";

  // Call the validateAddress function
  const result = await validateAddress(organicDatum, addressToMatch);

  // Log the result
  console.log("Validation Result:", result);
}

// Run the test function
// testValidateAddress();
async function main() {
  const url = "https://www.countrymanufacturing.com";

  if (!url) {
    console.log("Please provide a URL as an argument");
    process.exit(1);
  }

  const result = await validateAddress(
    { originalUrl: url },
    "2-800-335-1880",
    // true
  );

  console.log("HTML Check Results:", result);
}

if (require.main === module) {
  main();
}

module.exports = { validateAddress };
