/**
 * @swagger
 * /api/lead/insert/amazon_seller:
 *   post:
 *     summary: Insert Amazon Seller data from CSV file
 *     description: |
 *       This endpoint allows you to process a CSV file to insert new Amazon Seller records into the database. By default blanks values are skipped and if there are errors a csv with the errors listed is returned. Errors include:
 *         - Missing required fields (Amazon Seller ID, Marketplace)
 *         - Website present but website status is empty
 *         - Domain already associated with a different Amazon Seller ID in the same marketplace
 *         - Domain associated with a different Amazon Seller ID in the same file
 *
 *       Download sample CSV file:
 *       - [Download sample insert amazon_seller CSV](/examples/input_amazon_seller_insert.csv)
 *     tags: [Leads]
 *     parameters:
 *       - in: query
 *         name: skiperrorrows
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Whether to skip rows with errors
 *       - in: query
 *         name: updateBlankValues
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Whether to allow blank rows or values
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               csvFile:
 *                 type: string
 *                 format: binary
 *                 description: |
 *                   CSV file to process with the following structure for Amazon Seller data:
 *
 *                   Required fields:
 *                   - "Amazon Seller ID" or "amazon_seller_id" or "Seller ID": Amazon seller identifier (required)
 *                   - "Marketplace" or "marketplace" or "SmartScout Country" or "smartscout_country" or "Country": Marketplace/country code (required)
 *
 *                   Optional fields:
 *                   - "Name" or "name" or "Seller Name": Name of the seller
 *                   - "Primary Category" or "primary_category": Primary category name
 *                   - "Primary Sub Category" or "primary_sub_category": Primary sub-category
 *                   - "Estimate Sales" or "estimate_sales": Estimated sales figure (converted to Float)
 *                   - "Avg Price" or "avg_price": Average price (converted to Float)
 *                   - "Percent FBA" or "percent_fba": Percentage of Fulfillment by Amazon (converted to Float)
 *                   - "Number Reviews Lifetime" or "number_reviews_lifetime": Number of lifetime reviews (converted to Int)
 *                   - "Number Reviews 30days" or "number_reviews_30days": Number of reviews in last 30 days (converted to Int)
 *                   - "Number Winning Brands" or "number_winning_brands": Number of winning brands (converted to Int)
 *                   - "Number ASINs" or "number_asins": Number of ASINs (converted to Int)
 *                   - "Number Top ASINs" or "number_top_asins": Number of top ASINs (converted to Int)
 *                   - "Street" or "street": Street address
 *                   - "City" or "city": City
 *                   - "State" or "adr_state": State location
 *                   - "Country" or "adr_country": Country location
 *                   - "Zip Code" or "adr_zip_code": Postal/ZIP code
 *                   - "Business Name" or "business_name": Business name
 *                   - "Number Brands 1000" or "number_brands_1000": Number of brands in top 1000 (converted to Int)
 *                   - "MoM Growth" or "mom_growth": Month-over-Month growth (converted to Float)
 *                   - "MoM Growth Count" or "mom_growth_count": Month-over-Month growth count (converted to Int)
 *                   - "Is Suspended" or "is_suspended": Whether the seller is suspended (converted to Boolean)
 *                   - "Last Suspended Date" or "last_suspended_date": Date of last suspension (converted to Date)
 *                   - "Started Selling Date" or "started_selling_date": Date started selling (converted to Date)
 *                   - "Website" or "website": Company website URL
 *                   - "Domain" or "domain": Domain extracted from website
 *                   - "Website Status" or "website_status": Status of the website
 *                   - "Lookup Source" or "lookup_source": Source of the data
 *                   - "Seller Group ID" or "seller_group_id": ID of the seller group (converted to Int, automatically managed during processing)
 *
 *                   Example amazon_seller CSV:
 *                   Amazon Seller ID,Marketplace,Name,Business Name,Website,Website Status,Estimate Sales,Percent FBA,Number ASINs
 *                   A1B2C3D4E5,US,Acme Inc,Acme Corporation,https://acme.com,Final Correct,1000000,85.5,250
 *                   F6G7H8I9J0,UK,XYZ Corp,XYZ Industries,https://xyz-corp.com,Pending,500000,70.2,120
 *             required:
 *               - csvFile
 *     responses:
 *       200:
 *         description: Data uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Data uploaded successfully"
 *       403:
 *         description: CSV file containing error rows
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 *             example: |
 *               row,error_message,Amazon Seller ID,Marketplace,Name,Business Name,Website,Website Status
 *               2,"Missing required field: Amazon Seller ID","","US","Acme Inc","Acme Corporation","https://acme.com","Final Correct"
 *               3,"Missing required field: Marketplace","A1B2C3D4E5","","XYZ Corp","XYZ Industries","https://xyz-corp.com","Pending"
 *               4,"Website present but website_status is empty","K1L2M3N4O5","US","ABC Ltd","ABC Limited","https://abc.com",""
 *               5,"Domain already associated with a different Amazon Seller ID in marketplace US","P6Q7R8S9T0","US","DEF Inc","DEF Incorporated","https://acme.com","Final Correct"
 *         headers:
 *           Content-Disposition:
 *             schema:
 *               type: string
 *             description: Attachment header with filename
 *             example: attachment; filename="ERROR-input_file.csv"
 *           Content-Type:
 *             schema:
 *               type: string
 *             example: text/csv
 *       400:
 *         description: Invalid type or operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Invalid type"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *
 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 */

/**
 * @swagger
 * /api/lead/update/amazon_seller:
 *   post:
 *     summary: Update Amazon Seller data from CSV file
 *     description: |
 *       This endpoint allows you to process a CSV file to update existing Amazon Seller records in the database. By default blanks values are skipped and if there are errors a csv with the errors listed is returned. Errors include:
 *         - Missing required fields (Amazon Seller ID, Marketplace)
 *         - Website present but website status is empty
 *         - Domain already associated with a different Amazon Seller ID in the same marketplace
 *         - Domain associated with a different Amazon Seller ID in the same file
 *         - Cannot perform update - record does not exist
 *         - Domain does not match with the existing record when website status is "Final Correct"
 *
 *       Download sample CSV file:
 *       - [Download sample update amazon_seller CSV](/examples/input_amazon_seller_update.csv)
 *     tags: [Leads]
 *     parameters:
 *       - in: query
 *         name: skiperrorrows
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Whether to skip rows with errors
 *       - in: query
 *         name: updateBlankValues
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Whether to allow blank rows or values
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               csvFile:
 *                 type: string
 *                 format: binary
 *                 description: |
 *                   CSV file to process with the following structure for Amazon Seller data:
 *
 *                   Required fields:
 *                   - "Amazon Seller ID" or "amazon_seller_id" or "Seller ID": Amazon seller identifier (required)
 *                   - "Marketplace" or "marketplace" or "SmartScout Country" or "smartscout_country" or "Country": Marketplace/country code (required)
 *
 *                   Optional fields:
 *                   - "Name" or "name" or "Seller Name": Name of the seller
 *                   - "Primary Category" or "primary_category": Primary category name
 *                   - "Primary Sub Category" or "primary_sub_category": Primary sub-category
 *                   - "Estimate Sales" or "estimate_sales": Estimated sales figure (converted to Float)
 *                   - "Avg Price" or "avg_price": Average price (converted to Float)
 *                   - "Percent FBA" or "percent_fba": Percentage of Fulfillment by Amazon (converted to Float)
 *                   - "Number Reviews Lifetime" or "number_reviews_lifetime": Number of lifetime reviews (converted to Int)
 *                   - "Number Reviews 30days" or "number_reviews_30days": Number of reviews in last 30 days (converted to Int)
 *                   - "Number Winning Brands" or "number_winning_brands": Number of winning brands (converted to Int)
 *                   - "Number ASINs" or "number_asins": Number of ASINs (converted to Int)
 *                   - "Number Top ASINs" or "number_top_asins": Number of top ASINs (converted to Int)
 *                   - "Street" or "street": Street address
 *                   - "City" or "city": City
 *                   - "State" or "adr_state": State location
 *                   - "Country" or "adr_country": Country location
 *                   - "Zip Code" or "adr_zip_code": Postal/ZIP code
 *                   - "Business Name" or "business_name": Business name
 *                   - "Number Brands 1000" or "number_brands_1000": Number of brands in top 1000 (converted to Int)
 *                   - "MoM Growth" or "mom_growth": Month-over-Month growth (converted to Float)
 *                   - "MoM Growth Count" or "mom_growth_count": Month-over-Month growth count (converted to Int)
 *                   - "Is Suspended" or "is_suspended": Whether the seller is suspended (converted to Boolean)
 *                   - "Last Suspended Date" or "last_suspended_date": Date of last suspension (converted to Date)
 *                   - "Started Selling Date" or "started_selling_date": Date started selling (converted to Date)
 *                   - "Website" or "website": Company website URL
 *                   - "Domain" or "domain": Domain extracted from website
 *                   - "Website Status" or "website_status": Status of the website
 *                   - "Lookup Source" or "lookup_source": Source of the data
 *                   - "Seller Group ID" or "seller_group_id": ID of the seller group (converted to Int)
 *
 *                   Example amazon_seller CSV:
 *                   Amazon Seller ID,Marketplace,Name,Business Name,Website,Website Status,Estimate Sales,Percent FBA,Number ASINs
 *                   A1B2C3D4E5,US,Acme Inc,Acme Corporation,https://acme.com,Final Correct,1000000,85.5,250
 *                   F6G7H8I9J0,UK,XYZ Corp,XYZ Industries,https://xyz-corp.com,Pending,500000,70.2,120
 *             required:
 *               - csvFile
 *     responses:
 *       200:
 *         description: Data uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Data uploaded successfully"
 *       403:
 *         description: CSV file containing error rows
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 *             example: |
 *               row,error_message,Amazon Seller ID,Marketplace,Name,Business Name,Website,Website Status
 *               2,"Missing required field: Amazon Seller ID","","US","Acme Inc","Acme Corporation","https://acme.com","Final Correct"
 *               3,"Missing required field: Marketplace","A1B2C3D4E5","","XYZ Corp","XYZ Industries","https://xyz-corp.com","Pending"
 *               4,"Website present but website_status is empty","K1L2M3N4O5","US","ABC Ltd","ABC Limited","https://abc.com",""
 *               5,"Cannot perform update - record does not exist","P6Q7R8S9T0","US","DEF Inc","DEF Incorporated","https://def.com","Final Correct"
 *         headers:
 *           Content-Disposition:
 *             schema:
 *               type: string
 *             description: Attachment header with filename
 *             example: attachment; filename="ERROR-input_file.csv"
 *           Content-Type:
 *             schema:
 *               type: string
 *             example: text/csv
 *       400:
 *         description: Invalid type or operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Invalid type"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal server error"
 */
