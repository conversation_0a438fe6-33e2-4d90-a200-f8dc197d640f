const path = require("path");
const crypto = require("crypto");
const { FlatCache } = require("flat-cache");
const fs = require("fs");
const axios = require("axios");
const cacheDir = path.resolve(__dirname, "cache");
if (!fs.existsSync(cacheDir)) {
  fs.mkdirSync(cacheDir, { recursive: true });
}

const cache = new FlatCache({
  cacheId: "htmlCache",
  ttl: 60 * 15 * 1000, // 15 mins in seconds
  lruSize: 600, // 500 items}
  expirationInterval: 5 * 1000 * 60, // 5 minutes
});

async function getCacheFilePath(url) {
  const hash = crypto.createHash("md5").update(url).digest("hex");
  return hash;
}

async function getOrFetchHtml(url, redirectCount = 0) {
  const cacheKey = await getCacheFilePath(url);
  const cachedContent = cache.getKey(cacheKey);

  if (cachedContent) {
    console.log("Using cached content for:", url);
    return cachedContent;
  }

  try {
    console.log("Fetching URL:", url);
    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), 30000);

    const response = await axios.get(url, {
      headers: {
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        Accept:
          "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        Connection: "keep-alive",
        "Upgrade-Insecure-Requests": "1",
      },
      redirect: "manual",
      signal: controller.signal,
      validateStatus: function (status) {
        return status >= 200 && status < 400;
      },
      timeout: 30000,
      maxRedirects: 5,
    });

    clearTimeout(timeout);

    if (response.status >= 300 && response.status < 400) {
      if (redirectCount >= 3) {
        throw new Error("Too many redirects");
      }
      const redirectUrl = response.headers.location;
      console.log(
        "Redirecting to:",
        redirectUrl,
        "from",
        url,
        "count",
        redirectCount,
      );
      return getOrFetchHtml(redirectUrl, redirectCount + 1);
    }

    const contentType = response.headers["content-type"];
    if (contentType && contentType.includes("application/pdf")) {
      throw new Error("PDF content detected");
    }

    if (!response.data || typeof response.data !== "string") {
      throw new Error("Invalid response data received");
    }

    const html = response.data;
    if (html.length < 100) {
      // Basic validation that we got meaningful content
      throw new Error("Received suspiciously short HTML content");
    }

    cache.setKey(cacheKey, html);
    console.log("Successfully fetched and cached HTML for:", url);
    return html;
  } catch (error) {
    if (error.name === "AbortError") {
      console.error("Request timed out for URL:", url);
      throw new Error(`Request timed out after 30 seconds for ${url}`);
    }
    if (error.response) {
      console.error("HTTP Error:", {
        status: error.response.status,
        statusText: error.response.statusText,
        url: url,
      });
      throw new Error(
        `HTTP ${error.response.status}: ${error.response.statusText}`,
      );
    }
    if (error.request) {
      console.error("Network Error:", {
        url: url,
        error: error.message,
      });
      throw new Error(`Network error: ${error.message}`);
    }
    console.error("Error fetching URL:", url, error);
    throw error;
  }
}
module.exports = {
  getCacheFilePath,
  getOrFetchHtml,
};
