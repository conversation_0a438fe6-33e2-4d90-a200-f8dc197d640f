#!/usr/bin/env node

/**
 * Metadata Tagging Test Script
 * 
 * This script demonstrates and tests the comprehensive metadata tagging
 * functionality for LiteLLM/Lunary tracking.
 */

require('dotenv').config();
const { litellmService } = require('./services/ai/litellmService');
const { getChatGPTResponse } = require('./services/scrapeGPT/request');
const { completionFactory } = require('./services/scrapeGPT/factory');
const { aiMetadataHelper, metadataGenerators } = require('./utils/aiMetadataHelper');

// Enable metadata logging for this test
process.env.AI_METADATA_LOGGING = 'true';

function printTestHeader(title) {
    console.log('\n' + '='.repeat(60));
    console.log(`🏷️  ${title}`);
    console.log('='.repeat(60));
}

function printTestResult(testName, success, details = '') {
    const icon = success ? '✅' : '❌';
    console.log(`${icon} ${testName}: ${success ? 'PASSED' : 'FAILED'}`);
    if (details) {
        console.log(`   ${details}`);
    }
}

async function testBasicMetadataGeneration() {
    printTestHeader('Basic Metadata Generation Test');
    
    try {
        // Test basic metadata generation
        const metadata = aiMetadataHelper.generateMetadata({
            useCase: 'test',
            operationType: 'test_operation',
            userId: 'test_user_123',
            userType: 'admin',
            customTags: {
                test_tag: 'test_value'
            }
        });
        
        console.log('Generated Metadata:');
        console.log(JSON.stringify(metadata, null, 2));
        
        // Verify required fields
        const requiredFields = ['request_id', 'session_id', 'timestamp', 'service', 'calling_function'];
        const hasAllFields = requiredFields.every(field => metadata[field]);
        
        printTestResult('Metadata Generation', hasAllFields, `Contains all required fields`);
        printTestResult('Custom Tags', metadata.tags.test_tag === 'test_value', 'Custom tags preserved');
        printTestResult('User Context', metadata.user_id === 'test_user_123', 'User context captured');
        
        return hasAllFields;
    } catch (error) {
        printTestResult('Basic Metadata Generation', false, error.message);
        return false;
    }
}

async function testLiteLLMServiceWithMetadata() {
    printTestHeader('LiteLLM Service with Metadata Test');
    
    try {
        console.log('🔄 Testing LiteLLM service with metadata tagging...');
        
        const result = await litellmService.createChatCompletion([
            {
                role: 'system',
                content: 'You are a helpful assistant for testing metadata tagging.'
            },
            {
                role: 'user',
                content: 'Say "Metadata test successful!" and explain what metadata tagging is in one sentence.'
            }
        ], {
            useCase: 'metadata_test',
            feature: 'testing',
            userId: 'test_user_456',
            userType: 'developer',
            temperature: 0.7,
            max_tokens: 100,
            customTags: {
                test_type: 'metadata_validation',
                environment: 'test'
            }
        });
        
        const success = result.success && result.message;
        printTestResult('LiteLLM Request', success, success ? `Response: "${result.message.substring(0, 50)}..."` : result.error);
        printTestResult('Metadata Included', !!result.metadata, 'Response contains metadata');
        printTestResult('Request ID', !!result.metadata?.request_id, 'Request ID generated');
        
        if (result.metadata) {
            console.log('\nResponse Metadata Preview:');
            console.log(`  Request ID: ${result.metadata.request_id}`);
            console.log(`  Use Case: ${result.metadata.use_case}`);
            console.log(`  Feature: ${result.metadata.tags.feature}`);
            console.log(`  User ID: ${result.metadata.user_id}`);
            console.log(`  Duration: ${result.duration}ms`);
        }
        
        return success;
    } catch (error) {
        printTestResult('LiteLLM Service Test', false, error.message);
        return false;
    }
}

async function testScrapeGPTWithMetadata() {
    printTestHeader('ScrapeGPT with Metadata Test');
    
    try {
        console.log('🔄 Testing ScrapeGPT with metadata tagging...');
        
        const result = await getChatGPTResponse(
            'You are an AI that analyzes website content for business information.',
            'Analyze this sample text: "Acme Corp is a leading provider of widgets and gadgets."',
            true, // usePortkey
            {
                useCase: 'scrape_analysis',
                feature: 'content_analysis',
                userId: 'scraper_bot',
                userType: 'system',
                scrapeType: 'business_analysis',
                domain: 'example.com',
                customTags: {
                    content_type: 'business_description',
                    analysis_type: 'entity_extraction'
                }
            }
        );
        
        const success = result.success && result.message;
        printTestResult('ScrapeGPT Request', success, success ? `Response: "${result.message.substring(0, 50)}..."` : result.error);
        printTestResult('Metadata Tracking', !!result.metadata, 'Metadata tracked in response');
        
        return success;
    } catch (error) {
        printTestResult('ScrapeGPT Test', false, error.message);
        return false;
    }
}

async function testFactoryWithMetadata() {
    printTestHeader('Factory Completion with Metadata Test');
    
    try {
        console.log('🔄 Testing factory completion with metadata tagging...');
        
        const testData = {
            textContent: 'This is a test company called Example Corp that sells software solutions.',
            businessKeywords: ['Example Corp', 'software'],
            url: 'https://example.com'
        };
        
        const result = await completionFactory(
            'match_text',
            testData,
            null, // no assistant
            true, // stringify
            {
                useCase: 'lead_generation',
                feature: 'business_matching',
                userId: 'lead_gen_system',
                userType: 'system',
                customTags: {
                    batch_id: 'test_batch_001',
                    source: 'manual_test'
                }
            }
        );
        
        const success = result.success && result.message;
        printTestResult('Factory Completion', success, success ? `Response: "${result.message.substring(0, 50)}..."` : result.error);
        printTestResult('Template Metadata', !!result.metadata, 'Template metadata captured');
        
        return success;
    } catch (error) {
        printTestResult('Factory Test', false, error.message);
        return false;
    }
}

async function testStreamingWithMetadata() {
    printTestHeader('Streaming with Metadata Test');
    
    try {
        console.log('🔄 Testing streaming completion with metadata tagging...');
        
        const stream = await litellmService.createStreamingCompletion([
            {
                role: 'user',
                content: 'Count from 1 to 5, each number on a new line.'
            }
        ], {
            useCase: 'streaming_test',
            feature: 'streaming_response',
            userId: 'stream_tester',
            userType: 'developer',
            customTags: {
                stream_type: 'counting',
                test_mode: true
            }
        });
        
        let fullResponse = '';
        let chunkCount = 0;
        
        console.log('Streaming response:');
        for await (const chunk of stream) {
            const content = chunk.choices[0]?.delta?.content || '';
            if (content) {
                fullResponse += content;
                chunkCount++;
                process.stdout.write(content);
            }
        }
        console.log(); // New line
        
        const success = chunkCount > 0 && fullResponse.length > 0;
        printTestResult('Streaming Response', success, `Received ${chunkCount} chunks`);
        printTestResult('Content Generated', fullResponse.length > 0, `Content length: ${fullResponse.length}`);
        
        return success;
    } catch (error) {
        printTestResult('Streaming Test', false, error.message);
        return false;
    }
}

async function testMetadataHeaders() {
    printTestHeader('Metadata Headers Test');
    
    try {
        console.log('🔄 Testing metadata header generation...');
        
        const headers = aiMetadataHelper.generateHeaders({
            useCase: 'header_test',
            operationType: 'test_operation',
            userId: 'header_tester',
            feature: 'header_generation'
        });
        
        console.log('Generated Headers:');
        Object.entries(headers).forEach(([key, value]) => {
            console.log(`  ${key}: ${value.substring(0, 50)}${value.length > 50 ? '...' : ''}`);
        });
        
        const requiredHeaders = ['X-Request-ID', 'X-Session-ID', 'X-Service', 'X-Function'];
        const hasAllHeaders = requiredHeaders.every(header => headers[header]);
        
        printTestResult('Header Generation', hasAllHeaders, 'All required headers present');
        printTestResult('Metadata Header', !!headers['X-LiteLLM-Metadata'], 'LiteLLM metadata header included');
        
        return hasAllHeaders;
    } catch (error) {
        printTestResult('Headers Test', false, error.message);
        return false;
    }
}

async function main() {
    console.log('🚀 Starting Metadata Tagging Tests');
    console.log('📋 This will test comprehensive metadata tagging for LiteLLM/Lunary tracking\n');
    
    const results = {
        basicMetadata: await testBasicMetadataGeneration(),
        litellmService: await testLiteLLMServiceWithMetadata(),
        scrapeGPT: await testScrapeGPTWithMetadata(),
        factory: await testFactoryWithMetadata(),
        streaming: await testStreamingWithMetadata(),
        headers: await testMetadataHeaders(),
    };
    
    // Final summary
    printTestHeader('Test Summary');
    
    const totalTests = Object.keys(results).length;
    const passedTests = Object.values(results).filter(Boolean).length;
    const failedTests = totalTests - passedTests;
    
    console.log(`📊 Test Results: ${passedTests}/${totalTests} passed`);
    console.log(`✅ Passed: ${passedTests}`);
    console.log(`❌ Failed: ${failedTests}`);
    
    for (const [testName, passed] of Object.entries(results)) {
        printTestResult(testName.charAt(0).toUpperCase() + testName.slice(1), passed);
    }
    
    if (passedTests === totalTests) {
        console.log('\n🎉 All metadata tagging tests passed!');
        console.log('\n💡 Integration Tips:');
        console.log('1. Set USE_LITELLM_PROXY=true in your .env to use LiteLLM service');
        console.log('2. Set AI_METADATA_LOGGING=true to see metadata in logs');
        console.log('3. Check Lunary dashboard for detailed request tracking');
        console.log('4. Use custom tags to categorize your requests by feature/use case');
    } else {
        console.log('\n⚠️  Some tests failed. Check the configuration and try again.');
    }
    
    process.exit(failedTests > 0 ? 1 : 0);
}

if (require.main === module) {
    main().catch(error => {
        console.error('❌ Test suite failed:', error);
        process.exit(1);
    });
}

module.exports = {
    testBasicMetadataGeneration,
    testLiteLLMServiceWithMetadata,
    testScrapeGPTWithMetadata,
    testFactoryWithMetadata,
    testStreamingWithMetadata,
    testMetadataHeaders
};
