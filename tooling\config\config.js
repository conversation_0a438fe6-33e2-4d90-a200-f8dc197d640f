require('dotenv').config({ path: '../.env' });

module.exports = {
  sheets: {
    MANUAL_SHEET_ID: "1RGhtxUiQwttUi4T631CovQtMnBsE2My6G-gJ1lhLwVA",
    ERRORS_SHEET_ID: "12gx3PjWpuV2KK-iGiy7xvE6hVtxuNX0rgbOaTyEcG8c",
    OUTPUT_TRACKING_SHEET_ID: "1ps59iKvviPeprLJYHAyEva-zqvSIuvrVPw7q5FwENPQ",
    OUTPUT_CSV_FILE: "google_sheet_data.csv",
    ERRORS_CSV_FILE: "errors.csv",
    CREDENTIALS_PATH: "config/GoogleServiceAccountCreds.json",
    SCOPES: [
      "https://www.googleapis.com/auth/spreadsheets.readonly",
      "https://www.googleapis.com/auth/spreadsheets"
    ]
  },
  api: {
    BEARER_TOKEN: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************.FahcAF5_QjbRaDmHxe9Zc-AHlrA0oNCrFiB5cTg-OE4",
    BASE_URL: "http://**************:8000",
    ENDPOINTS: {
      ClientProspectMatching: "/api/lead/matching",
      UpdateCompanies: "/api/lead/update/company",
      UpdateProspects: "/api/lead/prospect"
    },
    UPDATED_ENDPOINTS: {
      UpdateCompanies: "/api/lead/update/amazon_seller",
      UpdateProspects: "/api/lead/amazon_prospect"
    }
  },
  validation: {
    optionalHeaders: [
      "percent_fba",
      "number_asins",
      "number_top_asins",
      "mom_growth",
      "mom_growth_count",
      "smartscout_country",
      "company_linkedin",
      "company_fb",
      "company_address",
      "company_pincode"
    ]
  }
};
