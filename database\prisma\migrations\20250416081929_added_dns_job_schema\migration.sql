-- CreateTable
CREATE TABLE "DNSJob" (
    "id" SERIAL NOT NULL,
    "originalFilename" TEXT,
    "status" "JobStatus" NOT NULL DEFAULT 'pending',
    "progress" INTEGER NOT NULL DEFAULT 0,
    "totalDomains" INTEGER NOT NULL DEFAULT 0,
    "processedDomains" INTEGER NOT NULL DEFAULT 0,
    "inputFilePath" TEXT,
    "outputPath" TEXT,
    "error" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "DNSJob_pkey" PRIMARY KEY ("id")
);
