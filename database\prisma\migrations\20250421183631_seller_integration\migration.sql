-- AlterTable
ALTER TABLE "Prospect" ADD COLUMN     "seller_group_id" INTEGER;

-- CreateTable
CREATE TABLE "AmazonSeller" (
    "id" SERIAL NOT NULL,
    "name" TEXT,
    "amazon_seller_id" TEXT,
    "marketplace" TEXT,
    "primary_category" TEXT,
    "primary_sub_category" TEXT,
    "estimate_sales" DOUBLE PRECISION,
    "avg_price" DOUBLE PRECISION,
    "percent_fba" DOUBLE PRECISION,
    "number_reviews_lifetime" INTEGER,
    "number_reviews_30days" INTEGER,
    "number_winning_brands" INTEGER,
    "number_asins" INTEGER,
    "number_top_asins" INTEGER,
    "street" TEXT,
    "city" TEXT,
    "adr_state" TEXT,
    "adr_country" TEXT,
    "adr_zip_code" TEXT,
    "business_name" TEXT,
    "number_brands_1000" INTEGER,
    "mom_growth" DOUBLE PRECISION,
    "mom_growth_count" INTEGER,
    "is_suspended" BOOLEAN,
    "last_suspended_date" TIMESTAMP(3),
    "started_selling_date" TIMESTAMP(3),
    "website" TEXT,
    "domain" TEXT,
    "website_status" TEXT,
    "lookup_source" TEXT,
    "lookup_sources" JSONB NOT NULL DEFAULT '{}',
    "seller_group_id" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AmazonSeller_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SellerGroup" (
    "id" SERIAL NOT NULL,
    "name" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SellerGroup_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "AmazonSeller_domain_idx" ON "AmazonSeller"("domain");

-- CreateIndex
CREATE INDEX "AmazonSeller_website_status_idx" ON "AmazonSeller"("website_status");

-- CreateIndex
CREATE INDEX "AmazonSeller_seller_group_id_idx" ON "AmazonSeller"("seller_group_id");

-- CreateIndex
CREATE UNIQUE INDEX "AmazonSeller_amazon_seller_id_marketplace_key" ON "AmazonSeller"("amazon_seller_id", "marketplace");

-- AddForeignKey
ALTER TABLE "Prospect" ADD CONSTRAINT "Prospect_seller_group_id_fkey" FOREIGN KEY ("seller_group_id") REFERENCES "SellerGroup"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AmazonSeller" ADD CONSTRAINT "AmazonSeller_seller_group_id_fkey" FOREIGN KEY ("seller_group_id") REFERENCES "SellerGroup"("id") ON DELETE SET NULL ON UPDATE CASCADE;
