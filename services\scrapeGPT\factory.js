const { getChatGPTResponse } = require("./request");
const { getAssistantResponse } = require("./assistant");
const { aiMetadataHelper, metadataGenerators } = require("../../utils/aiMetadataHelper");
const fs = require("fs");
async function getPromptTemplate(type) {
  // console.log(__dirname)
  switch (type) {
    case "match_text":
      return fs.readFileSync(`${__dirname}/matchText.md`, "utf8");
    case "match_url":
      return fs.readFileSync(`${__dirname}/matchUrl.md`, "utf8");
    case "match_image":
      return fs.readFileSync(`${__dirname}/matchImages.md`, "utf8");
  }
  return "Hello";
}

async function completionFactory(
  type,
  data,
  assistantId = null,
  stringify = true,
  options = {}
) {
  try {
    // Generate metadata for factory completion
    const metadata = metadataGenerators.centralizedAI({
      useCase: 'factory_completion',
      feature: 'template_completion',
      operationType: 'factory_completion',
      userId: options.userId,
      userType: options.userType,
      customTags: {
        factory_type: type,
        data_size: JSON.stringify(data).length,
        template_type: type,
        uses_assistant: !!assistantId,
        assistant_id: assistantId,
        stringify_data: stringify,
        ...options.customTags
      }
    });

    // Log factory operation
    aiMetadataHelper.logMetadata({
      ...metadata,
      operation: 'completionFactory',
      factory_type: type,
      data_preview: JSON.stringify(data).substring(0, 100) + '...'
    }, 'info');

    const system_prompt = await getPromptTemplate(type);
    const user_prompt = stringify ? JSON.stringify(data) : data;

    // Pass metadata to the underlying services
    const requestOptions = {
      ...options,
      useCase: 'factory_completion',
      feature: type,
      scrapeType: type,
      customTags: {
        ...metadata.tags,
        template_used: type
      }
    };

    const result = assistantId
      ? await getAssistantResponse(assistantId, user_prompt, true, requestOptions)
      : await getChatGPTResponse(system_prompt, user_prompt, true, requestOptions);

    // Log successful completion
    aiMetadataHelper.logMetadata({
      ...metadata,
      status: 'success',
      result_length: result?.message?.length || 0,
      tokens_used: result?.usage?.total_tokens
    }, 'info');

    return result;
  } catch (error) {
    // Log error with metadata
    aiMetadataHelper.logMetadata({
      operation: 'completionFactory',
      factory_type: type,
      status: 'error',
      error_message: error.message,
      error_stack: error.stack,
      uses_assistant: !!assistantId
    }, 'error');

    console.error(`Error in ${type}:`, error.stack);
    throw new Error(`Error fetching text from ChatGPT: ${error}`);
  }
}

// Example usage
async function exampleUsage() {
  try {
    // const result = await completionFactory("match_url", {
    //   url: "https://tnpub.com/",
    //   businessKeywords: ["True North Publishing" , "The Starboard Group LLCn"],
    // });
    // console.log(result);
    const result = await completionFactory("match_text", {
      textContent: fs.readFileSync("services/scrapeGPT/data.txt", "utf8"),
      businessKeywords: ["True North Publishing", "The Starboard Group LLCn"],
    });
    console.log(result);
  } catch (error) {
    console.error("Error:", error);
  }
}

// exampleUsage();
module.exports = { completionFactory };
