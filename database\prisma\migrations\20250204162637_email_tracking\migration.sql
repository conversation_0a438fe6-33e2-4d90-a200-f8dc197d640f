-- CreateTable
CREATE TABLE "Client" (
    "id" SERIAL NOT NULL,
    "clientId" TEXT NOT NULL,
    "name" TEXT NOT NULL,

    CONSTRAINT "Client_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Campaign" (
    "id" SERIAL NOT NULL,
    "campaignId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "clientId" INTEGER NOT NULL,

    CONSTRAINT "Campaign_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SmartLead_Lead" (
    "id" SERIAL NOT NULL,
    "campaignLeadMapId" TEXT NOT NULL,
    "leadId" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "website" TEXT,
    "campaignId" INTEGER NOT NULL,

    CONSTRAINT "SmartLead_Lead_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Email" (
    "id" SERIAL NOT NULL,
    "matchId" TEXT NOT NULL,
    "leadId" INTEGER NOT NULL,
    "subject" TEXT NOT NULL,
    "body" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "toEmailID" TEXT NOT NULL,
    "fromEmailID" TEXT NOT NULL,
    "time" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Email_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Client_clientId_key" ON "Client"("clientId");

-- CreateIndex
CREATE UNIQUE INDEX "Campaign_campaignId_key" ON "Campaign"("campaignId");

-- CreateIndex
CREATE UNIQUE INDEX "SmartLead_Lead_campaignLeadMapId_key" ON "SmartLead_Lead"("campaignLeadMapId");

-- CreateIndex
CREATE UNIQUE INDEX "SmartLead_Lead_leadId_key" ON "SmartLead_Lead"("leadId");

-- CreateIndex
CREATE UNIQUE INDEX "SmartLead_Lead_email_key" ON "SmartLead_Lead"("email");

-- CreateIndex
CREATE UNIQUE INDEX "Email_matchId_key" ON "Email"("matchId");

-- AddForeignKey
ALTER TABLE "Campaign" ADD CONSTRAINT "Campaign_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "Client"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SmartLead_Lead" ADD CONSTRAINT "SmartLead_Lead_campaignId_fkey" FOREIGN KEY ("campaignId") REFERENCES "Campaign"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Email" ADD CONSTRAINT "Email_leadId_fkey" FOREIGN KEY ("leadId") REFERENCES "SmartLead_Lead"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
