/**
 * Migration script to transfer data from Prospect to AmazonProspect table
 * Uses simple upsert logic based on email or LinkedIn
 * Maintains all relationships with AmazonSeller and ClientsProspectsMatching
 */

const prisma = require("../database/prisma/getPrismaClient");

// Configuration
const BATCH_SIZE = 2000;
let skipCount = 0;

async function migrateProspectData() {
  console.log(
    "Starting migration from Prospect to AmazonProspect..."
  );

  try {
    // Get total count for progress tracking
    const totalProspects = await prisma.prospect.count();
    console.log(`Found ${totalProspects} prospects to migrate`);

    // Stats tracking
    let processedCount = 0;
    let successCount = 0;
    let errorCount = 0;
    let skippedCount = 0;
    let matchingUpdatedCount = 0;
    let skip = 0;

    // Process prospects in batches
    while (true) {
      // Fetch a batch of prospects
      console.log(`Fetching batch starting at offset ${skip}...`);
      const prospects = await prisma.prospect.findMany({
        skip,
        take: BATCH_SIZE,
        include: {
          clientsProspects: true,
        },
        orderBy: {
          prospect_id: "asc",
        },
      });

      if (prospects.length === 0) {
        console.log("No more prospects to process.");
        break;
      }

      let batchSuccessCount = 0;
      let batchErrorCount = 0;
      let batchSkippedCount = 0;
      let batchMatchingUpdatedCount = 0;

      console.log(`Processing batch of ${prospects.length} prospects...`);

      // Process each prospect in the batch
      for (const prospect of prospects) {
        processedCount++;

        try {
          // Skip prospects without email or LinkedIn (no way to upsert)
          if (!prospect.email && !prospect.person_linkedin) {
            console.log(
              `Skipping prospect ${prospect.prospect_id} - no email or LinkedIn`
            );
            skippedCount++;
            batchSkippedCount++;
            continue;
          }

          // Find associated AmazonSeller based on amazon_seller_id if there are multiple sellers for the same amazon_seller_id then the market place ordersing  US -> CA -> UK -> undefined
          let seller_id = null;
          if (prospect.amazon_seller_id) {
            const seller = await prisma.amazonSeller.findMany({
              where: { amazon_seller_id: prospect.amazon_seller_id },
              orderBy: {  
                marketplace: "asc",
              },
            });

            if (seller && seller.length > 0) {
              const sortedSellers = seller.sort((a, b) => {
                const marketplaceOrder = {
                  US: 0,
                  CA: 1,
                  UK: 2,
                  undefined: 3,
                };  
                return marketplaceOrder[a.marketplace] - marketplaceOrder[b.marketplace];
              });
              
              seller_id = sortedSellers[0].id;
              // console.log(`Mapped AmazonProspect to seller_id: ${seller_id}`);
            }
          }

          // Upsert AmazonProspect based on email or LinkedIn
          const amazonProspect = await upsertAmazonProspect(prospect, seller_id);

          // Update all ClientsProspectsMatching records to point to this AmazonProspect
          const updatedCount = await updateClientsProspectsMatching(
            prospect.clientsProspects,
            amazonProspect.prospect_id
          );
          
          matchingUpdatedCount += updatedCount;
          batchMatchingUpdatedCount += updatedCount;

          successCount++;
          batchSuccessCount++;
        } catch (error) {
          errorCount++;
          batchErrorCount++;
          console.error(
            `Error processing prospect ${prospect.prospect_id}:`,
            error.message
          );
        }
      }

      console.log(
        `Batch progress: ${processedCount}/${totalProspects} (${((processedCount / totalProspects) * 100).toFixed(2)}%)`
      );
      console.log(`Batch results: Success: ${batchSuccessCount}, Errors: ${batchErrorCount}, Skipped: ${batchSkippedCount}, Matchings updated: ${batchMatchingUpdatedCount}`);
      console.log(`Running totals: Success: ${successCount}, Errors: ${errorCount}, Skipped: ${skippedCount}, Matchings updated: ${matchingUpdatedCount}, total processed: ${processedCount}`);

      // Move to next batch
      skip += BATCH_SIZE;
    }

    // Print final stats
    console.log("\nMigration completed!");
    console.log(`Total prospects processed: ${processedCount}`);
    console.log(`Successful migrations: ${successCount}`);
    console.log(`Skipped count: ${skippedCount}`);
    console.log(`Failed migrations: ${errorCount}`);
    console.log(`Client-prospect matchings updated: ${matchingUpdatedCount}`);
  } catch (error) {
    console.error("Migration failed:", error);
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * Upsert AmazonProspect based on email or LinkedIn
 */
async function upsertAmazonProspect(prospect, seller_id) {
  // Prepare the data for upsert
  const prospectData = {
    person_name: prospect.person_name,
    person_linkedin: prospect.person_linkedin,
    email: prospect.email,
    job_title: prospect.job_title,
    source: prospect.source,
    sources: prospect.sources,
    email_status: prospect.email_status,
    seller_id: seller_id,
  };

  // Try to find existing AmazonProspect by email or LinkedIn
  let existingProspect = null;
  let condition = {};
  let amazonProspect = null;

  condition = {
    email: prospect.email,
    person_linkedin: prospect.person_linkedin,
  };

  existingProspect = await prisma.amazonProspect.findFirst({
    where: condition,
  });

  if (!existingProspect) {
    amazonProspect = await prisma.amazonProspect.create({
      data: prospectData,
    });
  } else {
    amazonProspect = await prisma.amazonProspect.update({
      where: { prospect_id: existingProspect.prospect_id },
      data: prospectData,
    });
    skipCount += 1;
  }
  return amazonProspect;
}

/**
 * Update ClientsProspectsMatching records to point to the AmazonProspect
 * @returns {number} The number of matching records updated
 */
async function updateClientsProspectsMatching(
  clientsProspects,
  amazonProspectId
) {
  let updatedCount = 0;
  for (const match of clientsProspects) {
    await prisma.clientsProspectsMatching.update({
      where: { match_id: match.match_id },
      data: { amazon_prospect_id: amazonProspectId },
    });
    updatedCount++;
  }
  return updatedCount;
}

// Run the migration
if (require.main === module) {
  migrateProspectData().catch((e) => {
    console.error("Migration script error:", e);
    process.exit(1);
  });
}

// Export for testing or use in other modules
module.exports = {
  migrateProspectData,
  upsertAmazonProspect,
  updateClientsProspectsMatching,
};