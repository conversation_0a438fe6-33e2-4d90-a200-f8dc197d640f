const cron = require("node-cron");
const processSellerPage = require("../utils/processSellerPage");
const prisma = require("../database/prisma/getPrismaClient");

// jobWorker();

async function jobWorker() {
  // Uncomment the cron.schedule line for periodic execution
  cron.schedule("* * * * *", async () => {
    await runJobWorker();
  });

  // Optional: Running the jobWorker instantly for testing purposes
  runJobWorker(); // This will run your job instantly without waiting for the cron schedule.
}

async function runJobWorker() {
  try {
    console.log("Running Scheduled job");

    const processingJobs = await prisma.job.findMany({
      where: {
        status: "in_progress",
      },
    });
    if (processingJobs.length > 0) {
      console.log(
        "Previous job still running. Skipping this iteration for CSV Data Fetching.",
      );
      return;
    }

    const jobs = await prisma.job.findMany({
      where: {
        OR: [{ status: "pending" }],
      },
    });

    for (const job of jobs) {
      console.log("Processing Job CSV data for job id:", job.id);

      await prisma.job.update({
        where: {
          id: job.id,
        },
        data: {
          status: "in_progress",
        },
      });

      const outputData = await prisma.outputData.findMany({
        where: {
          jobId: job.id,
          inputStatus: "pending", // Process only pending sellers
        },
      });

      // console.log({ outputData });
      for (const seller of outputData) {
        try {
          // Process the seller page
          await processSellerPage(seller, job.id);

          // Mark seller as "success" if no errors occurred
          await prisma.outputData.update({
            where: {
              id: seller.id,
            },
            data: {
              inputStatus: "success",
            },
          });
        } catch (error) {
          console.error(`Error processing seller ${seller.sellerName}:`, error);

          // Mark seller as "error" in case of failure
          await prisma.outputData.update({
            where: {
              id: seller.id,
            },
            data: {
              inputStatus: "error",
            },
          });
        }
      }

      // Check if all sellers in this job are completed
      const incompleteSellers = await prisma.outputData.findMany({
        where: {
          jobId: 1,
          inputStatus: "pending",
        },
      });

      // If no incomplete sellers, mark job as "completed", else "error"
      if (incompleteSellers.length === 0) {
        await prisma.job.update({
          where: {
            id: job.id,
          },
          data: {
            status: "completed",
          },
        });
        console.log(`-----ALL DATA PROCESSED FOR JOB ID:${job.id}  -------`);
      }
    }
  } catch (error) {
    console.error("Error in processing job:", error);
  }
}

module.exports = jobWorker;
