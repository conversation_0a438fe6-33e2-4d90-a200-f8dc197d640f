const prisma = require("../database/prisma/getPrismaClient");

/**
 * <PERSON>ript to extract seller ID from seller URLs and update the Product table
 * Extracts seller ID from URLs like: https://www.amazon.com/sp?seller=alvtcb5w0q3g2
 */
async function updateSellerIdFromUrl() {
    console.log("Starting to update seller IDs from seller URLs...");

    try {
        // Find all products that have a sellerUrl but no sellerId
        const productsWithUrlNoId = await prisma.product.findMany({
            where: {
                sellerUrl: {
                    not: null,
                },
                sellerId: null,
            },
            select: {
                id: true,
                sellerUrl: true,
                sellerId: true,
            },
        });

        console.log(`Found ${productsWithUrlNoId.length} products with seller URLs but no seller ID`);

        if (productsWithUrlNoId.length === 0) {
            console.log("No products found that need seller ID updates.");
            return;
        }

        // Function to extract seller ID from URL
        function extractSellerIdFromUrl(url) {
            if (!url) return null;

            try {
                // Handle different URL formats
                const urlObj = new URL(url);

                // Check if it's an Amazon seller URL with /sp path
                if (urlObj.hostname.includes('amazon') && urlObj.pathname === '/sp') {
                    const sellerParam = urlObj.searchParams.get('seller');
                    if (sellerParam) {
                        return sellerParam;
                    }
                }

                // Check if it's an Amazon search URL with 'me' parameter (seller ID)
                if (urlObj.hostname.includes('amazon') && urlObj.pathname === '/s') {
                    const meParam = urlObj.searchParams.get('me');
                    if (meParam) {
                        return meParam;
                    }
                }

                // Alternative: use regex to extract seller ID from 'seller' parameter
                const sellerMatch = url.match(/[?&]seller=([^&]+)/);
                if (sellerMatch) {
                    return sellerMatch[1];
                }

                // Alternative: use regex to extract seller ID from 'me' parameter
                const meMatch = url.match(/[?&]me=([^&]+)/);
                if (meMatch) {
                    return meMatch[1];
                }

                return null;
            } catch (error) {
                console.error(`Error parsing URL: ${url}`, error.message);
                return null;
            }
        }

        let updatedCount = 0;
        let errorCount = 0;
        const errors = [];

        // Process each product
        for (const product of productsWithUrlNoId) {
            try {
                const sellerId = extractSellerIdFromUrl(product.sellerUrl);

                if (sellerId) {
                    // Update the product with the extracted seller ID
                    await prisma.product.update({
                        where: { id: product.id },
                        data: { sellerId: sellerId },
                    });

                    updatedCount++;
                    console.log(`Updated product ${product.id}: extracted seller ID "${sellerId}" from URL "${product.sellerUrl}"`);
                } else {
                    errorCount++;
                    errors.push({
                        productId: product.id,
                        url: product.sellerUrl,
                        reason: "Could not extract seller ID from URL"
                    });
                    console.log(`Could not extract seller ID from URL: ${product.sellerUrl} (Product ID: ${product.id})`);
                }
            } catch (error) {
                errorCount++;
                errors.push({
                    productId: product.id,
                    url: product.sellerUrl,
                    reason: error.message
                });
                console.error(`Error processing product ${product.id}:`, error.message);
            }
        }

        // Summary
        console.log("\n=== UPDATE SUMMARY ===");
        console.log(`Total products processed: ${productsWithUrlNoId.length}`);
        console.log(`Successfully updated: ${updatedCount}`);
        console.log(`Errors: ${errorCount}`);

        if (errors.length > 0) {
            console.log("\n=== ERRORS ===");
            errors.slice(0, 10).forEach((error, index) => {
                console.log(`${index + 1}. Product ID: ${error.productId}, URL: ${error.url}, Reason: ${error.reason}`);
            });
            if (errors.length > 10) {
                console.log(`... and ${errors.length - 10} more errors`);
            }
        }

        // Also check for products that already have sellerId but might need URL updates
        const productsWithIdNoUrl = await prisma.product.findMany({
            where: {
                sellerId: {
                    not: null,
                },
                sellerUrl: null,
            },
            select: {
                id: true,
                sellerId: true,
                sellerUrl: true,
            },
        });

        console.log(`\nFound ${productsWithIdNoUrl.length} products with seller ID but no seller URL`);

    } catch (error) {
        console.error("Error updating seller IDs:", error);
        throw error;
    } finally {
        await prisma.$disconnect();
    }
}

/**
 * Function to generate seller URL from seller ID
 * Useful for products that have sellerId but no sellerUrl
 */
async function generateSellerUrlFromId() {
    console.log("\nGenerating seller URLs from seller IDs...");

    try {
        const productsWithIdNoUrl = await prisma.product.findMany({
            where: {
                sellerId: {
                    not: null,
                },
                sellerUrl: null,
            },
            select: {
                id: true,
                sellerId: true,
                marketplace: true,
            },
        });

        console.log(`Found ${productsWithIdNoUrl.length} products with seller ID but no seller URL`);

        if (productsWithIdNoUrl.length === 0) {
            console.log("No products found that need seller URL generation.");
            return;
        }

        let updatedCount = 0;

        for (const product of productsWithIdNoUrl) {
            try {
                // Generate Amazon seller URL
                const sellerUrl = `https://www.amazon.com/sp?seller=${product.sellerId}`;

                await prisma.product.update({
                    where: { id: product.id },
                    data: {
                        sellerUrl: sellerUrl,
                        marketplace: product.marketplace || 'Amazon'
                    },
                });

                updatedCount++;
                console.log(`Generated URL for product ${product.id}: ${sellerUrl}`);
            } catch (error) {
                console.error(`Error generating URL for product ${product.id}:`, error.message);
            }
        }

        console.log(`Generated ${updatedCount} seller URLs`);

    } catch (error) {
        console.error("Error generating seller URLs:", error);
        throw error;
    }
}

// Run the script if called directly
if (require.main === module) {
    updateSellerIdFromUrl()
        .then(() => generateSellerUrlFromId())
        .then(() => {
            console.log("\nScript completed successfully");
            process.exit(0);
        })
        .catch((error) => {
            console.error("Script failed:", error);
            process.exit(1);
        });
}

module.exports = { updateSellerIdFromUrl, generateSellerUrlFromId }; 