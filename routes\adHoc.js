const express = require("express");
const router = express.Router();
const multer = require("multer");
const upload = multer({ dest: "uploads/" });
const processCSV = require("../services/matchData/storeleads");
const sellerLeadsMatch = require("../services/matchData/sellerLeadMatching");
const matchAndMap = require("../services/matchData/sellerLeadMatching");
const {
  sendSlackNotification,
  getThreadBySlackTs,
  slackToGitHubMapping,
} = require("../services/SlackNotifier/githubSlack");
const prisma = require("../database/prisma/getPrismaClient");
const { adminAuth } = require("../middlewares/jwt");
const { fetchGoogleSheetData, parseCSV } = require("../utils/upload.utils");
const {
  postSlackMessageAsGitHubComment,
} = require("../services/SlackNotifier/githubCommentPoster");
/**
 * @swagger
 * /api/matchSeller:
 *   post:
 *     summary: Process seller matching CSV file
 *     description: |
 *       Uploads a CSV file and processes it for seller matching. It retrieves companies by name and Amazon seller ID, matches entries from the uploaded CSV, and returns a new CSV with matched seller data.
 *
 *       The process:
 *       1. Fetches all companies from the database
 *       2. Creates a map of company names to Amazon seller IDs
 *       3. Matches merchant names from the CSV with company names
 *       4. For matched companies, fetches complete data including SellerCountryMatching records
 *       5. Appends company and seller data to the original CSV rows
 *       6. Returns an enriched CSV with all matched data
 *
 *       - The CSV must contain a `merchantName` column for matching.
 *       - For each match, all company fields and related SellerCountryMatching data are added.
 *       - For unmatched rows, empty fields are added to maintain consistent structure.
 *     tags: [AdHoc]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               csvFile:
 *                 type: string
 *                 format: binary
 *                 description: >
 *                   CSV file to process with merchantName column.
 *                   - [Download sample CSV](/examples/match_seller_input.csv)
 *     responses:
 *       200:
 *         description: CSV processed successfully
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 *             example: |
 *               merchantName,domain,company_id,amazon_seller_id,name,website,website_status,SellerCountryMatching_amazon_seller_id,SellerCountryMatching_smartscout_country,SellerCountryMatching_seller_url
 *               "Acme Inc","acme.com","123","A1B2C3D4E5","Acme Inc","https://acme.com","Final Correct","A1B2C3D4E5","US","https://www.amazon.com/sp?seller=A1B2C3D4E5"
 *               "XYZ Corp","xyz.com","","","","","","","",""
 *         headers:
 *           Content-Disposition:
 *             schema:
 *               type: string
 *             description: Attachment header with filename
 *             example: attachment; filename="matched_results_input_1234567890.csv"
 *       400:
 *         description: Bad Request - No file uploaded
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *             example: No file uploaded
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 */
router.post(
  "/api/matchSeller",
  adminAuth,
  upload.single("csvFile"),
  async (req, res) => {
    processCSV(req, res);
  }
);

/**
 * @swagger
 * /api/company-data:
 *   post:
 *     summary: Enrich company data from CSV
 *     description: |
 *       Uploads a CSV file containing Amazon seller IDs and enriches it with additional data from the database.
 *
 *       The process:
 *       1. Reads and parses the input CSV file
 *       2. Transforms the data using the sellerLeadMatchSchemaMap
 *       3. Extracts seller IDs and fetches matching company records from the database
 *       4. Adds "Website Status" and "Derived Estimated Sales" fields to each row based on the matched company
 *       5. Returns the enriched CSV with the additional fields
 *
 *       - The CSV must contain a column that maps to "seller_id" (Amazon Seller ID)
 *       - For unmatched seller IDs, "Website Status" will be "Not Found" and "Derived Estimated Sales" will be "N/A"
 *     tags: [AdHoc]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: |
 *                   CSV file to enrich with company data.
 *                   Must contain a column for Amazon Seller ID.
 *                   - [Download sample CSV](/examples/company_data.csv)
 *     responses:
 *       200:
 *         description: CSV enriched successfully
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 *             example: |
 *               seller_name,seller_url,seller_id,client_name,sheet_name,Website Status,Derived Estimated Sales
 *               Acme Inc,https://amazon.com/sp?seller=A1B2C3D4E5,A1B2C3D4E5,Client A,Sheet 1,Final Correct,1500000
 *               XYZ Corp,https://amazon.com/sp?seller=F6G7H8I9J0,F6G7H8I9J0,Client B,Sheet 2,Not Found,N/A
 *         headers:
 *           Content-Disposition:
 *             schema:
 *               type: string
 *             description: Attachment header with filename
 *             example: attachment; filename="enriched_data.csv"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 */ router.post(
  "/api/company-data",
  adminAuth,
  upload.single("file"),
  async (req, res) => {
    try {
      let rawInputData;

      const { sheetId } = req.body;

      if (sheetId) {
        rawInputData = await fetchGoogleSheetData(sheetId);
      } else if (req.file) {
        rawInputData = await parseCSV(req.file.path);
      } else {
        return res
          .status(400)
          .send("Either a CSV file or sheetId must be provided");
      }

      const csvOutput = await matchAndMap(rawInputData);
      console.log(csvOutput);
      res.header("Content-Type", "text/csv");
      res.attachment("enriched_data.csv");
      return res.send(csvOutput);
    } catch (error) {
      console.error("Error processing CSV:", error);
      res.status(500).send("Error processing CSV file");
    }
  }
);

/**
 * @swagger
 * /api/github/slack-notification:
 *   post:
 *     summary: Send GitHub events to Slack
 *     description: |
 *       Processes GitHub webhooks and sends notifications to Slack, this is for sending notifications about issues that are created or edited on slack.
 *
 *       **Note: This API does not require authentication** - it is designed to be called directly by GitHub webhooks.
 *     tags: [AdHoc]
 *     security: []  # This explicitly indicates no security requirements
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               action:
 *                 type: string
 *                 enum: [opened, created, edited]
 *                 description: The action that triggered the webhook
 *                 example: "created"
 *               repository:
 *                 type: object
 *                 properties:
 *                   full_name:
 *                     type: string
 *                     description: Full name of the repository (e.g., 'owner/repo')
 *                     example: "aguyran/test-repo"
 *                   html_url:
 *                     type: string
 *                     format: uri
 *                     description: URL to the repository
 *                     example: "https://github.com/aguyran/test-repo"
 *               issue:
 *                 type: object
 *                 properties:
 *                   number:
 *                     type: integer
 *                     description: Issue number
 *                     example: 42
 *                   title:
 *                     type: string
 *                     description: Issue title
 *                     example: "Fix login bug"
 *                   html_url:
 *                     type: string
 *                     format: uri
 *                     description: URL to the issue
 *                     example: "https://github.com/aguyran/test-repo/issues/42"
 *                   body:
 *                     type: string
 *                     description: Issue description
 *                     example: "The login page is not working properly"
 *                   user:
 *                     type: object
 *                     properties:
 *                       login:
 *                         type: string
 *                         description: GitHub username of the issue creator
 *                         example: "aguyran"
 *               comment:
 *                 type: object
 *                 properties:
 *                   body:
 *                     type: string
 *                     description: Comment text
 *                     example: "I've identified the root cause"
 *                   user:
 *                     type: object
 *                     properties:
 *                       login:
 *                         type: string
 *                         description: GitHub username of the commenter
 *                         example: "aguyran"
 *                   html_url:
 *                     type: string
 *                     format: uri
 *                     description: URL to the comment
 *                     example: "https://github.com/aguyran/test-repo/issues/42#issuecomment-123456"
 *               changes:
 *                 type: object
 *                 properties:
 *                   body:
 *                     type: object
 *                     properties:
 *                       from:
 *                         type: string
 *                         description: Previous content before edit
 *                         example: "Original comment text"
 *             required:
 *               - action
 *               - repository
 *     responses:
 *       200:
 *         description: Notification processed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Notification sent to Slack"
 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 */ router.post(
  "/api/github/slack-notification",

  async (req, res) => {
    try {
      const payload = req.body;

      if (
        (payload.action === "created" || payload.action === "edited") &&
        payload.comment
      ) {
        await sendSlackNotification(payload);
        return res.status(200).json({ message: "Notification sent to Slack" });
      }

      if (payload.action === "opened" && payload.issue) {
        await sendSlackNotification(payload);
        return res
          .status(200)
          .json({ message: "Issue created notification sent to Slack" });
      }

      return res.status(200).json({ message: "No relevant action detected" });
    } catch (error) {
      console.error(" Error processing webhook:", error.message);
      res.status(500).json({ error: "Failed to process webhook" });
    }
  }
);

/**
 * @swagger
 * /api/review-jobs:
 *   post:
 *     summary: Create a new review job
 *     description: Upload a file to create a new Amazon reviews processing job. The file is stored in the database and the job status is set to "queued".
 *     deprecated: true
 *     tags: [Review(Lex)]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: |
 *                   CSV file with the following structure:
 *                   - "ASIN": Amazon Standard Identification Number (required)
 *                   - "Product Name": Name of the product (required)
 *                   - "Brand": Brand name of the product
 *                   - "Category": Product category
 *
 *                   [Download example CSV file](/examples/review-input.csv)
 *             required:
 *               - file
 *     responses:
 *       201:
 *         description: Job created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Amazon Reviews Job created successfully"
 *                 jobId:
 *                   type: integer
 *                   example: 42
 *                 status:
 *                   type: string
 *                   example: "queued"
 *       400:
 *         description: No file uploaded
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "No file uploaded"
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Unauthorized - No token provided"
 *       403:
 *         description: Forbidden - Not an admin or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Forbidden - Admin access required"
 *       500:
 *         description: Error creating job
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Failed to create Amazon Reviews job"
 *                 details:
 *                   type: string
 *                   example: "Database connection error"
 */
router.post(
  "/api/review-jobs",
  adminAuth,
  upload.single("file"),
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: "No file uploaded" });
      }

      const inputFilePath = req.file.path;
      const originalFilename = req.file.originalname;

      const job = await prisma.reviewJob.create({
        data: {
          filename: originalFilename,
          inputFilePath,
          status: "queued",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });

      res.status(201).json({
        message: "Amazon Reviews Job created successfully",
        jobId: job.id,
        status: job.status,
      });
    } catch (error) {
      console.error("Error creating Amazon Reviews job:", error);
      res.status(500).json({
        error: "Failed to create Amazon Reviews job",
        details: error.message,
      });
    }
  }
);

/**
 * @swagger
 * /api/review-jobs:
 *   get:
 *     summary: Get all review jobs
 *     description: Retrieve a list of all Amazon review processing jobs from the database.
 *     deprecated: true
 *     tags: [Review(Lex)]
 *     responses:
 *       200:
 *         description: List of jobs
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: integer
 *                     example: 42
 *                   filename:
 *                     type: string
 *                     example: "amazon_reviews.csv"
 *                   status:
 *                     type: string
 *                     enum: [queued, processing, completed, failed]
 *                     example: "completed"
 *                   error:
 *                     type: string
 *                     nullable: true
 *                     example: null
 *                   createdAt:
 *                     type: string
 *                     format: date-time
 *                     example: "2023-10-15T14:30:00Z"
 *                   updatedAt:
 *                     type: string
 *                     format: date-time
 *                     example: "2023-10-15T15:45:00Z"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 */
router.get("/api/review-jobs", adminAuth, async (req, res) => {
  try {
    const jobs = await prisma.reviewJob.findMany({
      select: {
        id: true,
        filename: true,
        status: true,
        error: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: { createdAt: "desc" },
    });

    res.json(jobs);
  } catch (error) {
    console.error("Error fetching Amazon Reviews jobs:", error);
    res.status(500).json({ error: "Failed to fetch Amazon Reviews jobs" });
  }
});

/**
 * @swagger
 * /api/review-jobs/{id}:
 *   get:
 *     summary: Get a specific review job
 *     description: Retrieve details of a specific Amazon review processing job by its ID.
 *     deprecated: true
 *     tags: [Review(Lex)]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Job ID
 *     responses:
 *       200:
 *         description: Job details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: integer
 *                   example: 42
 *                 filename:
 *                   type: string
 *                   example: "amazon_reviews.csv"
 *                 status:
 *                   type: string
 *                   enum: [queued, processing, completed, failed]
 *                   example: "completed"
 *                 error:
 *                   type: string
 *                   nullable: true
 *                   example: null
 *                 createdAt:
 *                   type: string
 *                   format: date-time
 *                   example: "2023-10-15T14:30:00Z"
 *                 updatedAt:
 *                   type: string
 *                   format: date-time
 *                   example: "2023-10-15T15:45:00Z"
 *       404:
 *         description: Job not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Amazon Reviews Job not found"
 *       500:
 *         description: Error fetching job
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Failed to fetch Amazon Reviews job"
 */
router.get("/api/review-jobs/:id", adminAuth, async (req, res) => {
  try {
    const jobId = parseInt(req.params.id);
    const job = await prisma.reviewJob.findUnique({
      where: { id: jobId },
      select: {
        id: true,
        filename: true,
        status: true,
        error: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!job) {
      return res.status(404).json({ error: "Amazon Reviews Job not found" });
    }

    res.json(job);
  } catch (error) {
    console.error("Error fetching Amazon Reviews job:", error);
    res.status(500).json({ error: "Failed to fetch Amazon Reviews job" });
  }
});

/**
 * @swagger
 * /api/review-jobs/{id}/download:
 *   get:
 *     summary: Download processed CSV
 *     description: Download the processed CSV file for a completed review job.
 *     deprecated: true
 *     tags: [Review(Lex)]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Job ID
 *     responses:
 *       200:
 *         description: CSV file containing processed review data
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 *             example: |
 *               "ASIN","Product Name","Brand","Category","Review Count","Average Rating","Positive Reviews","Negative Reviews","Neutral Reviews","Top Keywords"
 *               "B07XYZ123","Wireless Earbuds","TechBrand","Electronics","245","4.2","180","40","25","battery life, sound quality, comfort"
 *               "B08ABC456","Smart Watch","WearTech","Wearables","189","3.8","120","50","19","battery, notifications, fitness tracking"
 *         headers:
 *           Content-Disposition:
 *             schema:
 *               type: string
 *             description: Attachment header with filename
 *             example: attachment; filename="input_processed_reviews.csv"
 *           Content-Type:
 *             schema:
 *               type: string
 *             example: text/csv
 *       400:
 *         description: Job not completed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Amazon Reviews Job not completed yet"
 *                 status:
 *                   type: string
 *                   example: "processing"
 *       404:
 *         description: Job or output not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Output CSV not found"
 *       500:
 *         description: Error downloading file
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Failed to download Amazon Reviews result"
 */
router.get("/api/review-jobs/:id/download", adminAuth, async (req, res) => {
  try {
    const jobId = parseInt(req.params.id);
    const job = await prisma.reviewJob.findUnique({
      where: { id: jobId },
    });

    if (!job) {
      return res.status(404).json({ error: "Amazon Reviews Job not found" });
    }

    if (job.status !== "completed") {
      return res.status(400).json({
        error: "Amazon Reviews Job not completed yet",
        status: job.status,
      });
    }

    if (!job.outputCSV) {
      return res.status(404).json({ error: "Output CSV not found" });
    }

    res.setHeader("Content-Type", "text/csv");
    const inputFileName = job.filename
      .split(".")[0]
      .replace(/[^a-zA-Z0-9]/g, "_");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=${inputFileName}_processed_reviews.csv`
    );

    res.send(Buffer.from(job.outputCSV));
  } catch (error) {
    console.error("Error downloading Amazon Reviews result:", error);
    res.status(500).json({ error: "Failed to download Amazon Reviews result" });
  }
});

router.post("/api/slack/events", express.json(), async (req, res) => {
  const { type, challenge, event } = req.body;

  if (type === "url_verification") {
    return res.status(200).json({ challenge });
  }

  if (event && event.type === "message" && !event.bot_id) {
    const { text, user, channel, thread_ts, ts } = event;

    if (thread_ts) {
      try {
        const thread = await getThreadBySlackTs(thread_ts);

        if (thread) {
          const { repo, issueNumber } = thread;
          const slackUserId = user;
          const commentBody = `**From Slack User \`<@${slackToGitHubMapping[slackUserId]}>\`:**\n\n${text}`;

          await postSlackMessageAsGitHubComment(repo, issueNumber, commentBody);
        } else {
          console.log(
            `No GitHub issue found for Slack thread_ts: ${thread_ts}`
          );
        }
      } catch (error) {
        console.error("Error processing Slack message event:", error);

        return res.status(404).json({
          error: "Failed to process Slack message event",
          details: error.message || String(error),
        });
      }
    } else {
      console.log(
        "Ignoring Slack message without thread_ts as it doesn't belong to an issue thread."
      );
    }
  }

  res.status(200).send();
});

module.exports = router;
