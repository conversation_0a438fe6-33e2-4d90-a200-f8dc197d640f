const BasePreprocessor = require("./basePreprocessor");
const CompanyPreprocessor = require("./companyPreprocessor");
const ProspectPreprocessor = require("./prospectPreprocessor");
const AmazonSellerPreprocessor = require("./amazonSellerPreprocessor");
const MatchingPreprocessor = require("./matchingPreprocessor");
const AmazonProspectPreprocessor = require("./amazonProspectPreprocessor");

/**
 * Factory function to get the appropriate preprocessor based on type
 * @param {string} type - The type of data being processed
 * @returns {BasePreprocessor} - The appropriate preprocessor instance
 */
function getPreprocessor(type) {
  switch (type) {
    case "company":
      return new CompanyPreprocessor();
    case "prospect":
      return new ProspectPreprocessor();
    case "amazon_seller":
      return new AmazonSellerPreprocessor();
    case "amazon_prospect":
      return new AmazonProspectPreprocessor();
    case "matching":
      return new MatchingPreprocessor();
    default:
      return new BasePreprocessor();
  }
}

module.exports = {
  getPreprocessor,
  BasePreprocessor,
  CompanyPreprocessor,
  ProspectPreprocessor,
  AmazonSellerPreprocessor,
  AmazonProspectPreprocessor,
  MatchingPreprocessor,
};
