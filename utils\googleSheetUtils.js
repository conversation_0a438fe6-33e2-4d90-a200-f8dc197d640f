const { google } = require("googleapis");
const fs = require("fs");
const path = require("path");
const util = require("util");
const unlink = util.promisify(fs.unlink);
const { parse } = require("csv-parse/sync");
const AdmZip = require("adm-zip");

// Use service account JSON from environment variable
const serviceAccountBase64 = process.env.GOOGLE_SERVICE_ACCOUNT_KEY;

if (!serviceAccountBase64) {
  throw new Error("Missing GOOGLE_SERVICE_ACCOUNT_KEY environment variable.");
}

// Parse service account credentials
const serviceAccountJSON = JSON.parse(
  Buffer.from(serviceAccountBase64, "base64").toString("utf-8")
);

// Set up Google auth
const auth = new google.auth.GoogleAuth({
  credentials: serviceAccountJSON,
  scopes: [
    "https://www.googleapis.com/auth/spreadsheets",
    "https://www.googleapis.com/auth/drive",
  ],
});

const sheets = google.sheets({ version: "v4", auth });
const drive = google.drive({ version: "v3", auth });

const CHUNK_SIZE = 3000;

/**
 * Create a Google Sheet from CSV file, uploading in chunks of 5000 rows.
 * @param {string} csvFilePath - Path to the CSV file
 * @param {string} sheetTitle - Title for the Google Sheet
 * @returns {Promise<{sheetId: string, sheetUrl: string}>} - Sheet ID and URL
 */
const createSheetFromCsv = async (csvFilePath, sheetTitle = null) => {
  try {
    // Read and parse the CSV file
    const csvData = fs.readFileSync(csvFilePath, "utf8");
    const records = parse(csvData, { columns: true, skip_empty_lines: true });
    
    if (!records.length) {
      throw new Error("No data found in CSV file");
    }
    
    const headers = Object.keys(records[0]);
    const rows = records.map(record => headers.map(header => record[header] || ""));

    // Create a new Google Sheet
    const title = sheetTitle || `CSV Import - ${path.basename(csvFilePath)} - ${new Date().toISOString()}`;
    const sheetResponse = await sheets.spreadsheets.create({
      requestBody: {
        properties: { title }
      }
    });

    const sheetId = sheetResponse.data.spreadsheetId;
    if(!sheetId){
      throw new Error("Failed to create Google Sheet");
    }else{
      // Make the sheet public
      await drive.permissions.create({
        fileId: sheetId,
        requestBody: {
          role: "writer",
          type: "anyone"
        }
      });
      console.log("Sheet created successfully:", sheetId);
      console.log("Sheet link:", `https://docs.google.com/spreadsheets/d/${sheetId}`);
    }

    // Upload the header + first chunk
    const firstChunk = rows.slice(0, CHUNK_SIZE);
    await sheets.spreadsheets.values.update({
      spreadsheetId: sheetId,
      range: "Sheet1!A1",
      valueInputOption: "RAW",
      requestBody: {
        values: [
          headers,
          ...firstChunk
        ]
      }
    });

    // Upload remaining chunks
    let start = CHUNK_SIZE;
    while (start < rows.length) {
      const chunk = rows.slice(start, start + CHUNK_SIZE);
      console.log("Uploading chunk:", start, start + CHUNK_SIZE);
      await sheets.spreadsheets.values.append({
        spreadsheetId: sheetId,
        range: "Sheet1",
        valueInputOption: "RAW",
        insertDataOption: "INSERT_ROWS",
        requestBody: {
          values: chunk
        }
      });
      start += CHUNK_SIZE;
    }
    
    return {
      sheetId,
      sheetUrl: `https://docs.google.com/spreadsheets/d/${sheetId}`
    };
  } catch (error) {
    console.error("Error creating sheet from CSV:", error);
    throw error;
  }
};

/**
 * Process a ZIP file containing multiple CSV files and add them as sheets to a single Google Sheet
 * @param {string} zipFilePath - Path to the ZIP file
 * @param {string} sheetTitle - Title for the Google Sheet
 * @returns {Promise<{sheetId: string, sheetUrl: string}>} - Sheet ID and URL
 */
const processZipToSheet = async (zipFilePath, sheetTitle = null) => {
  const tempDir = path.join("uploads", "temp_" + Date.now());
  if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir, { recursive: true });
  }
  
  try {
    // Extract ZIP contents
    const zip = new AdmZip(zipFilePath);
    zip.extractAllTo(tempDir, true);
    
    // Find all CSV files
    const files = fs.readdirSync(tempDir);
    const csvFiles = files.filter(file => file.toLowerCase().endsWith('.csv'));
    
    if (csvFiles.length === 0) {
      throw new Error("No CSV files found in the ZIP archive");
    }
    
    // Create a new Google Sheet
    const title = sheetTitle || `ZIP Import - ${path.basename(zipFilePath)} - ${new Date().toISOString()}`;
    const sheetResponse = await sheets.spreadsheets.create({
      requestBody: {
        properties: { title }
      }
    });
    
    const sheetId = sheetResponse.data.spreadsheetId;
    
    // Rename default sheet
    await sheets.spreadsheets.batchUpdate({
      spreadsheetId: sheetId,
      requestBody: {
        requests: [
          {
            updateSheetProperties: {
              properties: {
                sheetId: 0,
                title: path.basename(csvFiles[0], '.csv')
              },
              fields: "title"
            }
          }
        ]
      }
    });
    
    // Process first CSV file for the default sheet
    const firstCsvPath = path.join(tempDir, csvFiles[0]);
    const firstCsvData = fs.readFileSync(firstCsvPath, "utf8");
    const firstRecords = parse(firstCsvData, { columns: true, skip_empty_lines: true });
    
    if (firstRecords.length > 0) {
      const headers = Object.keys(firstRecords[0]);
      const rows = firstRecords.map(record => headers.map(header => record[header] || ""));
      
      await sheets.spreadsheets.values.update({
        spreadsheetId: sheetId,
        range: `${path.basename(csvFiles[0], '.csv')}!A1`,
        valueInputOption: "RAW",
        requestBody: {
          values: [
            headers,
            ...rows
          ]
        }
      });
    }
    
    // Process remaining CSV files
    for (let i = 1; i < csvFiles.length; i++) {
      const csvPath = path.join(tempDir, csvFiles[i]);
      const csvData = fs.readFileSync(csvPath, "utf8");
      const records = parse(csvData, { columns: true, skip_empty_lines: true });
      
      if (records.length > 0) {
        const sheetTitle = path.basename(csvFiles[i], '.csv');
        
        // Add a new sheet
        await sheets.spreadsheets.batchUpdate({
          spreadsheetId: sheetId,
          requestBody: {
            requests: [
              {
                addSheet: {
                  properties: {
                    title: sheetTitle
                  }
                }
              }
            ]
          }
        });
        
        const headers = Object.keys(records[0]);
        const rows = records.map(record => headers.map(header => record[header] || ""));
        
        await sheets.spreadsheets.values.update({
          spreadsheetId: sheetId,
          range: `${sheetTitle}!A1`,
          valueInputOption: "RAW",
          requestBody: {
            values: [
              headers,
              ...rows
            ]
          }
        });
      }
    }
    
    // Make the sheet public
    await drive.permissions.create({
      fileId: sheetId,
      requestBody: {
        role: "writer",
        type: "anyone"
      }
    });
    
    return {
      sheetId,
      sheetUrl: `https://docs.google.com/spreadsheets/d/${sheetId}`
    };
  } catch (error) {
    console.error("Error processing ZIP to sheet:", error);
    throw error;
  } finally {
    // Clean up temp directory
    if (fs.existsSync(tempDir)) {
      const files = fs.readdirSync(tempDir);
      for (const file of files) {
        fs.unlinkSync(path.join(tempDir, file));
      }
      fs.rmdirSync(tempDir);
    }
  }
};

// Helper function to upload CSV data to a sheet in chunks
async function uploadCsvToSheet(spreadsheetId, sheetName, records) {
  if (!records.length) return;
  const headers = Object.keys(records[0]);
  const rows = records.map(record => headers.map(header => record[header] || ""));

  // Header + first chunk
  const firstChunk = rows.slice(0, CHUNK_SIZE);
  await sheets.spreadsheets.values.update({
    spreadsheetId,
    range: `${sheetName}!A1`,
    valueInputOption: "RAW",
    requestBody: {
      values: [headers, ...firstChunk]
    }
  });
  console.log("Uploaded First chunk:", CHUNK_SIZE);

  // Remaining chunks
  let start = CHUNK_SIZE;
  while (start < rows.length) {
    console.log("Uploading chunk:", start, start + CHUNK_SIZE);
    const chunk = rows.slice(start, start + CHUNK_SIZE);
    await sheets.spreadsheets.values.append({
      spreadsheetId,
      range: sheetName,
      valueInputOption: "RAW",
      insertDataOption: "INSERT_ROWS",
      requestBody: { values: chunk }
    });
    start += CHUNK_SIZE;
  }
}

/**
 * Create a Google Sheet with multiple sheets from multiple CSV files
 * @param {Array<{path: string, name: string}>} csvFiles - Array of CSV file paths and sheet names
 * @param {string} sheetTitle - Title for the Google Sheet
 * @returns {Promise<{sheetId: string, sheetUrl: string, sheets: Array<{name: string, index: number}>}>} - Sheet ID, URL and sheet information
 */
const createMultiSheetFromCsvs = async (csvFiles, sheetTitle) => {
  try {
    if (!csvFiles.length) {
      throw new Error("No CSV files provided");
    }
    
    // Create a new Google Sheet
    const title = sheetTitle || `Multi-Sheet Import - ${new Date().toISOString()}`;
    const sheetResponse = await sheets.spreadsheets.create({
      requestBody: {
        properties: { title }
      }
    });
    console.log("Sheet created successfully: ", sheetResponse.data.spreadsheetId);
    const sheetId = sheetResponse.data.spreadsheetId;
    const createdSheets = [];
    
    // Rename the default sheet to the first CSV name
    const firstSheetName = csvFiles[0].name;
    await sheets.spreadsheets.batchUpdate({
      spreadsheetId: sheetId,
      requestBody: {
        requests: [
          {
            updateSheetProperties: {
              properties: {
                sheetId: 0,
                title: firstSheetName
              },
              fields: "title"
            }
          }
        ]
      }
    });
    
    createdSheets.push({ name: firstSheetName, index: 0 });
    
    // Process first CSV file for the default sheet (chunked)
    const firstCsvPath = csvFiles[0].path;
    const firstCsvData = fs.readFileSync(firstCsvPath, "utf8");
    const firstRecords = parse(firstCsvData, { columns: true, skip_empty_lines: true });
    await uploadCsvToSheet(sheetId, firstSheetName, firstRecords);
    
    // Process remaining CSV files (chunked)
    for (let i = 1; i < csvFiles.length; i++) {
      const { path: csvPath, name: sheetName } = csvFiles[i];
      const csvData = fs.readFileSync(csvPath, "utf8");
      const records = parse(csvData, { columns: true, skip_empty_lines: true });
      
      // Add a new sheet
      const addSheetResponse = await sheets.spreadsheets.batchUpdate({
        spreadsheetId: sheetId,
        requestBody: {
          requests: [
            {
              addSheet: {
                properties: {
                  title: sheetName
                }
              }
            }
          ]
        }
      });
      const addedSheetId = addSheetResponse.data.replies[0].addSheet.properties.sheetId;
      createdSheets.push({ name: sheetName, index: addedSheetId });
      await uploadCsvToSheet(sheetId, sheetName, records);
    }
    
    // Make the sheet public
    await drive.permissions.create({
      fileId: sheetId,
      requestBody: {
        role: "writer",
        type: "anyone"
      }
    });
    
    return {
      sheetId,
      sheetUrl: `https://docs.google.com/spreadsheets/d/${sheetId}`,
      sheets: createdSheets
    };
  } catch (error) {
    console.error("Error creating multi-sheet from CSVs:", error);
    throw error;
  }
};
if (require.main === module) {
  createSheetFromCsv("exports/jobs-77-main.csv", "Test");
}

module.exports = {
  createSheetFromCsv,
  processZipToSheet,
  createMultiSheetFromCsvs
}; 