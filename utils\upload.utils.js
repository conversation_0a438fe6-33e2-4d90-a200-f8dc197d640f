const fs = require("fs");
const csv = require("csv-parser");
const { google } = require("googleapis");

/**
 * Parses a CSV file and returns JSON rows
 */
const parseCSV = (filePath) => {
  return new Promise((resolve, reject) => {
    const results = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on("data", (data) => results.push(data))
      .on("end", () => resolve(results))
      .on("error", (err) => reject(err));
  });
};

/**
 * Fetches data from a public Google Sheet (or one with credentials)
 * @param {string} sheetId
 * @param {string} range
 * @param {object} [credentials] - Optional: OAuth2 credentials
 */
const fetchGoogleSheetData = async (sheetId) => {
  const rawKey = process.env.GOOGLE_SERVICE_ACCOUNT_KEY;
  if (!rawKey) throw new Error("Missing GOOGLE_SERVICE_ACCOUNT_KEY");

  const credentials = JSON.parse(
    Buffer.from(rawKey, "base64").toString("utf8")
  );

  const auth = new google.auth.GoogleAuth({
    credentials,
    scopes: ["https://www.googleapis.com/auth/spreadsheets.readonly"],
  });

  const sheets = google.sheets({ version: "v4", auth });

  // 1. Get the first sheet name
  const meta = await sheets.spreadsheets.get({
    spreadsheetId: sheetId,
  });

  const firstSheetName = meta.data.sheets?.[0]?.properties?.title;
  if (!firstSheetName) throw new Error("No sheet/tab found in the spreadsheet");

  // 2. Default range
  const range = `${firstSheetName}!A1:Z1000`;

  // 3. Fetch data
  const res = await sheets.spreadsheets.values.get({
    spreadsheetId: sheetId,
    range,
  });

  const [header, ...rows] = res.data.values || [];
  return rows.map((row) => {
    const rowObj = {};
    header.forEach((key, i) => {
      rowObj[key] = row[i] || "";
    });
    return rowObj;
  });
};

module.exports = {
  parseCSV,
  fetchGoogleSheetData,
};
