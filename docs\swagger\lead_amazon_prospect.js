/**
 * @swagger
 * /api/lead/amazon_prospect:
 *   post:
 *     summary: Process a CSV file for Amazon Prospect data
 *     description: |
 *       This allows Amazon Prospect data to be uploaded using the process csv method. 
 *       Amazon Prospect data is similar to Prospect data but integrates better with Amazon Seller and newer data models.
 *
 *       Download sample CSV file:
 *       - [Download sample Amazon Prospect CSV](/examples/input_amazon_prospect_type.csv)
 *     tags: [Leads]
 *     parameters:
 *       - in: query
 *         name: skiperrorrows
 *         schema:
 *           type: boolean
 *           default: false
 *         description: When set to true, rows with errors will be skipped and processing will continue. When false, processing will stop on first error.
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               csvFile:
 *                 type: string
 *                 format: binary
 *                 description: |
 *                   CSV file to process with the following structure:
 *
 *                   - "Amazon Seller ID" or "amazon_seller_id" or "amazonSellerId": Amazon seller identifier
 *                   - "Website" or "company_website" or "website" or "Company Website Full" or "apollo_website" or "Apollo Website": Company website URL
 *                   - "Domain" or "domain": Domain name (extracted from website URL if not provided)
 *                   - "Full Name" or "full_name" or "person_name" or "Person Name" or "Contact Name": Contact person's full name
 *                   - "LinkedIn Link" or "linkedin_url" or "person_linkedin" or "contact LinkedIn" or "Person Linkedin" or "LinkedIn URL": LinkedIn profile URL
 *                   - "Email" or "eMail" or "email" or "Contact Email": Contact email address
 *                   - "Title" or "job_title" or "Job Title" or "Contact Job Title": Job title of the contact
 *                   - "Source" or "source" or "lookup_source" or "Lookup Source" or "Data Source": Source of the data
 *                   - "Email Status" or "email_status" or "eMail Status" or "Contact Status": Status of the email
 *                   - "Seller Group ID" or "seller_group_id" or "sellerGroupId": ID of the seller group (automatically managed during processing)
 *
 *                   Example Amazon Prospect CSV:
 *                   Amazon Seller ID,Website,Full Name,LinkedIn Link,Email,Title,Source,Email Status
 *                   A1B2C3D4E5,https://acme.com,John Doe,https://linkedin.com/in/johndoe,<EMAIL>,CEO,WEBSITE_SEARCH_APOLLO_P1,VERIFIED
 *                   F6G7H8I9J0,https://xyz-corp.com,Jane Smith,https://linkedin.com/in/janesmith,<EMAIL>,CTO,WEBSITE_SEARCH_FULL_SERP_P4,UNVERIFIED
 *             required:
 *               - csvFile
 *     responses:
 *       200:
 *         description: Data uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Data uploaded successfully"
 *       403:
 *         description: Returns error CSV file
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 *             example: |
 *               row,error_message,original_data
 *               2,"Missing email or LinkedIn URL for prospect identification","{\"Website\":\"https://missing-id.com\",\"Full Name\":\"Missing ID\"}"
 *               4,"Invalid email format","{\"Amazon Seller ID\":\"X1Y2Z3\",\"Full Name\":\"Invalid Email\",\"Email\":\"not-an-email\"}"
 *         headers:
 *           Content-Disposition:
 *             schema:
 *               type: string
 *             description: Attachment header with filename
 *             example: attachment; filename="ERROR-input_file.csv"
 *           Content-Type:
 *             schema:
 *               type: string
 *             example: text/csv
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 */

 /**
 * @swagger
 * /api/lead/insert/amazon_prospect:
 *   post:
 *     summary: Insert Amazon Prospect data from CSV file
 *     description: |
 *       This endpoint allows you to process a CSV file to insert new Amazon Prospect records into the database.
 *       
 *       The AmazonProspect model provides a more optimized and future-proof data structure compared to the legacy Prospect model,
 *       with improved integration with AmazonSeller data.
 *
 *       Download sample CSV file:
 *       - [Download sample insert Amazon Prospect CSV](/examples/input_amazon_prospect_operation_type_insert.csv)
 *     tags: [Leads]
 *     parameters:
 *       - in: query
 *         name: skiperrorrows
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Whether to skip rows with errors
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               csvFile:
 *                 type: string
 *                 format: binary
 *                 description: |
 *                   CSV file to process with the Amazon Prospect data structure.
 *             required:
 *               - csvFile
 *     responses:
 *       200:
 *         description: Data uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Data uploaded successfully"
 *       403:
 *         description: CSV file containing error rows
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 *         headers:
 *           Content-Disposition:
 *             schema:
 *               type: string
 *             description: Attachment header with filename
 *             example: attachment; filename="ERROR-input_file.csv"
 *           Content-Type:
 *             schema:
 *               type: string
 *             example: text/csv
 *       400:
 *         description: Invalid type or operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Invalid type"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 */ 