const https = require('https');
const dns = require('dns').promises;
const { URL } = require('url');
const prisma = require('../../database/prisma/getPrismaClient');

class MXRecordChecker {
  constructor(options = {}) {
    this.prisma = prisma;
    this.rateLimit = {
      requestsPerMinute: 1000, // Google DNS API has very generous limits
      requestInterval: 60000, // 1 minute
      currentRequests: 0,
      resetTime: Date.now()
    };
    this.batchSize = 500; // Process 50 records at a time
    this.refreshIntervalDays = 120; // 4 months = 120 days
    
    // DNS Method Configuration
    this.config = {
      method: options.method || 'fallback', // 'google', 'cloudflare', 'nodejs', 'fallback'
      timeout: options.timeout || 10000,
      retries: options.retries || 3,
      ...options
    };
    
    // Method priorities for fallback
    this.methodPriorities = [
      { name: 'google', fn: this.queryMXWithGoogle.bind(this) },
      { name: 'nodejs', fn: this.queryMXWithNodeDNS.bind(this) },
      { name: 'cloudflare', fn: this.queryMXWithCloudflare.bind(this) },
    ];
  }

  /**
   * Extract domain from email address
   */
  extractDomain(email) {
    if (!email || typeof email !== 'string') return null;
    const parts = email.split('@');
    return parts.length === 2 ? parts[1].toLowerCase() : null;
  }

  /**
   * Check rate limit and wait if necessary
   */
  async checkRateLimit() {
    const now = Date.now();
    
    // Reset counter if minute has passed
    if (now - this.rateLimit.resetTime >= this.rateLimit.requestInterval) {
      this.rateLimit.currentRequests = 0;
      this.rateLimit.resetTime = now;
    }
    
    // If we've hit the limit, wait until next minute
    if (this.rateLimit.currentRequests >= this.rateLimit.requestsPerMinute) {
      const waitTime = this.rateLimit.requestInterval - (now - this.rateLimit.resetTime);
      console.log(`Rate limit reached. Waiting ${waitTime}ms...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
      this.rateLimit.currentRequests = 0;
      this.rateLimit.resetTime = Date.now();
    }
    
    this.rateLimit.currentRequests++;
  }

  /**
   * Method 1: Node.js Built-in DNS Module (FASTEST)
   */
  async queryMXWithNodeDNS(domain) {
    try {
      const records = await dns.resolveMx(domain);
      return {
        Status: 0,
        Answer: records.map(record => ({
          type: 15,
          data: `${record.priority} ${record.exchange}`
        })),
        method: 'nodejs',
        timing: Date.now()
      };
    } catch (error) {
      return {
        Status: 1,
        error: error.message,
        method: 'nodejs',
        timing: Date.now()
      };
    }
  }

  /**
   * Method 2: Google DNS-over-HTTPS (CURRENT METHOD)
   */
  async queryMXWithGoogle(domain) {
    await this.checkRateLimit();
    
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      const url = new URL('https://dns.google/resolve');
      url.searchParams.append('name', domain);
      url.searchParams.append('type', 'MX');
      url.searchParams.append('cd', 'false'); // Don't disable DNSSEC validation
      
      const options = {
        hostname: url.hostname,
        port: url.port || 443,
        path: url.pathname + url.search,
        method: 'GET',
        headers: {
          'Accept': 'application/dns-json',
          'User-Agent': 'SellerBot-MXChecker/1.0'
        },
        timeout: this.config.timeout
      };
      
      const req = https.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          try {
            const result = JSON.parse(data);
            resolve({
              ...result,
              method: 'google',
              timing: Date.now() - startTime
            });
          } catch (error) {
            reject(new Error(`Failed to parse DNS response: ${error.message}`));
          }
        });
      });
      
      req.on('timeout', () => {
        req.destroy();
        reject(new Error('DNS query timeout'));
      });
      
      req.on('error', (error) => {
        reject(error);
      });
      
      req.end();
    });
  }

  /**
   * Method 3: Cloudflare DNS-over-HTTPS (PRIVACY-FOCUSED)
   */
  async queryMXWithCloudflare(domain) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      const url = new URL('https://cloudflare-dns.com/dns-query');
      url.searchParams.append('name', domain);
      url.searchParams.append('type', 'MX');
      
      const options = {
        hostname: url.hostname,
        port: url.port || 443,
        path: url.pathname + url.search,
        method: 'GET',
        headers: {
          'Accept': 'application/dns-json',
          'User-Agent': 'SellerBot-MXChecker/1.0'
        },
        timeout: this.config.timeout
      };
      
      const req = https.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          try {
            const result = JSON.parse(data);
            resolve({
              ...result,
              method: 'cloudflare',
              timing: Date.now() - startTime
            });
          } catch (error) {
            reject(new Error(`Failed to parse DNS response: ${error.message}`));
          }
        });
      });
      
      req.on('timeout', () => {
        req.destroy();
        reject(new Error('DNS query timeout'));
      });
      
      req.on('error', (error) => {
        reject(error);
      });
      
      req.end();
    });
  }

  /**
   * Query MX records using the configured method
   */
  async queryMXRecords(domain) {
    const method = this.config.method;
    
    switch (method) {
      case 'google':
        return await this.queryMXWithGoogle(domain);
      case 'cloudflare':
        return await this.queryMXWithCloudflare(domain);
      case 'nodejs':
        return await this.queryMXWithNodeDNS(domain);
      case 'fallback':
        return await this.queryMXWithFallback(domain);
      default:
        throw new Error(`Unknown DNS method: ${method}`);
    }
  }

  /**
   * Method 4: Fallback with Multiple Providers (MAXIMUM RELIABILITY)
   */
  async queryMXWithFallback(domain) {
    let lastError = null;
    
    for (const method of this.methodPriorities) {
      try {
        console.log(`Trying ${method.name} DNS for ${domain}...`);
        const result = await method.fn(domain);
        
        if (result.Status === 0) {
          console.log(`${method.name} DNS succeeded for ${domain}`);
          return result;
        } else {
          console.log(`${method.name} DNS returned status ${result.Status} for ${domain}`);
          lastError = new Error(`DNS status ${result.Status}`);
        }
      } catch (error) {
        console.log(`${method.name} DNS failed for ${domain}: ${error.message}`);
        lastError = error;
        continue;
      }
    }
    
    throw lastError || new Error('All DNS methods failed');
  }

  /**
   * Process MX records and extract meaningful information
   */
  processMXRecords(mxRecords) {
    if (!mxRecords || mxRecords.length === 0) {
      return { provider: 'Unknown', records: [] };
    }
    
    const sortedRecords = mxRecords.sort((a, b) => a.priority - b.priority);
    const provider = this.determineEmailProvider(sortedRecords);
    
    return { provider, records: sortedRecords };
  }

  /**
   * Determine email provider based on MX records - returns original domain in lowercase
   */
  determineEmailProvider(mxRecords) {
    if (!mxRecords || mxRecords.length === 0) return '';
    
    // Sort by priority to get the primary MX record
    const sortedRecords = mxRecords.sort((a, b) => a.priority - b.priority);
    
    // Return the exchange domain from the highest priority (lowest number) MX record in lowercase
    const primaryMX = sortedRecords[0];
    return primaryMX.exchange ? primaryMX.exchange.toLowerCase() : '';
  }

  /**
   * Performance test for all DNS methods
   */
  async performanceTest(domain = 'gmail.com') {
    const results = {};
    const testCount = 5;
    
    console.log(`\n=== MX DNS Performance Test (${testCount} runs) ===`);
    console.log(`Testing domain: ${domain}\n`);
    
    for (const method of this.methodPriorities) {
      console.log(`Testing ${method.name} DNS...`);
      const times = [];
      let successCount = 0;
      
      for (let i = 0; i < testCount; i++) {
        try {
          const start = Date.now();
          const result = await method.fn(domain);
          const timing = Date.now() - start;
          times.push(timing);
          
          if (result.Status === 0) successCount++;
          
          // Small delay between requests
          await new Promise(resolve => setTimeout(resolve, 100));
        } catch (error) {
          console.log(`  Run ${i + 1} failed: ${error.message}`);
        }
      }
      
      if (times.length > 0) {
        results[method.name] = {
          avgTime: Math.round(times.reduce((a, b) => a + b, 0) / times.length),
          minTime: Math.min(...times),
          maxTime: Math.max(...times),
          successRate: `${successCount}/${testCount}`,
          successPercent: Math.round((successCount / testCount) * 100)
        };
      } else {
        results[method.name] = {
          avgTime: 'N/A',
          minTime: 'N/A',
          maxTime: 'N/A',
          successRate: `0/${testCount}`,
          successPercent: 0
        };
      }
    }
    
    // Print results
    console.log('\n=== Results ===');
    Object.entries(results).forEach(([method, stats]) => {
      console.log(`${method.toUpperCase()}:`);
      console.log(`  Average: ${stats.avgTime}ms`);
      console.log(`  Range: ${stats.minTime}ms - ${stats.maxTime}ms`);
      console.log(`  Success: ${stats.successRate} (${stats.successPercent}%)`);
      console.log('');
    });
    
    return results;
  }

  /**
   * Get current configuration
   */
  getConfig() {
    return {
      method: this.config.method,
      timeout: this.config.timeout,
      retries: this.config.retries,
      batchSize: this.batchSize,
      refreshIntervalDays: this.refreshIntervalDays
    };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Update MX records for a specific prospect record
   */
  async updateMXRecord(recordId, email, tableType) {
    try {
      if (!email) {
        console.log(`No email to process for ${tableType} record ${recordId}`);
        return;
      }
      
      const domain = this.extractDomain(email);
      if (!domain) {
        console.log(`Invalid email format for ${tableType} record ${recordId}: ${email}`);
        return;
      }
      
      console.log(`Processing MX records for domain: ${domain} (from ${email}) - ${tableType}`);
      
      const dnsResult = await this.queryMXRecords(domain);
      
      if (dnsResult.Status !== 0) {
        console.log(`DNS query failed for ${domain}: Status ${dnsResult.Status}`);
        return;
      }
      
      const mxAnswers = dnsResult.Answer ? dnsResult.Answer.filter(a => a.type === 15) : [];
      const mxRecords = mxAnswers.map(record => ({
        priority: record.data ? parseInt(record.data.split(' ')[0]) : 0,
        exchange: record.data ? record.data.split(' ')[1] : ''
      }));
      
      const { provider, records } = this.processMXRecords(mxRecords);
      
      const updateData = {
        emailProvider: provider,
        mxRecords: records, // Store as JSON object, not string
        lastProcessAt: new Date(),
        nextProcessAt: new Date(Date.now() + (this.refreshIntervalDays * 24 * 60 * 60 * 1000))
      };
      
      // Update the appropriate table
      if (tableType === 'prospect') {
        await this.prisma.prospect.update({
          where: { prospect_id: recordId },
          data: updateData
        });
      } else if (tableType === 'amazonProspect') {
        await this.prisma.amazonProspect.update({
          where: { prospect_id: recordId },
          data: updateData
        });
      }
      
      console.log(`Updated MX records for ${domain}: Provider: ${provider}, Records: ${records.length} - ${tableType}`);
      
    } catch (error) {
      console.error(`Error updating MX record for ${tableType} ${email}:`, error.message);
      
      // Update with error information
      const errorUpdateData = {
        lastProcessAt: new Date(),
        nextProcessAt: new Date(Date.now() + (24 * 60 * 60 * 1000)) // Retry in 24 hours
      };
      
      try {
        if (tableType === 'prospect') {
          await this.prisma.prospect.update({
            where: { prospect_id: recordId },
            data: errorUpdateData
          });
        } else if (tableType === 'amazonProspect') {
          await this.prisma.amazonProspect.update({
            where: { prospect_id: recordId },
            data: errorUpdateData
          });
        }
      } catch (updateError) {
        console.error(`Failed to update error status for ${tableType} ${recordId}:`, updateError.message);
      }
    }
  }

  /**
   * Get prospect records that need MX record updates
   */
  async getRecordsToProcess() {
    const now = new Date();
    const conditions = {
      email: { not: "" },
      OR: [
        { emailProvider: null },
        { nextProcessAt: null },
        { nextProcessAt: { lt: now } }
      ]
    };
    
    const selectFields = { 
      prospect_id: true, 
      email: true,
      person_name: true,
      emailProvider: true, 
      nextProcessAt: true 
    };
    
    // Get records from both tables
    const [prospects, amazonProspects] = await Promise.all([
      this.prisma.prospect.findMany({
        where: {
          email: {
            not: ""
          },
          OR: [
            {
              emailProvider: {
                equals: ""
              }
            },
            {
              nextProcessAt: {
                lt: new Date()
              }
            }
          ]
        },
        select: {
          prospect_id: true,
          email: true,
          person_name: true,
          emailProvider: true,
          nextProcessAt: true
        },
        take: Math.floor(this.batchSize / 2)
      }),
      this.prisma.amazonProspect.findMany({
        where: {
          email: {
            not: ""
          },
          OR: [
            {
              emailProvider: {
                equals: ""
              }
            },
            {
              nextProcessAt: {
                lt: new Date()
              }
            }
          ]
        },
        select: {
          prospect_id: true,
          email: true,
          person_name: true,
          emailProvider: true,
          nextProcessAt: true
        },
        take: Math.floor(this.batchSize / 2)
      })
    ]);
    
    // Combine records with table type indicator
    const allRecords = [
      ...prospects.map(record => ({
        id: record.prospect_id,
        email: record.email,
        person_name: record.person_name,
        domain: this.extractDomain(record.email),
        tableType: 'prospect'
      })),
      ...amazonProspects.map(record => ({
        id: record.prospect_id,
        email: record.email,
        person_name: record.person_name,
        domain: this.extractDomain(record.email),
        tableType: 'amazonProspect'
      }))
    ];
    
    return allRecords;
  }

  /**
   * Process all prospect records that need MX record updates
   */
  async processAllRecords() {
    console.log('Starting MX record batch processing for prospects...');
    
    try {
      const records = await this.getRecordsToProcess();
      
      console.log(`Found ${records.length} prospect records to process`);
      
      if (records.length === 0) {
        console.log('No prospect records need MX record updates');
        return;
      }
      
      // Process records in batches
      for (let i = 0; i < records.length; i += this.batchSize) {
        const batch = records.slice(i, i + this.batchSize);
        console.log(`Processing batch ${Math.floor(i / this.batchSize) + 1}/${Math.ceil(records.length / this.batchSize)}`);
        
        // Process batch in parallel but with rate limiting
        const batchPromises = batch.map(record => 
          this.updateMXRecord(record.id, record.email, record.tableType)
        );
        
        await Promise.allSettled(batchPromises);
        
        // Small delay between batches to be respectful
        if (i + this.batchSize < records.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
      
      console.log('MX record batch processing completed');
      
    } catch (error) {
      console.error('Error in MX record batch processing:', error);
      throw error;
    }
  }

  /**
   * Get statistics about MX record processing
   */
  async getProcessingStats() {
    // Get stats from both tables
    const [prospectStats, amazonProspectStats, prospectTotals, amazonProspectTotals] = await Promise.all([
      this.prisma.prospect.groupBy({
        by: ['emailProvider'],
        _count: true,
        where: { email: { not: '' } }
      }),
      this.prisma.amazonProspect.groupBy({
        by: ['emailProvider'],
        _count: true,
        where: { email: { not: '' } }
      }),
      Promise.all([
        this.prisma.prospect.count({
          where: { email: { not: '' } }
        }),
        this.prisma.prospect.count({
          where: { 
            email: { not: '' },
            emailProvider: {
              not: ''
            }
          }
        })
      ]),
      Promise.all([
        this.prisma.amazonProspect.count({
          where: { email: { not: '' } }
        }),
        this.prisma.amazonProspect.count({
          where: { 
            email: { not: '' },
            emailProvider: {
              not: ''
            }
          }
        })
      ])
    ]);
    
    // Calculate totals
    const totalProspects = prospectTotals[0] || 0;
    const totalAmazonProspects = amazonProspectTotals[0] || 0;
    const processedProspects = prospectTotals[1] || 0;
    const processedAmazonProspects = amazonProspectTotals[1] || 0;
    
    const totalRecords = totalProspects + totalAmazonProspects;
    const totalProcessed = processedProspects + processedAmazonProspects;
    const totalPending = totalRecords - totalProcessed;
    
    // Combine provider stats
    const allProviderStats = {};
    
    // Add prospect stats
    prospectStats.forEach(stat => {
      const provider = stat.emailProvider || 'Pending';
      allProviderStats[provider] = (allProviderStats[provider] || 0) + stat._count;
    });
    
    // Add amazon prospect stats
    amazonProspectStats.forEach(stat => {
      const provider = stat.emailProvider || 'Pending';
      allProviderStats[provider] = (allProviderStats[provider] || 0) + stat._count;
    });
    
    return {
      emailProviders: Object.entries(allProviderStats).map(([provider, count]) => ({
        emailProvider: provider,
        _count: count
      })),
      totalRecords,
      totalProcessed,
      totalPending,
      processedPercentage: totalRecords > 0 ? ((totalProcessed / totalRecords) * 100).toFixed(2) : 0,
      breakdown: {
        prospects: {
          total: totalProspects,
          processed: processedProspects,
          pending: totalProspects - processedProspects
        },
        amazonProspects: {
          total: totalAmazonProspects,
          processed: processedAmazonProspects,
          pending: totalAmazonProspects - processedAmazonProspects
        }
      }
    };
  }
}

module.exports = MXRecordChecker; 