const cheerio = require("cheerio");
const axios = require("axios");
require("dotenv");
const getSellerImage = async (sellerProfileUrl) => {
  try {
    const url = `https://api.scraperapi.com?api_key=${
      process.env.SCRAPER_API_KEY
    }&url=${encodeURIComponent(sellerProfileUrl)}`;
    const response = await axios.get(url, {
      headers: {
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
      },
    });
    const text = response.data;
    const $ = cheerio.load(text);

    const sellerLogoUrl = $("#seller-logo-img").attr("src");

    if (sellerLogoUrl) {
      const imageResponse = await axios.get(sellerLogoUrl, {
        responseType: "arraybuffer",
      });
      const base64Image = Buffer.from(imageResponse.data, "binary").toString(
        "base64",
      );
      return base64Image;
    }
    return null;
  } catch (error) {
    // console.log(error);
    console.error("Error fetching seller image:", error);
    return null;
  }
};

module.exports = { getSellerImage };
