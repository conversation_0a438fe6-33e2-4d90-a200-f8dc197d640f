# Metadata Tagging Guide for LiteLLM/Lunary

This guide explains how to use the comprehensive metadata tagging system to track AI requests in LiteLLM and Lunary for better observability and debugging.

## Overview

The metadata tagging system automatically adds rich context to every AI request, enabling you to:

- **Track function calls**: See exactly which function made each AI request
- **Monitor usage patterns**: Understand how different features use AI
- **Debug issues**: Trace problems back to specific code paths
- **Analyze costs**: Break down AI costs by feature, user, or use case
- **Performance monitoring**: Track response times and token usage

## Quick Start

### 1. Environment Variables

Add these to your `.env` file:

```bash
# Enable LiteLLM proxy (optional)
USE_LITELLM_PROXY=true
LITELLM_PROXY_URL=http://**************:4000
LITELLM_API_KEY=sk-your-litellm-key

# Enable metadata logging (for debugging)
AI_METADATA_LOGGING=true

# Lunary configuration (if using Lunary)
LUNARY_API_KEY=your-lunary-api-key
LUNARY_PROJECT_ID=your-project-id
```

### 2. Basic Usage

```javascript
const { litellmService } = require('./services/ai/litellmService');

// Simple request with metadata
const result = await litellmService.createChatCompletion([
    { role: 'user', content: 'Hello!' }
], {
    useCase: 'customer_support',
    feature: 'greeting',
    userId: 'user_123',
    userType: 'customer'
});
```

### 3. Using Enhanced ScrapeGPT

```javascript
const { getChatGPTResponse } = require('./services/scrapeGPT/request');

const result = await getChatGPTResponse(
    'You are a content analyzer.',
    'Analyze this website content...',
    true, // usePortkey
    {
        useCase: 'scrape_analysis',
        feature: 'content_extraction',
        scrapeType: 'business_info',
        domain: 'example.com',
        userId: 'scraper_bot'
    }
);
```

## Metadata Fields

### Automatic Fields

These are automatically generated for every request:

- `request_id`: Unique identifier for this request
- `session_id`: Unique identifier for this application session
- `timestamp`: ISO timestamp of the request
- `service`: Always "SellerBot"
- `calling_function`: Name of the function that made the request
- `calling_file`: File name where the request originated
- `hostname`: Server hostname
- `environment`: development/production/staging

### Configurable Fields

You can set these in your request options:

- `useCase`: High-level category (e.g., 'scrape_analysis', 'lead_generation')
- `feature`: Specific feature (e.g., 'content_extraction', 'email_classification')
- `operationType`: Type of operation (e.g., 'chat_completion', 'streaming')
- `userId`: User making the request
- `userType`: Type of user (e.g., 'customer', 'admin', 'system')
- `customTags`: Object with any additional tags

### Business Context Tags

For specific use cases, additional tags are automatically added:

**Scrape Analysis:**
- `domain`: Website domain being analyzed
- `scrape_type`: Type of scraping operation

**Lead Generation:**
- `lead_source`: Source of the lead data
- `batch_id`: Batch processing identifier

**Email Analysis:**
- `email_type`: Type of email being analyzed
- `campaign_id`: Email campaign identifier

## Service Integration

### LiteLLM Service

The `LiteLLMService` class provides the most comprehensive metadata tagging:

```javascript
const { litellmService } = require('./services/ai/litellmService');

// Chat completion with full metadata
const result = await litellmService.createChatCompletion(messages, {
    useCase: 'lead_generation',
    feature: 'prospect_analysis',
    userId: 'lead_gen_system',
    userType: 'system',
    customTags: {
        batch_id: 'batch_001',
        source: 'smartscout',
        priority: 'high'
    }
});

// Streaming with metadata
const stream = await litellmService.createStreamingCompletion(messages, {
    useCase: 'customer_support',
    feature: 'chat_response',
    userId: 'support_agent_123'
});

// Factory pattern with metadata
const result = await litellmService.completionFactory('match_text', data, {
    useCase: 'content_matching',
    feature: 'business_matching'
});
```

### Enhanced ScrapeGPT

All ScrapeGPT functions now support metadata:

```javascript
// Request with metadata
const result = await getChatGPTResponse(
    systemPrompt,
    userPrompt,
    true,
    {
        useCase: 'scrape_analysis',
        feature: 'content_analysis',
        scrapeType: 'business_info',
        domain: 'target-website.com',
        customTags: {
            extraction_type: 'contact_info',
            confidence_threshold: 0.8
        }
    }
);

// Factory with metadata
const result = await completionFactory(
    'match_text',
    data,
    null,
    true,
    {
        useCase: 'lead_generation',
        feature: 'business_matching',
        customTags: {
            batch_id: 'batch_001',
            source: 'manual_upload'
        }
    }
);

// Assistant with metadata
const result = await getAssistantResponse(
    assistantId,
    jsonString,
    true,
    {
        useCase: 'structured_analysis',
        feature: 'data_extraction',
        customTags: {
            assistant_type: 'business_analyzer',
            data_format: 'json'
        }
    }
);
```

## Headers and HTTP Requests

For direct HTTP requests, metadata is included in headers:

```javascript
const { aiMetadataHelper } = require('./utils/aiMetadataHelper');

const headers = aiMetadataHelper.generateHeaders({
    useCase: 'api_request',
    feature: 'direct_call',
    userId: 'api_user'
});

// Headers include:
// X-LiteLLM-Metadata: {...full metadata object...}
// X-Request-ID: req_123...
// X-Session-ID: sellerbot_456...
// X-Service: SellerBot
// X-Function: myFunction
// X-Use-Case: api_request
// etc.
```

## Monitoring and Debugging

### Enable Logging

Set `AI_METADATA_LOGGING=true` to see metadata in your logs:

```bash
[AI_METADATA_INFO] {
  "request_id": "req_1703123456789_abc123",
  "session_id": "sellerbot_1703123456789_def456",
  "calling_function": "getChatGPTResponse",
  "use_case": "scrape_analysis",
  "feature": "content_analysis",
  "tags": {
    "domain": "example.com",
    "scrape_type": "business_info"
  }
}
```

### Lunary Dashboard

In Lunary, you'll see:

- **Request traces** with full metadata context
- **Performance metrics** grouped by use case and feature
- **Cost breakdown** by user, feature, and operation type
- **Error tracking** with function-level detail

### LiteLLM Proxy Logs

The LiteLLM proxy will log metadata for each request, enabling:

- Request routing decisions based on metadata
- Cost allocation by business unit
- Rate limiting by user type
- Performance optimization by use case

## Best Practices

### 1. Consistent Use Cases

Use consistent `useCase` values across your application:

```javascript
// Good
useCase: 'scrape_analysis'
useCase: 'lead_generation'
useCase: 'email_analysis'

// Avoid
useCase: 'scraping'
useCase: 'scrape_stuff'
useCase: 'analyze_content'
```

### 2. Descriptive Features

Use descriptive `feature` names:

```javascript
// Good
feature: 'business_info_extraction'
feature: 'contact_email_classification'
feature: 'product_description_generation'

// Avoid
feature: 'extraction'
feature: 'classification'
feature: 'generation'
```

### 3. Meaningful Custom Tags

Add custom tags that help with debugging and analysis:

```javascript
customTags: {
    batch_id: 'batch_001',
    source: 'smartscout_import',
    priority: 'high',
    retry_count: 0,
    expected_format: 'json'
}
```

### 4. User Context

Always include user context when available:

```javascript
{
    userId: 'user_123',
    userType: 'admin', // or 'customer', 'system', 'api'
}
```

## Testing

Run the metadata tagging tests:

```bash
# Test all metadata functionality
node test_metadata_tagging.js

# Test LiteLLM proxy with metadata
node test_litellm_proxy.js
```

## Troubleshooting

### Common Issues

1. **Metadata not appearing in Lunary**
   - Check `LUNARY_API_KEY` is set
   - Verify network connectivity to Lunary
   - Enable `AI_METADATA_LOGGING=true` to debug

2. **Headers too large**
   - Reduce custom tags size
   - Use shorter tag values
   - Consider moving large data to request body

3. **Performance impact**
   - Metadata generation is lightweight
   - Disable logging in production: `AI_METADATA_LOGGING=false`
   - Use async logging if needed

### Debug Mode

Enable comprehensive debugging:

```bash
AI_METADATA_LOGGING=true
DEBUG=litellm:*
NODE_ENV=development
```

This will show detailed metadata generation and request tracking information.
