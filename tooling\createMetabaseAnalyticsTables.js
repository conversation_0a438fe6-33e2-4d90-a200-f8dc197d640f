require("dotenv").config({ path: "../.env" });
const prisma = require("../database/prisma/getPrismaClient");
const axios = require("axios");
const fs = require("fs");
const path = require("path");

// Command-line argument processing
const args = process.argv.slice(2);
const VALID_MODES = ['funnelClient', 'tag', 'providerAnalytics'];
const mode = args[0];

// Define field mapping from JSON to more readable column names
const funnelClientFieldMapping = {
  // Main email counts
  "Sum of email_1_sent_count": "email_1_sent",
  "Sum of email_2_sent_count": "email_2_sent",
  "Sum of email_3_sent_count": "email_3_sent",
  
  // Email 1 type breakdowns
  "Sum of email_1_sent_work_count": "email_1_sent_work",
  "Sum of email_1_sent_personal_count": "email_1_sent_personal",
  "Sum of email_1_sent_role_count": "email_1_sent_role",
  "Sum of email_1_sent_unknown_count": "email_1_sent_unknown",
  
  // Email 2 type breakdowns
  "Sum of email_2_sent_work_count": "email_2_sent_work",
  "Sum of email_2_sent_personal_count": "email_2_sent_personal",
  "Sum of email_2_sent_role_count": "email_2_sent_role",
  "Sum of email_2_sent_unknown_count": "email_2_sent_unknown",
  
  // Email 3 type breakdowns
  "Sum of email_3_sent_work_count": "email_3_sent_work",
  "Sum of email_3_sent_personal_count": "email_3_sent_personal",
  "Sum of email_3_sent_role_count": "email_3_sent_role",
  "Sum of email_3_sent_unknown_count": "email_3_sent_unknown",
  
  // Reply counts
  "Sum of replies_after_email_1_count": "replies_after_email_1",
  "Sum of replies_after_email_2_count": "replies_after_email_2",
  "Sum of replies_after_email_3_count": "replies_after_email_3",
  
  // Automated replies
  "Sum of automated_replies_after_email_1_count": "auto_replies_after_email_1",
  "Sum of automated_replies_after_email_2_count": "auto_replies_after_email_2",
  "Sum of automated_replies_after_email_3_count": "auto_replies_after_email_3",
  
  // Error replies
  "Sum of error_replies_after_email_1_count": "error_replies_after_email_1",
  "Sum of error_replies_after_email_2_count": "error_replies_after_email_2",
  "Sum of error_replies_after_email_3_count": "error_replies_after_email_3",
  
  // Meeting slots
  "Sum of meeting_slots_sent_after_1": "meeting_slots_after_email_1",
  "Sum of meeting_slots_sent_after_2": "meeting_slots_after_email_2",
  "Sum of meeting_slots_sent_after_3": "meeting_slots_after_email_3",
  "Sum of meeting_slots_sent_unknown": "meeting_slots_unknown",
  "Sum of total_meeting_slots_sent": "total_meeting_slots_sent",
  
  // Meetings booked
  "Sum of meetings_booked": "meetings_booked",
  
  // Date and identification
  "week_start_date: Day": "week_start_date",
  "campaign_code": "campaign_code",
  "jeff_client_name": "client_name",
  
  // Add mappings for calculated fields
  "total_replies_email_1": "total_replies_email_1",
  "total_replies_email_2": "total_replies_email_2",
  "total_replies_email_3": "total_replies_email_3"
};

// Define field mapping for provider analytics data
const providerAnalyticsFieldMapping = {
  "provider_name": "provider_name",
  "Sum of email_1_sent_count": "email_1_sent",
  "Sum of automated_replies_after_email_2_count": "auto_replies_after_email_2",
  "Sum of email_2_sent_count": "email_2_sent",
  "Sum of error_replies_after_email_1_count": "error_replies_after_email_1",
  "week_start_date: Week": "week_start_date",
  "Sum of error_replies_after_email_2_count": "error_replies_after_email_2",
  "Sum of email_3_sent_count": "email_3_sent",
  "Sum of automated_replies_after_email_1_count": "auto_replies_after_email_1",
  "Sum of replies_after_email_1_count": "replies_after_email_1",
  "Sum of replies_after_email_3_count": "replies_after_email_3",
  "Sum of automated_replies_after_email_3_count": "auto_replies_after_email_3",
  "Sum of error_replies_after_email_3_count": "error_replies_after_email_3",
  "Sum of replies_after_email_2_count": "replies_after_email_2",
  "jeff_client_name": "client_name",
  "campaign_code": "campaign_code",
  // Add mappings for calculated fields
  "total_replies_email_1": "total_replies_email_1",
  "total_replies_email_2": "total_replies_email_2",
  "total_replies_email_3": "total_replies_email_3"
};

// Define field mapping for tag data
const tagFieldMapping = {
  "provider_name": "provider_name",
  "Sum of email_1_sent_count": "email_1_sent",
  "Sum of automated_replies_after_email_2_count": "auto_replies_after_email_2",
  "Sum of meeting_slots_sent_after_2": "meeting_slots_after_email_2",
  "Sum of email_2_sent_count": "email_2_sent",
  "Sum of error_replies_after_email_1_count": "error_replies_after_email_1",
  "week_start_date: Day": "week_start_date",
  "Sum of error_replies_after_email_2_count": "error_replies_after_email_2",
  "Sum of email_3_sent_count": "email_3_sent",
  "Sum of automated_replies_after_email_1_count": "auto_replies_after_email_1",
  "Sum of meeting_slots_sent_unknown": "meeting_slots_unknown",
  "Sum of meetings_booked": "meetings_booked",
  "Sum of replies_after_email_1_count": "replies_after_email_1",
  "Sum of replies_after_email_3_count": "replies_after_email_3",
  "Sum of automated_replies_after_email_3_count": "auto_replies_after_email_3",
  "Sum of error_replies_after_email_3_count": "error_replies_after_email_3",
  "tag_name": "tag_name",
  "Sum of meeting_slots_sent_after_1": "meeting_slots_after_email_1",
  "campaign_code": "campaign_code",
  "Sum of total_meeting_slots_sent": "total_meeting_slots_sent",
  "Sum of replies_after_email_2_count": "replies_after_email_2",
  "jeff_client_name": "client_name",
  // Add mappings for calculated fields
  "total_replies_email_1": "total_replies_email_1",
  "total_replies_email_2": "total_replies_email_2",
  "total_replies_email_3": "total_replies_email_3"
};

// Column type definitions
const COLUMN_TYPE = {
  DATE: 'DATE',
  TEXT: 'TEXT',
  INTEGER: 'INTEGER'
};

// Define column types for each field
const funnelClientColumnTypes = {
  "week_start_date": COLUMN_TYPE.DATE,
  "campaign_code": COLUMN_TYPE.TEXT,
  "client_name": COLUMN_TYPE.TEXT,
  // All other columns are INTEGER by default
};

// Define column types for provider analytics data
const providerAnalyticsColumnTypes = {
  "week_start_date": COLUMN_TYPE.DATE,
  "client_name": COLUMN_TYPE.TEXT,
  "provider_name": COLUMN_TYPE.TEXT,
  "campaign_code": COLUMN_TYPE.TEXT,
  // All other columns are INTEGER by default
};

// Define column types for tag data
const tagColumnTypes = {
  "week_start_date": COLUMN_TYPE.DATE,
  "campaign_code": COLUMN_TYPE.TEXT,
  "client_name": COLUMN_TYPE.TEXT,
  "provider_name": COLUMN_TYPE.TEXT,
  "tag_name": COLUMN_TYPE.TEXT,
  // All other columns are INTEGER by default
};

// Configure the desired column order here
// This array controls the order of columns in the database
// Format: Array of database column names in desired order
const funnelClientColumnOrder = [
  // Date columns first
  "week_start_date",
  
  // String columns second
  "campaign_code", 
  "client_name",
  
  // Main email counts
  "email_1_sent",
  "email_1_sent_work",
  "email_1_sent_personal",
  "email_1_sent_role",
  "email_1_sent_unknown",
  "replies_after_email_1",
  "auto_replies_after_email_1",
  "error_replies_after_email_1",
  "total_replies_email_1",  // Calculated column
  "meeting_slots_after_email_1",

  "email_2_sent",
  "email_2_sent_work",
  "email_2_sent_personal",
  "email_2_sent_role",
  "email_2_sent_unknown",
  "replies_after_email_2",
  "auto_replies_after_email_2",
  "error_replies_after_email_2",
  "total_replies_email_2",  // Calculated column
  "meeting_slots_after_email_2",

  "email_3_sent",
  "email_3_sent_work",
  "email_3_sent_personal",
  "email_3_sent_role",
  "email_3_sent_unknown",
  "replies_after_email_3",
  "auto_replies_after_email_3",
  "error_replies_after_email_3",
  "total_replies_email_3",  // Calculated column
  "meeting_slots_after_email_3",

  "meeting_slots_unknown",
  "total_meeting_slots_sent",
  "meetings_booked"
];

// Configure the desired column order for provider analytics data
const providerAnalyticsColumnOrder = [
  // Date columns first
  "week_start_date",
  
  // String columns second
  "campaign_code",
  "client_name",
  "provider_name",
  
  // Integer columns last (metrics)
  "email_1_sent",
  "replies_after_email_1",
  "auto_replies_after_email_1",
  "error_replies_after_email_1",
  "total_replies_email_1",  // Added calculated column

  "email_2_sent",
  "replies_after_email_2",
  "auto_replies_after_email_2",
  "error_replies_after_email_2",
  "total_replies_email_2",  // Added calculated column

  "email_3_sent",
  "replies_after_email_3",
  "auto_replies_after_email_3",
  "error_replies_after_email_3",
  "total_replies_email_3"  // Added calculated column
];

// Configure the desired column order for tag data
const tagColumnOrder = [
  // Date columns first
  "week_start_date",
  
  // String columns second
  "campaign_code", 
  "client_name",
  "provider_name",
  "tag_name",
  
  // Integer columns last (metrics)
  "email_1_sent",
  "replies_after_email_1",
  "auto_replies_after_email_1",
  "error_replies_after_email_1",
  "total_replies_email_1",  // Added calculated column
  "meeting_slots_after_email_1",

  "email_2_sent",
  "replies_after_email_2",
  "auto_replies_after_email_2",
  "error_replies_after_email_2",
  "total_replies_email_2",  // Added calculated column
  "meeting_slots_after_email_2",

  "email_3_sent",
  "replies_after_email_3",
  "auto_replies_after_email_3",
  "error_replies_after_email_3",
  "total_replies_email_3",  // Added calculated column
  "meeting_slots_after_email_3",

  "meeting_slots_unknown",
  "total_meeting_slots_sent",
  "meetings_booked"
];

// Check if mode is provided
if (!mode) {
  console.error(`❌ Error: Mode must be specified`);
  console.error(`Usage: node createMetabaseAnalyticsTables.js [mode]`);
  console.error(`Available modes: ${VALID_MODES.join(', ')}`);
  process.exit(1);
}

// Check if mode is valid
if (!VALID_MODES.includes(mode)) {
  console.error(`❌ Error: Invalid mode '${mode}'`);
  console.error(`Available modes: ${VALID_MODES.join(', ')}`);
  process.exit(1);
}

/**
 * Fetches data from Metabase Provider Analytics API endpoint
 * @returns {Promise<Array>} Array of provider analytics data objects
 */
async function fetchProviderAnalyticsData() {
  try {
    console.log("📥 Fetching provider analytics data from Metabase API...");
    const response = await axios.post(
      "https://metabase.equalcollective.com/api/card/314/query/json",
      {}, // Empty body for POST request
      {
        headers: {
          "X-API-KEY": "mb_YwbdsJ+gngJYJC2cLN2J21gdlwtClwctlNhNKaXVl00="
        }
      }
    );

    if (response.data && Array.isArray(response.data)) {
      console.log(`✅ Successfully fetched ${response.data.length} provider analytics records`);
      return response.data;
    } else {
      console.error("❌ Error: Metabase Provider Analytics API did not return an array");
      return [];
    }
  } catch (error) {
    console.error("❌ Error fetching provider analytics data:", error.message);
    return [];
  }
}

/**
 * Fetches data from Metabase FunnelClient API endpoint
 * @returns {Promise<Array>} Array of funnel client data objects
 */
async function fetchFunnelClientData() {
  try {
    console.log("📥 Fetching funnel client data from Metabase API...");
    const response = await axios.post(
      "https://metabase.equalcollective.com/api/card/291/query/json",
      {}, // Empty body for POST request
      {
        headers: {
          "X-API-KEY": "mb_YwbdsJ+gngJYJC2cLN2J21gdlwtClwctlNhNKaXVl00="
        }
      }
    );
    console.log(`✅ Successfully fetched data for ${response.data.length} funnel client records`);
    return response.data;
  } catch (error) {
    console.error("❌ Error fetching funnel client data:", error.message);
    throw error;
  }
}

/**
 * Fetches data from Metabase Tag API endpoint
 * @returns {Promise<Array>} Array of tag data objects
 */
async function fetchTagData() {
  try {
    console.log("📥 Fetching tag data from Metabase API...");
    const response = await axios.post(
      "https://metabase.equalcollective.com/api/card/290/query/json", 
      {},
      {
        headers: {
          "X-API-KEY": "mb_YwbdsJ+gngJYJC2cLN2J21gdlwtClwctlNhNKaXVl00="
        }
      }
    );
    console.log(`✅ Successfully fetched data for ${response.data.length} tag records`);
    return response.data;
  } catch (error) {
    console.error("❌ Error fetching tag data:", error.message);
    throw error;
  }
}

/**
 * Creates SQL table definition based on the first object in the data array
 * @param {Object} sampleObj - Sample object to derive column definitions
 * @param {String} tableName - Name of the table to create
 * @returns {String} - SQL CREATE TABLE statement
 */
function generateTableDefinition(sampleObj, tableName) {
  const columns = Object.keys(sampleObj).map(key => {
    // Handle special characters in column names by using double quotes
    const columnName = `"${key.replace(/"/g, '""')}"`;
    
    // Determine SQL type based on JavaScript type
    let sqlType;
    const value = sampleObj[key];
    if (typeof value === 'number') {
      // Check if it's an integer or float
      sqlType = Number.isInteger(value) ? 'INTEGER' : 'NUMERIC';
    } else if (typeof value === 'boolean') {
      sqlType = 'BOOLEAN';
    } else if (typeof value === 'string') {
      // Check if it might be a date
      if (key.toLowerCase().includes('date') && !isNaN(Date.parse(value))) {
        sqlType = 'DATE';
      } else {
        sqlType = 'TEXT';
      }
    } else if (value === null) {
      // Default to TEXT for null values
      sqlType = 'TEXT';
    } else {
      // Default to TEXT for anything else
      sqlType = 'TEXT';
    }
    
    return `${columnName} ${sqlType}`;
  }).join(',\n    ');

  return `
    CREATE TABLE IF NOT EXISTS "public2"."${tableName}" (
      "id" SERIAL PRIMARY KEY,
      ${columns}
    )
  `;
}

/**
 * Ensures that the required database table exists for FunnelClient data
 * Drops the existing table and recreates it with the latest schema
 */
async function ensureFunnelClientTableExists() {
  try {
    console.log("🔄 Recreating FunnelClient table...");
    
    // First drop the table if it exists
    try {
      console.log("🗑️ Dropping existing FunnelClient table if it exists...");
      await prisma.$executeRawUnsafe(`DROP TABLE IF EXISTS "public2"."FunnelClientAnalytics";`);
      console.log("✅ Existing FunnelClient table dropped successfully");
    } catch (dropError) {
      console.error("⚠️ Warning while dropping FunnelClient table:", dropError.message);
      // Continue execution even if drop fails
    }
    
    // Map column names to their SQL types based on configuration or defaults
    const columnDefs = new Map();
    
    // Get all unique DB column names from the field mapping
    const allDbColumns = [...new Set(Object.values(funnelClientFieldMapping))];
    
    // Create column definitions with proper SQL types
    allDbColumns.forEach(columnName => {
      // Get the type from column types config or default to INTEGER
      const sqlType = funnelClientColumnTypes[columnName] || COLUMN_TYPE.INTEGER;
      columnDefs.set(columnName, sqlType);
    });
    
    // Create SQL columns in the configured order
    const columns = funnelClientColumnOrder
      .map(columnName => {
        const sqlType = columnDefs.get(columnName);
        if (!sqlType) {
          console.warn(`⚠️ Warning: Column '${columnName}' in order config not found in field mapping. Skipping.`);
          return null;
        }
        return `"${columnName}" ${sqlType}`;
      })
      .filter(Boolean) // Remove null values
      .join(',\n        ');
    
    // Create a new table with the latest schema
    const createTableSQL = `
      CREATE TABLE "public2"."FunnelClientAnalytics" (
        "id" SERIAL PRIMARY KEY,
        ${columns}
      )
    `;
    
    await prisma.$executeRawUnsafe(createTableSQL);
    
    console.log("✅ FunnelClient table created successfully");
  } catch (error) {
    console.error("❌ Error recreating FunnelClient table:", error.message);
    throw error;
  }
}

/**
 * Ensures that the required database table exists for Tag data
 * Drops the existing table and recreates it with the latest schema
 * @param {Array} data - Array of processed tag data objects
 */
async function ensureTagTableExists(data) {
  try {
    console.log("🔄 Recreating Tag analytics table...");
    
    // First drop the table if it exists
    try {
      console.log("🗑️ Dropping existing Tag analytics table if it exists...");
      await prisma.$executeRawUnsafe(`DROP TABLE IF EXISTS "public2"."TagAnalytics";`);
      console.log("✅ Existing Tag analytics table dropped successfully");
    } catch (dropError) {
      console.error("⚠️ Warning while dropping Tag analytics table:", dropError.message);
      // Continue execution even if drop fails
    }
    
    console.log("⚙️ Creating Tag analytics table...");
    
    // Create a new object with mapped field names for table creation
    const mappedObj = {};
    const sampleObj = data[0];
    
    // Apply field mapping to create a sample object for table definition
    // This ensures column names match our desired output format
    Object.keys(tagFieldMapping).forEach(key => {
      if (sampleObj[key] !== undefined) {
        const mappedKey = tagFieldMapping[key];
        // Determine the appropriate type based on our column types configuration
        const columnType = tagColumnTypes[mappedKey] || COLUMN_TYPE.INTEGER;
        
        // Set sample value based on column type
        if (columnType === COLUMN_TYPE.DATE) {
          mappedObj[mappedKey] = new Date();
        } else if (columnType === COLUMN_TYPE.TEXT) {
          mappedObj[mappedKey] = "sample_text";
        } else {
          mappedObj[mappedKey] = 0;
        }
      }
    });
    
    // Add calculated fields
    mappedObj["total_replies_email_1"] = 0;
    mappedObj["total_replies_email_2"] = 0;
    mappedObj["total_replies_email_3"] = 0;
    
    // Create the table with our predefined column order
    const tableDef = `
      CREATE TABLE "public2"."TagAnalytics" (
        "id" SERIAL PRIMARY KEY,
        ${tagColumnOrder.map(col => {
          const colType = tagColumnTypes[col] || COLUMN_TYPE.INTEGER;
          return `"${col}" ${colType}`;
        }).join(',\n        ')}
      )
    `;
    
    await prisma.$executeRawUnsafe(tableDef);
    console.log("✅ Tag analytics table created successfully");
  } catch (error) {
    console.error("❌ Error recreating Tag analytics table:", error.message);
    throw error;
  }
}

/**
 * Truncates the specified table
 * @param {String} tableName - Name of the table to truncate
 */
async function truncateTable(tableName) {
  try {
    console.log(`🗑️ Truncating ${tableName} table...`);
    
    await prisma.$executeRawUnsafe(`TRUNCATE TABLE "public2"."${tableName}" CASCADE`);
    
    console.log(`✅ ${tableName} table successfully truncated`);
  } catch (error) {
    console.error(`❌ Error truncating ${tableName} table:`, error.message);
    throw error;
  }
}

/**
 * Inserts data into the specified table
 * @param {Array} data - Array of data objects to insert
 * @param {String} tableName - Name of the table to insert data into
 * @param {Object} fieldMapping - Optional mapping from JSON fields to column names
 */
async function insertData(data, tableName, fieldMapping = null) {
  try {
    console.log(`📝 Inserting ${data.length} records into ${tableName}...`);
    
    const BATCH_SIZE = 500;
    
    for (let i = 0; i < data.length; i += BATCH_SIZE) {
      const batch = data.slice(i, i + BATCH_SIZE);
      console.log(`🔄 Inserting batch ${Math.floor(i / BATCH_SIZE) + 1} of ${Math.ceil(data.length / BATCH_SIZE)}...`);
      
      // Get column information based on the desired column order
      let dbColumns;
      
      // For FunnelClient table, use the predefined column order
      if (tableName === "FunnelClientAnalytics" && funnelClientColumnOrder) {
        dbColumns = funnelClientColumnOrder;
      } else if (fieldMapping) {
        // For other tables with mapping, use the mapping values
        dbColumns = [...new Set(Object.values(fieldMapping))];
      } else {
        // No mapping or column order, use object keys
        const sampleObj = batch[0];
        dbColumns = Object.keys(sampleObj);
      }
      
      // Create placeholders for values insertion
      const placeholders = [];
      const values = [];
      let paramIndex = 1;
      
      batch.forEach(record => {
        const rowPlaceholders = [];
        
        dbColumns.forEach(dbColumn => {
          // Find corresponding JSON field
          let jsonField = null;
          let value = null;
          
          // For calculated fields that don't have a direct mapping from JSON fields
          if (dbColumn.startsWith('total_replies_email_')) {
            // These are already in the processed record
            value = record[dbColumn];
          } else if (fieldMapping) {
            // Find the JSON field that maps to this DB column
            for (const [jField, dColumn] of Object.entries(fieldMapping)) {
              if (dColumn === dbColumn) {
                jsonField = jField;
                value = record[jsonField];
                break;
              }
            }
          } else {
            // No mapping, treat the DB column as the JSON field
            jsonField = dbColumn;
            value = record[jsonField];
          }
          
          // Handle date conversions
          if (dbColumn === 'week_start_date' && value) {
            // Add explicit casting for date columns in the SQL placeholder
            rowPlaceholders.push(`$${paramIndex}::DATE`);
          } else {
            rowPlaceholders.push(`$${paramIndex}`);
          }
          
          values.push(value);
          paramIndex++;
        });
        
        placeholders.push(`(${rowPlaceholders.join(', ')})`);
      });
      
      // Build the insert query with proper database column names
      // Use ordered column names when available
      const columnNames = dbColumns.map(col => `"${col.replace(/"/g, '""')}"`);
      const query = `
        INSERT INTO "public2"."${tableName}" (${columnNames.join(', ')})
        VALUES ${placeholders.join(', ')}
      `;
      
      await prisma.$executeRawUnsafe(query, ...values);
    }
    
    console.log(`✅ Successfully inserted ${data.length} records into ${tableName}`);
  } catch (error) {
    console.error(`❌ Error inserting data into ${tableName}:`, error.message);
    throw error;
  }
}

/**
 * Processes FunnelClient data to add calculated columns
 * @param {Array} rawData - Raw data from API
 * @returns {Array} - Processed data with calculated columns
 */
function processFunnelClientData(rawData) {
  console.log("📊 Processing FunnelClient data and adding calculated columns...");
  
  return rawData.map(record => {
    // Create a new object that will hold all the data
    const processedRecord = { ...record };
    
    // Ensure all numeric fields are properly parsed as integers or default to 0
    // Main email counts
    processedRecord["Sum of email_1_sent_count"] = parseInt(record["Sum of email_1_sent_count"]) || 0;
    processedRecord["Sum of email_2_sent_count"] = parseInt(record["Sum of email_2_sent_count"]) || 0;
    processedRecord["Sum of email_3_sent_count"] = parseInt(record["Sum of email_3_sent_count"]) || 0;
    
    // Email 1 type breakdowns
    processedRecord["Sum of email_1_sent_work_count"] = parseInt(record["Sum of email_1_sent_work_count"]) || 0;
    processedRecord["Sum of email_1_sent_personal_count"] = parseInt(record["Sum of email_1_sent_personal_count"]) || 0;
    processedRecord["Sum of email_1_sent_role_count"] = parseInt(record["Sum of email_1_sent_role_count"]) || 0;
    processedRecord["Sum of email_1_sent_unknown_count"] = parseInt(record["Sum of email_1_sent_unknown_count"]) || 0;
    
    // Email 2 type breakdowns
    processedRecord["Sum of email_2_sent_work_count"] = parseInt(record["Sum of email_2_sent_work_count"]) || 0;
    processedRecord["Sum of email_2_sent_personal_count"] = parseInt(record["Sum of email_2_sent_personal_count"]) || 0;
    processedRecord["Sum of email_2_sent_role_count"] = parseInt(record["Sum of email_2_sent_role_count"]) || 0;
    processedRecord["Sum of email_2_sent_unknown_count"] = parseInt(record["Sum of email_2_sent_unknown_count"]) || 0;
    
    // Email 3 type breakdowns
    processedRecord["Sum of email_3_sent_work_count"] = parseInt(record["Sum of email_3_sent_work_count"]) || 0;
    processedRecord["Sum of email_3_sent_personal_count"] = parseInt(record["Sum of email_3_sent_personal_count"]) || 0;
    processedRecord["Sum of email_3_sent_role_count"] = parseInt(record["Sum of email_3_sent_role_count"]) || 0;
    processedRecord["Sum of email_3_sent_unknown_count"] = parseInt(record["Sum of email_3_sent_unknown_count"]) || 0;
    
    // Replies
    const replies1 = parseInt(record["Sum of replies_after_email_1_count"]) || 0;
    const replies2 = parseInt(record["Sum of replies_after_email_2_count"]) || 0;
    const replies3 = parseInt(record["Sum of replies_after_email_3_count"]) || 0;
    
    // Automated replies
    const autoReplies1 = parseInt(record["Sum of automated_replies_after_email_1_count"]) || 0;
    const autoReplies2 = parseInt(record["Sum of automated_replies_after_email_2_count"]) || 0;
    const autoReplies3 = parseInt(record["Sum of automated_replies_after_email_3_count"]) || 0;
    
    // Error replies
    const errorReplies1 = parseInt(record["Sum of error_replies_after_email_1_count"]) || 0;
    const errorReplies2 = parseInt(record["Sum of error_replies_after_email_2_count"]) || 0;
    const errorReplies3 = parseInt(record["Sum of error_replies_after_email_3_count"]) || 0;
    
    // Meeting slots
    processedRecord["Sum of meeting_slots_sent_after_1"] = parseInt(record["Sum of meeting_slots_sent_after_1"]) || 0;
    processedRecord["Sum of meeting_slots_sent_after_2"] = parseInt(record["Sum of meeting_slots_sent_after_2"]) || 0;
    processedRecord["Sum of meeting_slots_sent_after_3"] = parseInt(record["Sum of meeting_slots_sent_after_3"]) || 0;
    processedRecord["Sum of meeting_slots_sent_unknown"] = parseInt(record["Sum of meeting_slots_sent_unknown"]) || 0;
    processedRecord["Sum of total_meeting_slots_sent"] = parseInt(record["Sum of total_meeting_slots_sent"]) || 0;
    
    // Meetings booked
    processedRecord["Sum of meetings_booked"] = parseInt(record["Sum of meetings_booked"]) || 0;
    
    // Calculate total_replies for each email (auto replies + regular replies, excluding error replies)
    processedRecord["total_replies_email_1"] = autoReplies1 + replies1;
    processedRecord["total_replies_email_2"] = autoReplies2 + replies2;
    processedRecord["total_replies_email_3"] = autoReplies3 + replies3;
    
    return processedRecord;
  });
}

/**
 * Processes Provider Analytics data to add calculated columns
 * @param {Array} rawData - Raw data from API
 * @returns {Array} - Processed data with calculated columns
 */
function processProviderAnalyticsData(rawData) {
  console.log("🔄 Processing provider analytics data...");
  
  return rawData.map(item => {
    const processedItem = { ...item };
    
    // Calculate total replies for email 1
    const repliesAfterEmail1 = parseInt(item["Sum of replies_after_email_1_count"]) || 0;
    const autoRepliesAfterEmail1 = parseInt(item["Sum of automated_replies_after_email_1_count"]) || 0;
    processedItem.total_replies_email_1 = repliesAfterEmail1 + autoRepliesAfterEmail1;
    
    // Calculate total replies for email 2
    const repliesAfterEmail2 = parseInt(item["Sum of replies_after_email_2_count"]) || 0;
    const autoRepliesAfterEmail2 = parseInt(item["Sum of automated_replies_after_email_2_count"]) || 0;
    processedItem.total_replies_email_2 = repliesAfterEmail2 + autoRepliesAfterEmail2;
    
    // Calculate total replies for email 3
    const repliesAfterEmail3 = parseInt(item["Sum of replies_after_email_3_count"]) || 0;
    const autoRepliesAfterEmail3 = parseInt(item["Sum of automated_replies_after_email_3_count"]) || 0;
    processedItem.total_replies_email_3 = repliesAfterEmail3 + autoRepliesAfterEmail3;
    
    return processedItem;
  });
}

/**
 * Processes Tag data to add calculated columns
 * @param {Array} rawData - Raw data from API
 * @returns {Array} - Processed data with calculated columns
 */
function processTagData(rawData) {
  console.log("📊 Processing tag data and adding calculated columns...");
  
  return rawData.map(record => {
    // Create a new object that will hold all the data
    const processedRecord = { ...record };
    
    // Ensure all numeric fields are properly parsed as integers or default to 0
    // Main email counts
    processedRecord["Sum of email_1_sent_count"] = parseInt(record["Sum of email_1_sent_count"]) || 0;
    processedRecord["Sum of email_2_sent_count"] = parseInt(record["Sum of email_2_sent_count"]) || 0;
    processedRecord["Sum of email_3_sent_count"] = parseInt(record["Sum of email_3_sent_count"]) || 0;
    
    // Replies
    const replies1 = parseInt(record["Sum of replies_after_email_1_count"]) || 0;
    const replies2 = parseInt(record["Sum of replies_after_email_2_count"]) || 0;
    const replies3 = parseInt(record["Sum of replies_after_email_3_count"]) || 0;
    
    // Automated replies
    const autoReplies1 = parseInt(record["Sum of automated_replies_after_email_1_count"]) || 0;
    const autoReplies2 = parseInt(record["Sum of automated_replies_after_email_2_count"]) || 0;
    const autoReplies3 = parseInt(record["Sum of automated_replies_after_email_3_count"]) || 0;
    
    // Error replies
    const errorReplies1 = parseInt(record["Sum of error_replies_after_email_1_count"]) || 0;
    const errorReplies2 = parseInt(record["Sum of error_replies_after_email_2_count"]) || 0;
    const errorReplies3 = parseInt(record["Sum of error_replies_after_email_3_count"]) || 0;
    
    // Meeting slots
    processedRecord["Sum of meeting_slots_sent_after_1"] = parseInt(record["Sum of meeting_slots_sent_after_1"]) || 0;
    processedRecord["Sum of meeting_slots_sent_after_2"] = parseInt(record["Sum of meeting_slots_sent_after_2"]) || 0;
    processedRecord["Sum of meeting_slots_sent_after_3"] = parseInt(record["Sum of meeting_slots_sent_after_3"]) || 0;
    processedRecord["Sum of meeting_slots_sent_unknown"] = parseInt(record["Sum of meeting_slots_sent_unknown"]) || 0;
    processedRecord["Sum of total_meeting_slots_sent"] = parseInt(record["Sum of total_meeting_slots_sent"]) || 0;
    
    // Meetings booked
    processedRecord["Sum of meetings_booked"] = parseInt(record["Sum of meetings_booked"]) || 0;
    
    // Calculate total_replies for each email (auto replies + regular replies, excluding error replies)
    processedRecord["total_replies_email_1"] = autoReplies1 + replies1;
    processedRecord["total_replies_email_2"] = autoReplies2 + replies2;
    processedRecord["total_replies_email_3"] = autoReplies3 + replies3;
    
    return processedRecord;
  });
}

/**
 * Updates FunnelClient analytics by fetching data and inserting it into the database
 */
async function updateFunnelClientAnalytics() {
  try {
    console.log("🔄 Starting FunnelClient analytics update...");
    
    // Fetch data from Metabase API
    const rawData = await fetchFunnelClientData();
    
    // Process the raw data to add calculated fields
    const processedData = processFunnelClientData(rawData);
    
    // Output the data sample
    console.log("📃 Processed FunnelClient data sample (first 5 records):");
    console.log(JSON.stringify(processedData.slice(0, 5), null, 2));
    console.log(`Total records to be inserted: ${processedData.length}`);
    
    // Write processed data to JSON file for backup
    const outputFilePath = path.join(__dirname, "funnelClientData.json");
    fs.writeFileSync(outputFilePath, JSON.stringify(processedData, null, 2));
    console.log(`💾 Processed FunnelClient data backed up to: ${outputFilePath}`);
    
    // Ensure necessary table exists
    await ensureFunnelClientTableExists();
    
    // Truncate existing data
    await truncateTable("FunnelClientAnalytics");
    
    // Insert processed data into the database using the field mapping
    await insertData(processedData, "FunnelClientAnalytics", funnelClientFieldMapping);
    
    console.log("✅ FunnelClient analytics update completed successfully!");
    return processedData;
  } catch (error) {
    console.error("❌ FunnelClient analytics update failed:", error.message);
    throw error;
  }
}

/**
 * Ensures that the required database table exists for Provider Analytics data
 * Drops the existing table and recreates it with the latest schema
 */
async function ensureProviderAnalyticsTableExists() {
  try {
    console.log("🔄 Recreating ProviderAnalytics table...");
    
    // First drop the table if it exists
    try {
      console.log("🗑️ Dropping existing ProviderAnalytics table if it exists...");
      await prisma.$executeRawUnsafe(`DROP TABLE IF EXISTS "public2"."ProviderAnalytics";`);
      console.log("✅ Existing ProviderAnalytics table dropped successfully");
    } catch (dropError) {
      console.error("⚠️ Warning while dropping ProviderAnalytics table:", dropError.message);
      // Continue execution even if drop fails
    }
    
    // SQL to create the table with our specific schema
    const createTableSQL = `
      CREATE TABLE "public2"."ProviderAnalytics" (
        id SERIAL PRIMARY KEY,
        week_start_date DATE,
        campaign_code TEXT,
        client_name TEXT,
        provider_name TEXT,
        email_1_sent INTEGER DEFAULT 0,
        replies_after_email_1 INTEGER DEFAULT 0,
        auto_replies_after_email_1 INTEGER DEFAULT 0,
        error_replies_after_email_1 INTEGER DEFAULT 0,
        total_replies_email_1 INTEGER DEFAULT 0,
        email_2_sent INTEGER DEFAULT 0,
        replies_after_email_2 INTEGER DEFAULT 0,
        auto_replies_after_email_2 INTEGER DEFAULT 0,
        error_replies_after_email_2 INTEGER DEFAULT 0,
        total_replies_email_2 INTEGER DEFAULT 0,
        email_3_sent INTEGER DEFAULT 0,
        replies_after_email_3 INTEGER DEFAULT 0,
        auto_replies_after_email_3 INTEGER DEFAULT 0,
        error_replies_after_email_3 INTEGER DEFAULT 0,
        total_replies_email_3 INTEGER DEFAULT 0
      );
    `;
    
    // Execute the SQL statement
    await prisma.$executeRawUnsafe(createTableSQL);
    console.log("✅ ProviderAnalytics table created successfully");
    
  } catch (error) {
    console.error("❌ Error recreating ProviderAnalytics table:", error.message);
    throw error;
  }
}

/**
 * Updates Provider Analytics by fetching data and inserting it into the database
 */
async function updateProviderAnalyticsData() {
  try {
    console.log("🔄 Starting provider analytics data update process...");
    
    // Ensure table exists
    await ensureProviderAnalyticsTableExists();
    
    // Clear existing data
    await truncateTable("ProviderAnalytics");
    
    // Fetch new data
    const rawData = await fetchProviderAnalyticsData();
    if (rawData.length === 0) {
      console.log("⚠️ No provider analytics data received from API. Update aborted.");
      return;
    }
    
    // Process data to add calculated columns
    const processedData = processProviderAnalyticsData(rawData);
    
    // Insert into database
    await insertData(processedData, "ProviderAnalytics", providerAnalyticsFieldMapping);
    
    console.log("✅ Provider analytics data updated successfully!");
  } catch (error) {
    console.error("❌ Error updating provider analytics data:", error.message);
    throw error;
  }
}

/**
 * Updates Tag analytics by fetching data and inserting it into the database
 */
async function updateTagAnalytics() {
  try {
    console.log("🔄 Starting Tag analytics update...");
    
    // Fetch tag data from Metabase API
    const tagData = await fetchTagData();
    
    if (!tagData || tagData.length === 0) {
      console.error("❌ No tag data found. Aborting update.");
      return;
    }
    
    // Process the tag data to add calculated fields
    const processedTagData = processTagData(tagData);
    
    // Ensure the Tag analytics table exists
    await ensureTagTableExists(processedTagData);
    
    // Truncate the existing table to prepare for fresh data
    await truncateTable("TagAnalytics");
    
    // Insert the processed data into the database using the tag field mapping
    await insertData(processedTagData, "TagAnalytics", tagFieldMapping);
    
    console.log("✅ Tag analytics successfully updated!");
  } catch (error) {
    console.error("❌ Error updating Tag analytics:", error.message);
    throw error;
  }
}

/**
 * Main function that determines which mode to run
 */
async function main() {
  console.log(`🚀 Running in ${mode} mode...`);

  switch (mode) {
    case 'funnelClient':
      await updateFunnelClientAnalytics();
      break;
    case 'tag':
      await updateTagAnalytics();
      break;
    case 'providerAnalytics':
      await updateProviderAnalyticsData();
      break;
    default:
      console.error(`❌ Error: Unknown mode '${mode}'`);
      process.exit(1);
  }
}

// Execute the main function
(async () => {
  try {
    await main();
    console.log("✅ Script execution completed successfully!");
    process.exit(0);
  } catch (error) {
    console.error("❌ Script execution failed:", error.message);
    process.exit(1);
  }
})();