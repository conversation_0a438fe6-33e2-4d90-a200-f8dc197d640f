-- AlterTable
ALTER TABLE "ClientsProspectsMatching" ADD COLUMN     "amazon_prospect_id" INTEGER;

-- CreateTable
CREATE TABLE "AmazonProspect" (
    "prospect_id" SERIAL NOT NULL,
    "person_name" TEXT NOT NULL DEFAULT '',
    "person_linkedin" TEXT NOT NULL DEFAULT '',
    "email" TEXT NOT NULL DEFAULT '',
    "job_title" TEXT NOT NULL DEFAULT '',
    "source" TEXT NOT NULL DEFAULT '',
    "sources" JSONB NOT NULL DEFAULT '{}',
    "email_status" TEXT NOT NULL DEFAULT '',
    "seller_group_id" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AmazonProspect_pkey" PRIMARY KEY ("prospect_id")
);

-- CreateIndex
CREATE INDEX "AmazonProspect_person_linkedin_idx" ON "AmazonProspect"("person_linkedin");

-- CreateIndex
CREATE INDEX "AmazonProspect_email_idx" ON "AmazonProspect"("email");

-- CreateIndex
CREATE INDEX "ClientsProspectsMatching_amazon_prospect_id_idx" ON "ClientsProspectsMatching"("amazon_prospect_id");

-- AddForeignKey
ALTER TABLE "AmazonProspect" ADD CONSTRAINT "AmazonProspect_seller_group_id_fkey" FOREIGN KEY ("seller_group_id") REFERENCES "SellerGroup"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ClientsProspectsMatching" ADD CONSTRAINT "ClientsProspectsMatching_amazon_prospect_id_fkey" FOREIGN KEY ("amazon_prospect_id") REFERENCES "AmazonProspect"("prospect_id") ON DELETE SET NULL ON UPDATE CASCADE;
