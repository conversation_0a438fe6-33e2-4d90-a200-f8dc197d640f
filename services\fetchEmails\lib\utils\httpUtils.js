const axios = require("axios");
const fs = require("fs").promises;
const path = require("path");
const { getHtmlByProxy } = require("../../../../utils/getHtmlByProxy");

// Import constants
const { DEFAULT_OPTIONS } = require("../constants");

// Request timeouts and retry settings
const REQUEST_TIMEOUT = 30000; // 30 seconds
const STATUS_CHECK_TIMEOUT = 10000; // 10 seconds
const RETRY_BACKOFF_BASE = 2000; // 2 seconds

/**
 * Default user agents to rotate for requests
 */
const USER_AGENTS = [
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
  "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
  "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
];

/**
 * Save HTML content to file for debugging
 * @param {string} url - URL of the page
 * @param {string} html - HTML content to save
 * @returns {Promise<void>}
 */
async function saveHtml(url, html) {
  try {
    // Create a filename based on the URL (sanitize it)
    const fileName = `${url.replace(/[^a-zA-Z0-9]/g, "_")}_html.html`;

    const dir = path.join(__dirname, "../../htmlData");
    if (!(await fs.stat(dir).catch(() => false))) {
      await fs.mkdir(dir, { recursive: true });
    }

    const filePath = path.join(dir, fileName);
    await fs.writeFile(filePath, html);
    console.log(`HTML saved for: ${url}`);
  } catch (error) {
    console.error(`Error in saveHTML function for ${url}:`, error);
  }
}

/**
 * Converts HTTP URL to HTTPS
 * @param {string} url - URL to convert
 * @returns {string} - URL with HTTPS
 */
function convertToHttps(url) {
  if (url && url.startsWith("http://")) {
    return url.replace(/^http:\/\//i, "https://");
  }
  return url;
}

/**
 * Makes HTTP request with the full fallback strategy:
 * 1. Try original URL (respect HTTP if present)
 * 2. If it fails and was HTTP, try HTTPS version
 * 3. If both fail, use proxy via getHtmlByProxy
 *
 * @param {string} url - URL to fetch
 * @param {number} retries - Number of retries per method
 * @param {boolean} useProxy - Whether to use proxy as fallback
 * @param {boolean} saveHtmlContent - Whether to save the HTML content
 * @returns {Promise<string>} - HTML content
 */
async function fetchHtmlWithRetry(
  url,
  retries = 3,
  useProxy = false,
  saveHtmlContent = false,
  failedCount = { count: 0 },
  maxProxyPages = 0,
  addFailedUrl = () => {}
) {
  // Validate URL protocol first
  if (!url || typeof url !== "string") {
    throw new Error(`Invalid URL: ${url}`);
  }

  // Check for non-HTTP protocols
  if (url.startsWith("mailto:")) {
    throw new Error(
      "Cannot fetch mailto: URLs - these are email addresses, not web pages"
    );
  }

  if (url.startsWith("tel:")) {
    throw new Error(
      "Cannot fetch tel: URLs - these are phone numbers, not web pages"
    );
  }

  if (!url.startsWith("http://") && !url.startsWith("https://")) {
    throw new Error(`Unsupported URL protocol: ${url}`);
  }

  let lastError;
  const originalUrl = url;
  const isHttpUrl = url.startsWith("http://");

  // 1. Try with original URL first (respect HTTP if present)
  console.log(`Trying original URL: ${url}`);
  try {
    const html = await attemptFetch(url, retries);
    if (html) {
      if (saveHtmlContent) {
        await saveHtml(url, html);
      }
      return html;
    }
  } catch (error) {
    lastError = error;
    console.warn(`Failed to fetch using original URL: ${error.message}`);
  }

  // 2. If original was HTTP and failed, try HTTPS version
  if (isHttpUrl) {
    const httpsUrl = convertToHttps(originalUrl);
    console.log(`Trying HTTPS version: ${httpsUrl}`);
    try {
      const html = await attemptFetch(httpsUrl, retries);
      if (html) {
        if (saveHtmlContent) {
          await saveHtml(httpsUrl, html);
        }
        return html;
      }
    } catch (error) {
      lastError = error;
      console.warn(`Failed to fetch using HTTPS version: ${error.message}`);
    }
  }

  // 3. If direct requests failed and proxy is enabled, use getHtmlByProxy
  if (useProxy && failedCount.count < maxProxyPages) {
    console.log(`Trying with proxy service for ${url}`);
    try {
      // Use getHtmlByProxy with appropriate retry logic
      addFailedUrl(url);
      const html = await getHtmlByProxy(
        url,
        1,
        Math.max(1, retries - 1),
        false
      );

      if (html) {
        if (saveHtmlContent) {
          await saveHtml(url, html);
        }
        return html;
      }
    } catch (proxyError) {
      console.error(`Proxy request failed for ${url}: ${proxyError.message}`);
      lastError = proxyError;
    }
  }

  if (useProxy && failedCount.count >= maxProxyPages) {
    throw new Error("Cancelling Fetch as maximum budget for proxy exceeded");
  }
  // If all attempts fail, throw the last error
  throw new Error(
    `Failed to fetch ${url} after all fallback attempts: ${lastError.message}`
  );
}

/**
 * Helper function to attempt fetching a URL with retries
 * @param {string} url - URL to fetch
 * @param {number} retries - Number of retries
 * @returns {Promise<string>} - HTML content or null if failed
 */
async function attemptFetch(url, retries) {
  for (let attempt = 0; attempt < retries; attempt++) {
    try {
      // Rotate user agents for each attempt
      const userAgent =
        USER_AGENTS[Math.floor(Math.random() * USER_AGENTS.length)];

      const response = await axios.get(url, {
        headers: {
          "User-Agent": userAgent,
          Accept:
            "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
          "Accept-Language": "en-US,en;q=0.5",
        },
        timeout: REQUEST_TIMEOUT,
        maxRedirects: 5,
      });

      if (response.status === 200) {
        return response.data;
      }
      
    } catch (error) {
      // Check if the error is due to 401 or 403 status - don't retry these
      if (error.response && (error.response.status === 403 || error.response.status === 401 || error.response.status >= 500)) {
        return '';
      } 
      console.warn(
        `Attempt ${attempt + 1} failed for ${url}: ${error.message}`
      );
      // Wait before retry with exponential backoff
      await new Promise((resolve) =>
        setTimeout(resolve, RETRY_BACKOFF_BASE * Math.pow(2, attempt))
      );
    }
  }
  return null;
}

/**
 * Detects if website is responding or blocked
 * @param {string} url - URL to check
 * @returns {Promise<{isAccessible: boolean, status: string, statusCode: number}>}
 */
async function checkWebsiteStatus(url) {
  try {
    const response = await axios.get(url, {
      headers: {
        "User-Agent": USER_AGENTS[0],
      },
      timeout: STATUS_CHECK_TIMEOUT,
      validateStatus: false, // Don't throw errors for any status code
    });

    // Check if we're getting a successful response
    if (response.status >= 200 && response.status < 300) {
      return {
        isAccessible: true,
        status: "Available",
        statusCode: response.status,
      };
    }

    // Handle specific status codes
    if (response.status === 403 || response.status === 429) {
      return {
        isAccessible: false,
        status: "Blocked",
        statusCode: response.status,
      };
    }

    if (response.status >= 500) {
      return {
        isAccessible: false,
        status: "Server Error",
        statusCode: response.status,
      };
    }

    return {
      isAccessible: false,
      status: `HTTP Error: ${response.status}`,
      statusCode: response.status,
    };
  } catch (error) {
    // Handle timeout or network errors
    if (error.code === "ECONNABORTED") {
      return {
        isAccessible: false,
        status: "Timeout",
        statusCode: 0,
      };
    }

    if (error.code === "ENOTFOUND") {
      return {
        isAccessible: false,
        status: "Domain Not Found",
        statusCode: 0,
      };
    }

    return {
      isAccessible: false,
      status: `Error: ${error.message}`,
      statusCode: 0,
    };
  }
}

module.exports = {
  fetchHtmlWithRetry,
  saveHtml,
  checkWebsiteStatus,
  USER_AGENTS,
};
