const generateMetaData = async (data) => {
  try {
    const sellerImage = await getSellerImage(data);
    const sellerInfo = await getSellerInfo(data);
    const productInfo = await getProductInfo(data);
    const productImage = await getProductImage(data);
    const productDescription = await getProductDescription(data);

    return {
      sellerImage,
      sellerInfo,
      productInfo,
      productImage,
      productDescription,
    };
  } catch (error) {
    console.error("Error generating metadata:", error);
    throw error;
  }
};

module.exports = { generateMetaData };
