const prisma = require("../database/prisma/getPrismaClient");
const { getHtmlByProxy } = require("./getHtmlByProxy");
const { createKeyFromAmazonUrl } = require("./s3");
const cheerio = require("cheerio");

async function processSellerPage(seller, jobId) {
  try {
    console.log("Processing For Seller:", seller.sellerName);
    console.log("Seller Id:", seller.sellerId);

    const sellerData = await getSellerDetails(
      seller.sellerUrl,
      seller.sellerId
    );

    // Log the extracted data
    console.log("Extracted Seller Data:", sellerData);

    // Use the extracted sellerEmails and sellerContactNumbers directly
    const emails =
      sellerData.sellerEmails.length > 0 ? sellerData.sellerEmails : [""];
    const contacts =
      sellerData.sellerContactNumbers.length > 0
        ? sellerData.sellerContactNumbers
        : [""];

    // Log the arrays after assignment
    console.log("Emails:", emails);
    console.log("Contacts:", contacts);

    // Fetch existing entries for the seller
    const existingEntries = await prisma.outputData.findMany({
      where: { sellerId: seller.sellerId, jobId: jobId },
    });
    console.log({ existingEntries });

    // Update the first existing entry if it exists
    if (existingEntries.length > 0) {
      console.log("Updating For:", existingEntries[0].id);
      await prisma.outputData.update({
        where: { id: existingEntries[0].id }, // Update the first existing entry
        data: {
          inputStatus: "success",
          sellerAddress: sellerData.sellerAddress || "",
          sellerPincode: sellerData.sellerPincode || "",
          sellerEmail: emails[0], // Update with the first email
          contactNumber: contacts[0] || "", // Update with the first contact, using empty string if none
        },
      });
      console.log("Successfully updated entry for seller:", seller.sellerName);
    }

    // Create new rows for additional emails/contacts, starting from index 1
    for (let i = 1; i < Math.max(emails.length, contacts.length); i++) {
      const newEmail = emails[i];
      const newContact = contacts[i] || ""; // Use empty string if no contact for this iteration

      const newEntryData = {
        sellerId: seller.sellerId,
        sellerName: seller.sellerName,
        inputStatus: "success",
        sellerUrl: seller.sellerUrl,
        sellerAddress: sellerData.sellerAddress || "",
        sellerPincode: sellerData.sellerPincode || "",
        sellerEmail: newEmail,
        contactNumber: newContact,
        jobId: seller.jobId,
      };

      // Log each new entry's data to confirm before creation
      console.log("Creating new entry:", newEntryData);

      // Perform the database insertion for the additional email
      await prisma.outputData.create({
        data: newEntryData,
      });

      console.log(
        "Successfully created new entry for seller:",
        seller.sellerName,
        "with email:",
        newEmail
      );
    }
  } catch (error) {
    console.error("Error fetching seller page:", error);

    // Update seller's input status to error in case of failure
    await prisma.outputData.update({
      where: {
        id: seller.id, // Use the seller's id
      },
      data: {
        inputStatus: "error",
      },
    });
  }
}

async function getSellerDetails(url, sellerId) {
  // Fetch seller data to check if htmlData exists
  const seller = await prisma.seller.findUnique({
    where: {
      id: sellerId,
    },
    select: {
      htmlData: true, // Only fetch htmlData from the database
    },
  });

  let sellerPageHtml;

  // If htmlData is already in the database, use it. Otherwise, fetch it via the proxy.
  if (seller && seller.htmlData) {
    console.log("Using htmlData from the database");
    sellerPageHtml = seller.htmlData;
  } else {
    console.log("Fetching htmlData using proxy");
    sellerPageHtml = await getHtmlByProxy(
      url,
      1,
      2,
      true,
      24 * 30,
      createKeyFromAmazonUrl(url)
    );

    // Update the seller's htmlData in the database
    await prisma.seller.update({
      where: {
        id: sellerId,
      },
      data: {
        htmlData: sellerPageHtml,
      },
    });
  }

  const $ = cheerio.load(sellerPageHtml);

  // Extract business name
  const sellerBusinessName = $(".a-text-bold:contains('Business Name:')")
    .next()
    .text()
    .trim();

  // Extract business address
  const businessAddress = [];
  $(".a-text-bold:contains('Business Address:')")
    .parent()
    .nextAll(".indent-left")
    .each(function () {
      businessAddress.push($(this).text().trim());
    });
  const sellerPincode = businessAddress[businessAddress.length - 2];

  // Extract contact numbers
  const sellerContactNumbers = [];
  $("#seller-contact-phone").each(function () {
    const contactNumber = $(this).text().trim();
    if (contactNumber) {
      sellerContactNumbers.push(contactNumber);
    }
  });

  // Extract seller emails using regex
  const sellerEmailText = $("#spp-expander-about-seller").text().trim();
  const emailMatches = sellerEmailText.match(
    /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g
  );
  const sellerEmails = emailMatches ? emailMatches : [];

  // Return the extracted data
  return {
    sellerAddress: businessAddress.join(", "),
    sellerEmails,
    sellerContactNumbers,
    sellerPincode,
  };
}

module.exports = processSellerPage;
