const PortkeyLangSmithWrapper = require('../services/ai/portkeyLangsmithWrapper');
const { getAssistantResponse } = require('../services/scrapeGPT/assistant');
const { getChatGPTResponse } = require('../services/scrapeGPT/request');
require('dotenv').config();

/**
 * Example scripts demonstrating Portkey + LangSmith integration
 * 
 * These examples show how to use the wrapper for different AI operations
 * with comprehensive tracing and monitoring.
 */

// Example 1: Basic Chat Completion
async function exampleChatCompletion() {
  console.log('\n=== Example 1: Basic Chat Completion ===');
  
  try {
    const wrapper = new PortkeyLangSmithWrapper('chat');
    
    const messages = [
      {
        role: 'system',
        content: 'You are a helpful assistant that provides concise answers about e-commerce and Amazon selling.'
      },
      {
        role: 'user',
        content: 'What are the key factors for success on Amazon marketplace?'
      }
    ];
    
    const response = await wrapper.createChatCompletion(messages, {
      temperature: 0.7,
      max_tokens: 200,
    });
    
    console.log('Response:', response.choices[0]?.message?.content);
    console.log('Usage:', response.usage);
    console.log('Model:', response.model);
    
  } catch (error) {
    console.error('Chat completion failed:', error.message);
  }
}

// Example 2: Assistant API with Custom Instructions
async function exampleAssistantAPI() {
  console.log('\n=== Example 2: Assistant API ===');
  
  try {
    // Note: You'll need to create an assistant in OpenAI first
    const assistantId = process.env.OPENAI_ASSISTANT_ID || 'asst_example123';
    
    const businessData = {
      company: 'Example Corp',
      products: ['Widget A', 'Widget B'],
      revenue: '$1M',
      marketplace: 'Amazon US'
    };
    
    const response = await getAssistantResponse(
      assistantId,
      JSON.stringify(businessData),
      true // Use Portkey
    );
    
    console.log('Assistant Response:', response.message);
    console.log('Usage:', {
      prompt_tokens: response.prompt_tokens,
      completion_tokens: response.completion_tokens,
      total_tokens: response.total_tokens
    });
    console.log('Model:', response.gptDetails);
    
  } catch (error) {
    console.error('Assistant API failed:', error.message);
  }
}

// Example 3: Text Analysis with Different Temperature Settings
async function exampleTextAnalysis() {
  console.log('\n=== Example 3: Text Analysis ===');
  
  try {
    const wrapper = new PortkeyLangSmithWrapper('analysis');
    
    const productDescription = `
      Premium wireless headphones with active noise cancellation.
      Features 30-hour battery life, premium sound quality, and comfortable design.
      Perfect for travel, work, and everyday use.
    `;
    
    const messages = [
      {
        role: 'system',
        content: 'Analyze the given product description and extract key selling points, target audience, and potential improvements.'
      },
      {
        role: 'user',
        content: productDescription
      }
    ];
    
    const response = await wrapper.createChatCompletion(messages, {
      temperature: 0.1, // Low temperature for analytical tasks
      max_tokens: 300,
    });
    
    console.log('Analysis:', response.choices[0]?.message?.content);
    console.log('Usage:', response.usage);
    
  } catch (error) {
    console.error('Text analysis failed:', error.message);
  }
}

// Example 4: Batch Processing with Error Handling
async function exampleBatchProcessing() {
  console.log('\n=== Example 4: Batch Processing ===');
  
  const wrapper = new PortkeyLangSmithWrapper('chat');
  
  const prompts = [
    'Summarize the benefits of FBA for Amazon sellers',
    'List 5 key metrics for Amazon PPC campaigns',
    'Explain the importance of product reviews on Amazon'
  ];
  
  const results = [];
  
  for (let i = 0; i < prompts.length; i++) {
    try {
      console.log(`Processing prompt ${i + 1}/${prompts.length}...`);
      
      const messages = [
        {
          role: 'system',
          content: 'You are an Amazon selling expert. Provide concise, actionable advice.'
        },
        {
          role: 'user',
          content: prompts[i]
        }
      ];
      
      const response = await wrapper.createChatCompletion(messages, {
        temperature: 0.5,
        max_tokens: 150,
      });
      
      results.push({
        prompt: prompts[i],
        response: response.choices[0]?.message?.content,
        usage: response.usage,
        success: true
      });
      
      // Add delay to respect rate limits
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.error(`Failed to process prompt ${i + 1}:`, error.message);
      results.push({
        prompt: prompts[i],
        error: error.message,
        success: false
      });
    }
  }
  
  console.log('\nBatch Processing Results:');
  results.forEach((result, index) => {
    console.log(`\n${index + 1}. ${result.prompt}`);
    if (result.success) {
      console.log(`   Response: ${result.response}`);
      console.log(`   Tokens: ${result.usage?.total_tokens || 'N/A'}`);
    } else {
      console.log(`   Error: ${result.error}`);
    }
  });
}

// Example 5: Using Legacy Functions with Portkey
async function exampleLegacyIntegration() {
  console.log('\n=== Example 5: Legacy Integration ===');
  
  try {
    // Using the updated getChatGPTResponse function
    const systemPrompt = 'You are a data analyst specializing in e-commerce metrics.';
    const userPrompt = 'Calculate the ROI for an Amazon PPC campaign with $1000 spend and $3000 revenue.';
    
    const response = await getChatGPTResponse(systemPrompt, userPrompt, true);
    
    console.log('Legacy Function Response:', response.message);
    console.log('Usage:', {
      prompt_tokens: response.prompt_tokens,
      completion_tokens: response.completion_tokens,
      total_tokens: response.total_tokens
    });
    
  } catch (error) {
    console.error('Legacy integration failed:', error.message);
  }
}

// Example 6: Usage Statistics and Monitoring
async function exampleUsageStats() {
  console.log('\n=== Example 6: Usage Statistics ===');
  
  try {
    const wrapper = new PortkeyLangSmithWrapper('chat');
    
    // Get usage statistics (placeholder implementation)
    const stats = await wrapper.getUsageStats('24h');
    
    console.log('Usage Statistics (24h):', {
      requests: stats?.requests || 'N/A',
      tokens: stats?.tokens || 'N/A',
      cost: stats?.cost || 'N/A',
      errors: stats?.errors || 'N/A',
      avgLatency: stats?.avgLatency || 'N/A'
    });
    
  } catch (error) {
    console.error('Failed to get usage stats:', error.message);
  }
}

// Main function to run all examples
async function runAllExamples() {
  console.log('🚀 Starting Portkey + LangSmith Integration Examples');
  console.log('================================================');
  
  try {
    await exampleChatCompletion();
    await exampleAssistantAPI();
    await exampleTextAnalysis();
    await exampleBatchProcessing();
    await exampleLegacyIntegration();
    await exampleUsageStats();
    
    console.log('\n✅ All examples completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Example execution failed:', error.message);
  }
}

// Export functions for individual testing
module.exports = {
  exampleChatCompletion,
  exampleAssistantAPI,
  exampleTextAnalysis,
  exampleBatchProcessing,
  exampleLegacyIntegration,
  exampleUsageStats,
  runAllExamples,
};

// Run examples if this file is executed directly
if (require.main === module) {
  runAllExamples().catch(console.error);
}
