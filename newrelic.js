"use strict";
exports.config = {
  app_name: ["Seller Bot"],
  license_key: "4a929492bf08cbe85fc1956eb9ef04e2FFFFNRAL",
  logging: {
    level: "debug",
    enabled: true,
  },
  distributed_tracing: {
    enabled: true,
  },
  transaction_tracer: {
    enabled: true, // Turns on detailed transaction monitoring
    record_sql: "raw", // Captures complete SQL queries with actual values
    explain_threshold: 100, // Triggers explain plans for queries taking > 100ms
  },
  slow_sql: {
    enabled: true,
    threshold: 5, // Threshold in milliseconds
  },
  allow_all_headers: true,
  attributes: {
    exclude: [
      "request.headers.cookie",
      "request.headers.authorization",
      "request.headers.proxyAuthorization",
      "request.headers.setCookie*",
      "request.headers.x*",
      "response.headers.cookie",
      "response.headers.authorization",
      "response.headers.proxyAuthorization",
      "response.headers.setCookie*",
      "response.headers.x*",
    ],
  },
};
