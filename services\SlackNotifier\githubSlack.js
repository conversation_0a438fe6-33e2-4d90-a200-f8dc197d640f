const axios = require("axios");
const prisma = require("../../database/prisma/getPrismaClient");
const SLACK_WEBHOOK_URL = process.env.GITHUB_SLACK_WEBHOOK;

const SLACK_BOT_TOKEN = process.env.SLACK_BOT_TOKEN;
const SLACK_CHANNEL_ID = process.env.SLACK_CHANNEL_ID;

const userMapping = {
  bunnie26: "U07N1C6BJH3",
  RishabhVerma: "U076TFCNDTK",
  praveen448b: "U074PEWG5PZ",
  vedansh: "U0765PL0W4V",
  utsav1607: "U088F8THDT6",
  haider000: "U07N6QSERQC",
  aguyran: "U07N9A0HUJH",
  Adhikram: "U07M8GBCEA2",
  kaydee0502: "U07PL7LJ65V",
  "Aryansethi-25": "U076NCN6ZDX",
  mukul<PERSON><PERSON>waj: "U07N6JHSG68",
  rahul09999: "U07P12RNEBS",
};

const slackToGitHubMapping = Object.fromEntries(
  Object.entries(userMapping).map(([github, slack]) => [slack, github])
);
async function getThreadByRepoAndIssue(repo, issueNumber) {
  return prisma.gitHubSlackThread.findUnique({
    where: {
      repo_issueNumber: {
        repo,
        issueNumber,
      },
    },
  });
}

async function saveOrUpdateThread({ repo, issueNumber, slackTs, channelId }) {
  return prisma.gitHubSlackThread.upsert({
    where: {
      repo_issueNumber: { repo, issueNumber },
    },
    update: {
      slackTs,
      channelId,
    },
    create: {
      repo,
      issueNumber,
      slackTs,
      channelId,
    },
  });
}

async function postToSlack({ text, thread_ts = null }) {
  const response = await axios.post(
    "https://slack.com/api/chat.postMessage",
    {
      channel: SLACK_CHANNEL_ID,
      text,
      ...(thread_ts && { thread_ts }),
    },
    {
      headers: {
        Authorization: `Bearer ${SLACK_BOT_TOKEN}`,
        "Content-Type": "application/json",
      },
    }
  );
  if (!response.data.ok) {
    throw new Error(JSON.stringify(response.data));
  }
  return response.data.ts;
}

async function getThreadBySlackTs(slackTs) {
  return prisma.gitHubSlackThread.findFirst({
    where: {
      slackTs: slackTs,
    },
  });
}

async function getInstallationIdForRepo(repo) {
  const mapping = await prisma.repoInstallationMapping.findUnique({
    where: { repo },
  });

  if (!mapping) throw new Error("No installation ID found for this repo");

  return mapping.installationId;
}

async function sendSlackNotification(payload) {
  const repo = payload.repository.full_name;
  const issueNumber = payload.issue.number;
  const issueTitle = payload.issue.title;
  const issueURL = payload.issue.html_url;
  const repoURL = payload.repository.html_url;
  const projectName = "Tech Roadmap";

  // 👉 Handle issue created
  if (payload.action === "opened" && payload.issue) {
    const author = payload.issue.user.login;
    const slackUserId = userMapping[author] || null;
    const slackMention = slackUserId ? `<@${slackUserId}>` : `*${author}*`;
    const body = payload.issue.body || "_No description provided._";

    const message =
      `🚨 *New GitHub Issue Opened - ${projectName}*\n` +
      `📌 *Issue:* <${issueURL}|${issueTitle} (#${issueNumber})> in <${repoURL}|${repo}>\n` +
      `👤 *Created by:* ${slackMention}\n` +
      `📝 *Description:* ${body}`;

    const ts = await postToSlack({ text: message });

    if (ts) {
      await saveOrUpdateThread({
        repo,
        issueNumber,
        slackTs: ts,
        channelId: SLACK_CHANNEL_ID,
      });
    }

    return;
  }

  if (
    (payload.action === "created" || payload.action === "edited") &&
    payload.comment
  ) {
    const commentBody = payload.comment.body;
    const commentAuthor = payload.comment.user.login;
    const commentURL = payload.comment.html_url;
    const previousComment = payload.changes?.body?.from || null;

    const slackUserId = userMapping[commentAuthor] || null;
    const slackMention = slackUserId
      ? `<@${slackUserId}>`
      : `*${commentAuthor}*`;

    let modifiedComment = commentBody;
    if (modifiedComment.includes("From Slack User")) {
      return;
    }
    const mentionedUsers = commentBody.match(/@([a-zA-Z0-9_-]+)/g) || [];
    mentionedUsers.forEach((mention) => {
      const username = mention.substring(1);
      if (userMapping[username]) {
        modifiedComment = modifiedComment.replace(
          mention,
          `<@${userMapping[username]}>`
        );
      }
    });

    let message =
      `📝 *Comment Notification - ${projectName}*\n` +
      `📌 *Issue:* <${issueURL}|${issueTitle} (#${issueNumber})> in <${repoURL}|${repo}>\n` +
      `👤 *Comment by:* ${slackMention}\n` +
      `✍️ *Comment:* ${modifiedComment}\n` +
      `🔗 [View Comment](${commentURL})`;

    if (payload.action === "edited" && previousComment) {
      message += `\n✏️ *Previous Comment:* ${previousComment}`;
    }

    const thread = await getThreadByRepoAndIssue(repo, issueNumber);

    if (thread) {
      // Post in thread
      await postToSlack({ text: message, thread_ts: thread.slackTs });
    } else {
      // First message → create thread (failsafe)
      const newTs = await postToSlack({ text: message });
      await saveOrUpdateThread({
        repo,
        issueNumber,
        slackTs: newTs,
        channelId: SLACK_CHANNEL_ID,
      });
    }
  }
}

module.exports = {
  sendSlackNotification,
  getThreadBySlackTs,
  getInstallationIdForRepo,
  slackToGitHubMapping,
};
